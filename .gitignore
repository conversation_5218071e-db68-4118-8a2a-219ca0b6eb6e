# Dependencies
node_modules/
/.pnp
.pnp.js
yarn.lock
package-lock.json

# Testing
/coverage

# Production
/apps/web/.next
/build
/dist
/.next/
/out/

# Environment Variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
.env*.local

# Debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# IDEs and editors
/.idea
.project
.classpath
.c9/
*.launch
.settings/
*.sublime-workspace
.vscode/*

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Convex
convex/_generated
.convex/

# Misc
*.log
*.csv
*.dat
*.out
*.pid
*.seed
*.pid.lock
.lock-wscript

# Turborepo
.turbo 
.vercel
