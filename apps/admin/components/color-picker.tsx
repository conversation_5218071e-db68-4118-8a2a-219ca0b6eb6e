'use client';

import { forwardRef, useMemo, useState, useEffect } from 'react';
import { HexColorPicker } from 'react-colorful';
import { cn } from '@workspace/ui/lib/utils';
import { useForwardedRef } from '@/hooks/use-forward-ref';
import { Button } from '@workspace/ui/components/button';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@workspace/ui/components/popover';
import { Input } from '@workspace/ui/components/input';
import { ButtonProps } from 'react-day-picker';

interface ColorPickerProps {
  value: string;
  onChange: (value: string) => void;
  onBlur?: () => void;
  onSave?: () => void;
  onCancel?: () => void;
}

const ColorPicker = forwardRef<
  HTMLInputElement,
  Omit<ButtonProps, 'value' | 'onChange' | 'onBlur'> & ColorPickerProps
>(
  (
    { disabled, value, onChange, onBlur, name, className, onSave, onCancel, ...props },
    forwardedRef
  ) => {
    const ref = useForwardedRef(forwardedRef);
    const [open, setOpen] = useState(false);
    const [pendingColor, setPendingColor] = useState(value || '#FFFFFF');
    const [lastSavedColor, setLastSavedColor] = useState(value || '#FFFFFF');

    useEffect(() => {
      setPendingColor(value || '#FFFFFF');
      setLastSavedColor(value || '#FFFFFF');
    }, [value]);

    const parsedValue = useMemo(() => {
      return pendingColor || '#FFFFFF';
    }, [pendingColor]);

    const handleSave = () => {
      onChange(pendingColor);
      setLastSavedColor(pendingColor);
      onSave?.();
      setOpen(false);
    };

    const handleCancel = () => {
      setPendingColor(lastSavedColor);
      onCancel?.();
      setOpen(false);
    };

    return (
      <Popover onOpenChange={setOpen} open={open}>
        <PopoverTrigger asChild disabled={disabled} onBlur={onBlur}>
          <Button
            {...props}
            className={cn('block', className)}
            name={name}
            onClick={() => {
              setPendingColor(value || '#FFFFFF');
              setLastSavedColor(value || '#FFFFFF');
              setOpen(true);
            }}
            size='icon'
            style={{
              backgroundColor: value || '#FFFFFF',
            }}
            variant='outline'
          >
            <div />
          </Button>
        </PopoverTrigger>
        <PopoverContent className='w-full'>
          <HexColorPicker color={parsedValue} onChange={setPendingColor} className="border" />
          <Input
            maxLength={7}
            onChange={(e) => {
              setPendingColor(e?.currentTarget?.value);
            }}
            ref={ref}
            value={parsedValue}
            className="mt-2"
          />
          <div className="flex gap-2 mt-2 justify-end">
            <Button size="xs" variant="secondary" onClick={handleSave}>
              Save
            </Button>
            <Button size="xs" variant="ghost" onClick={handleCancel}>
              Cancel
            </Button>
          </div>
        </PopoverContent>
      </Popover>
    );
  }
);
ColorPicker.displayName = 'ColorPicker';

export { ColorPicker };