import { type Icon } from "@tabler/icons-react";
import { usePathname } from "next/navigation";
import { Preloaded } from "convex/react";
import { api } from "@workspace/backend/convex/_generated/api";

import {
  SidebarGroup,
  SidebarGroupContent,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "@workspace/ui/components/sidebar";
import { Notifications } from "@/components/notifications";
import { AppRouterInstance } from "next/dist/shared/lib/app-router-context.shared-runtime";

export function NavMain({
  items,
  router,
  preloadedUser,
}: {
  items: {
    title: string;
    url: string;
    icon?: Icon;
    count?: number | string;
  }[];
  router: AppRouterInstance;
  preloadedUser: Preloaded<typeof api.users.viewer>;
}) {
  const pathname = usePathname();

  return (
    <SidebarGroup>
      <SidebarGroupContent className="flex flex-col gap-2">
        <SidebarMenu>
          <SidebarMenuItem>
            {/* <Notifications preloadedUser={preloadedUser} /> */}
          </SidebarMenuItem>
          {items.map((item) => (
            <SidebarMenuItem key={item.title}>
              <SidebarMenuButton
                tooltip={item.title}
                onClick={() => router.push(`/v/${item.url}`)}
                className={pathname?.includes(item.url) ? "bg-accent" : ""}
              >
                {item.icon && <item.icon className="stroke-1" />}
                <span>{item.title}</span>
                {item.count && item.count !== 0 && (
                  <span className="inline-flex items-center justify-center gap-1 px-1 py-0 ml-auto rounded-sm text-[12px] font-medium leading-4 tracking-[-0.02em] text-[#EEEFF1] bg-[#266DF0] tabular-nums select-none cursor-pointer">
                    {item.count}
                  </span>
                )}
              </SidebarMenuButton>
            </SidebarMenuItem>
          ))}
        </SidebarMenu>
      </SidebarGroupContent>
    </SidebarGroup>
  );
}
