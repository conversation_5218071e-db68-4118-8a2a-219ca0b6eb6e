"use client";

import * as React from "react";
import { Button } from "@workspace/ui/components/button";
import { ShortcutDisplay } from "./shortcut-display";

interface CommandButtonProps {
  setOpen: (open: boolean) => void;
}

export function CommandButton({ setOpen }: CommandButtonProps) {
  return (
    <Button
      variant="outline"
      className="relative h-9 w-full justify-start text-sm text-muted-foreground sm:pr-12 md:w-40 lg:w-64"
      onClick={() => setOpen(true)}
    >
      <span className="hidden lg:inline-flex">Quick actions...</span>
      <span className="inline-flex lg:hidden">Search...</span>
      <kbd className="absolute right-1 top-[50%] translate-y-[-50%] px-1.5 py-0.5 text-xs font-medium bg-zinc-200 dark:bg-zinc-800 text-zinc-400 rounded-sm border border-zinc-100 dark:border-zinc-700">
        <ShortcutDisplay />K
      </kbd>
    </Button>
  );
}
