"use client";

import Link from "next/link";
import { usePathname } from "next/navigation";

import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@workspace/ui/components/breadcrumb";

export function BreadcrumbNav() {
  const pathname = usePathname();

  if (pathname === "/") return null;

  const segments = pathname.split("/").filter(Boolean);

  const processedSegments = segments.reduce<string[]>((acc, segment, index) => {
    if ((segment === "v" || segment === "c") && segments[index + 1]) {
      acc.push(`${segment}/${segments[index + 1]}`);
      segments[index + 1] = "";
    } else if (segment !== "") {
      acc.push(segment);
    }
    return acc;
  }, []);

  const breadcrumbElements = processedSegments.reduce<React.ReactNode[]>(
    (acc, segment, index) => {
      const isLast = index === processedSegments.length - 1;

      let href = "/";
      if (segment.startsWith("v/") || segment.startsWith("c/")) {
        href += segment;
      } else {
        href += processedSegments.slice(0, index + 1).join("/");
      }

      let displayText = segment;
      if (segment.startsWith("v/")) {
        displayText = segment.replace("v/", "");
      } else if (segment.startsWith("c/")) {
        displayText = segment.replace("c/", "");
      }

      acc.push(
        <BreadcrumbItem key={`item-${segment}`}>
          {!isLast ? (
            <BreadcrumbLink asChild>
              <Link href={href}>{displayText}</Link>
            </BreadcrumbLink>
          ) : (
            <BreadcrumbPage>{displayText}</BreadcrumbPage>
          )}
        </BreadcrumbItem>,
      );

      if (!isLast) {
        acc.push(<BreadcrumbSeparator key={`separator-${index}-${segment}`} />);
      }

      return acc;
    },
    [],
  );

  return (
    <Breadcrumb className="hidden md:block">
      <BreadcrumbList>{breadcrumbElements}</BreadcrumbList>
    </Breadcrumb>
  );
}
