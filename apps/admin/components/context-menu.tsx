import React from "react";
import {
  ContextMenu as BaseContextMenu,
  ContextMenuContent,
  ContextMenuItem,
  ContextMenuSeparator,
  ContextMenuSub,
  ContextMenuSubContent,
  ContextMenuSubTrigger,
  ContextMenuTrigger,
} from "@workspace/ui/components/context-menu";

export interface ContextMenuItemType {
  id: string;
  label: string;
  icon?: React.ElementType;
  iconClassName?: string;
  onClick?: (e: React.MouseEvent) => void;
  items?: ContextMenuItemType[];
  indicator?: {
    show: boolean;
    color?: string;
  };
}

interface ContextMenuProps {
  children: React.ReactNode;
  items: (ContextMenuItemType | "separator")[];
  contentClassName?: string;
}

export function ContextMenu({
  children,
  items,
  contentClassName = "w-64 dark:bg-zinc-900 bg-zinc-200",
}: ContextMenuProps) {
  const renderMenuItem = (item: ContextMenuItemType) => {
    if (item.items?.length) {
      return (
        <ContextMenuSub key={item.id}>
          <ContextMenuSubTrigger className="flex items-center cursor-pointer hover:bg-accent focus:bg-accent">
            <div className="flex items-center justify-between w-full">
              <div className="flex items-center">
                {item.icon &&
                  React.createElement(item.icon, {
                    className: `mr-2 h-4 w-4 ${item.iconClassName || ""}`,
                  })}
                <span>{item.label}</span>
              </div>
              {item.indicator?.show && (
                <div
                  className={`ml-2 h-2 w-2 rounded-full ${item.indicator.color || ""}`}
                />
              )}
            </div>
          </ContextMenuSubTrigger>
          <ContextMenuSubContent className="min-w-[180px] dark:bg-zinc-900 bg-zinc-200">
            {item.items.map((subItem) => renderMenuItem(subItem))}
          </ContextMenuSubContent>
        </ContextMenuSub>
      );
    }

    return (
      <ContextMenuItem
        key={item.id}
        onClick={item.onClick}
        className="flex items-center cursor-pointer hover:bg-accent focus:bg-accent"
      >
        <div className="flex items-center justify-between w-full">
          <div className="flex items-center">
            {item.icon &&
              React.createElement(item.icon, {
                className: `mr-2 h-4 w-4 ${item.iconClassName || ""}`,
              })}
            <span>{item.label}</span>
          </div>
          {item.indicator?.show && (
            <div
              className={`ml-2 h-2 w-2 rounded-full ${item.indicator.color || ""}`}
            />
          )}
        </div>
      </ContextMenuItem>
    );
  };

  return (
    <BaseContextMenu>
      <ContextMenuTrigger asChild>{children}</ContextMenuTrigger>
      <ContextMenuContent className={contentClassName}>
        {items.map((item, index) =>
          item === "separator" ? (
            <ContextMenuSeparator key={`sep-${index}`} className="my-1" />
          ) : (
            renderMenuItem(item)
          ),
        )}
      </ContextMenuContent>
    </BaseContextMenu>
  );
}
