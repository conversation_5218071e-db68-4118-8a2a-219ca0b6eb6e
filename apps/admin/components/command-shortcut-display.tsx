"use client";

import { useHelp } from "../context/help-wrapper";
import { CommandShortcut } from "@workspace/ui/components/command";

interface CommandShortcutDisplayProps {
  shortcut: string;
}

export function CommandShortcutDisplay({
  shortcut,
}: CommandShortcutDisplayProps) {
  const { shortcutSymbol } = useHelp();
  const displayShortcut = shortcut.replace("⌘", shortcutSymbol);
  return <CommandShortcut>{displayShortcut}</CommandShortcut>;
}
