"use client";

import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@radix-ui/react-collapsible";
import {
  IconChevronDown,
  IconStarOff,
  IconSquareRoundedCheck,
  IconFolderPlus,
  IconFolder,
  IconFolderOpen,
  IconDotsVertical,
  type Icon,
  IconPencil,
  IconTrash,
} from "@tabler/icons-react";
import Link from "next/link";
import {
  DndContext,
  DragEndEvent,
  DragOverEvent,
  DragOverlay,
  DragStartEvent,
  PointerSensor,
  useSensor,
  useSensors,
} from "@dnd-kit/core";
import { SortableContext, useSortable, arrayMove } from "@dnd-kit/sortable";
import { useDroppable } from "@dnd-kit/core";
import React from "react";
import { motion, AnimatePresence } from "framer-motion";

import {
  SidebarGroup,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuAction,
  SidebarMenuButton,
  SidebarMenuItem,
} from "@workspace/ui/components/sidebar";
import { cn } from "@workspace/ui/lib/utils";
import { useState } from "react";
import { Avatar } from "@workspace/ui/components/avatar";
import { api } from "@workspace/backend/convex/_generated/api";
import { useMutation, useQuery } from "convex/react";
import { Id } from "@workspace/backend/convex/_generated/dataModel";
import { Input } from "@workspace/ui/components/input";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@workspace/ui/components/dropdown-menu";

interface FavoriteFolder {
  _id: Id<"favoriteFolders">;
  name: string;
  isOpen?: boolean;
  position?: number;
  favorites: Array<{
    _id: Id<"favorites">;
    objectId: Id<"tasks"> | Id<"contacts"> | Id<"users"> | Id<"feedback">;
    title?: string;
    folderId?: Id<"favoriteFolders">;
    position?: number;
    userId: Id<"users">;
    _creationTime: number;
  }>;
}

interface FavoriteItem {
  _id: Id<"favorites">;
  name: string;
  icon: Icon;
  isActive?: boolean;
  folderId?: Id<"favoriteFolders">;
  position?: number;
  objectId: Id<"tasks"> | Id<"contacts"> | Id<"users"> | Id<"feedback">;
  tableName?: string;
}

interface DroppableProps {
  id: string;
  children: React.ReactNode;
  isFolder?: boolean;
}

const Droppable = ({ id, children, isFolder }: DroppableProps) => {
  const { setNodeRef, isOver, active } = useDroppable({
    id,
    data: {
      type: isFolder ? "folder" : "root",
      accepts: ["favorite", "folder"],
    },
  });

  const isDropZone =
    id.toString().startsWith("before-") || id.toString().startsWith("after-");
  const isValidDrop =
    isOver &&
    (isDropZone
      ? active?.data?.current?.type === "favorite"
      : isFolder
        ? active?.data?.current?.type === "favorite"
        : active?.data?.current?.type === active?.data?.current?.type);

  return (
    <div
      ref={setNodeRef}
      className={cn(
        "relative transition-colors rounded-lg border border-transparent",
        isValidDrop &&
          active?.data?.current?.type === "favorite" &&
          !isDropZone &&
          "bg-blue-500/10 border border-blue-500",
        isValidDrop &&
          active?.data?.current?.type === "folder" &&
          !isDropZone &&
          "bg-zinc-800/50",
        isValidDrop && isDropZone && "bg-blue-500/20",
      )}
    >
      {children}
    </div>
  );
};

const DraggableItem = ({
  item,
  children,
}: {
  item: FavoriteItem;
  children: React.ReactNode;
}) => {
  const { attributes, listeners, setNodeRef, isDragging } = useSortable({
    id: item._id,
    data: {
      type: "favorite",
      item,
    },
  });

  return (
    <div
      ref={setNodeRef}
      {...attributes}
      {...listeners}
      className={cn("transition-opacity", isDragging && "opacity-50")}
    >
      {children}
    </div>
  );
};

const DraggableFolder = ({
  folder,
  children,
}: {
  folder: FavoriteFolder;
  children: React.ReactNode;
}) => {
  const { attributes, listeners, setNodeRef, isDragging } = useSortable({
    id: folder._id,
    data: {
      type: "folder",
      item: folder,
    },
  });

  return (
    <div
      ref={setNodeRef}
      {...attributes}
      {...listeners}
      className={cn("transition-opacity", isDragging && "opacity-50")}
    >
      {children}
    </div>
  );
};

export function NavFavorites({ items }: { items: FavoriteItem[] }) {
  const toggleFavorite = useMutation(api.favorites.toggle);
  const updateItem = useMutation(api.favorites.updateItem);

  const createFolder = useMutation(api.favorites.createFolder);
  const updateFolder = useMutation(api.favorites.updateFolder);
  const deleteFolder = useMutation(api.favorites.deleteFolder);
  const folders = useQuery(api.favorites.getFolders) || [];

  const [hoveredItem, setHoveredItem] = useState<string | null>(null);
  const [hoveredFolder, setHoveredFolder] =
    useState<Id<"favoriteFolders"> | null>(null);
  const [isCreatingFolder, setIsCreatingFolder] = useState(false);
  const [newFolderName, setNewFolderName] = useState("");
  const [isFavoritesHovered, setIsFavoritesHovered] = useState(false);
  const [, setActiveId] = useState<string | null>(null);
  const [editingFolder, setEditingFolder] = useState<{
    id: Id<"favoriteFolders">;
    name: string;
  } | null>(null);
  const [draggedItem, setDraggedItem] = useState<
    FavoriteItem | FavoriteFolder | null
  >(null);

  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8,
      },
    }),
  );

  const handleCreateFolder = async () => {
    if (!isCreatingFolder) {
      setIsCreatingFolder(true);
      return;
    }

    if (newFolderName.trim()) {
      await createFolder({ name: newFolderName });
      setNewFolderName("");
      setIsCreatingFolder(false);
    }
  };

  const handleFolderClick = async (folder: FavoriteFolder) => {
    await updateFolder({
      folderId: folder._id as Id<"favoriteFolders">,
      isOpen: !folder.isOpen,
    });
  };

  const handleDragStart = (event: DragStartEvent) => {
    const { active } = event;
    setActiveId(active.id as string);
    setDraggedItem(active.data.current?.item);
  };

  const sortedFolders = [...folders].sort(
    (a, b) => (a.position || 0) - (b.position || 0),
  );

  const rootItems = items
    .filter((item) => !item.folderId)
    .sort((a, b) => (a.position || 0) - (b.position || 0));

  const folderItems = new Map<Id<"favoriteFolders">, FavoriteItem[]>();

  folders.forEach((folder) => {
    const items = folder.favorites.map((favorite) => ({
      _id: favorite._id,
      name: favorite.title || "Untitled",
      icon: IconSquareRoundedCheck,
      isActive: false,
      folderId: favorite.folderId,
      position: favorite.position,
      objectId: favorite.objectId,
    }));
    folderItems.set(folder._id, items as FavoriteItem[]);
  });

  const handleDragEnd = async (event: DragEndEvent) => {
    const { active, over } = event;

    if (!over) {
      setActiveId(null);
      setDraggedItem(null);
      return;
    }

    const activeData = active.data.current as {
      type: "favorite" | "folder";
      item: FavoriteItem | FavoriteFolder;
    };
    const overData = over.data.current as {
      type: "folder" | "root";
      accepts: string[];
    };

    if (activeData.type === "favorite") {
      const favoriteItem = activeData.item as FavoriteItem;

      if (!favoriteItem._id) {
        setActiveId(null);
        setDraggedItem(null);
        return;
      }

      try {
        if (overData.type === "folder") {
          const folderId = over.id.toString().startsWith("folder-content-")
            ? (over.id
                .toString()
                .replace("folder-content-", "") as Id<"favoriteFolders">)
            : (over.id as Id<"favoriteFolders">);

          let newPosition = 1000;
          const targetItems = folderItems.get(folderId) || [];

          if (targetItems.length > 0) {
            newPosition =
              Math.max(...targetItems.map((i) => i.position || 0)) + 1000;
          }

          await updateItem({
            itemId: favoriteItem._id,
            folderId,
            position: newPosition,
          });
        } else {
          const overId = over.id.toString();
          if (overId.startsWith("before-") || overId.startsWith("after-")) {
            const targetFolderId = overId.replace(/^(before|after)-/, "");
            const targetFolder = folders.find((f) => f._id === targetFolderId);

            if (targetFolder) {
              const isBeforeFolder = overId.startsWith("before-");
              const folderPosition = targetFolder.position || 0;

              const newPosition = isBeforeFolder
                ? folderPosition - 500
                : folderPosition + 500;

              await updateItem({
                itemId: favoriteItem._id,
                folderId: undefined,
                position: newPosition,
              });
            }
          } else {
            let newPosition = 1000;
            if (rootItems.length > 0) {
              newPosition =
                Math.max(...rootItems.map((i) => i.position || 0)) + 1000;
            }

            await updateItem({
              itemId: favoriteItem._id,
              folderId: undefined,
              position: newPosition,
            });
          }
        }
      } catch (error) {
        console.error("Error handling favorite:", error);
      }
    }

    setActiveId(null);
    setDraggedItem(null);
  };

  return (
    <DndContext
      sensors={sensors}
      onDragStart={handleDragStart}
      onDragEnd={handleDragEnd}
    >
      <Collapsible defaultOpen className="group/collapsible">
        <SidebarGroup>
          <SidebarGroupLabel
            className="group-data-[collapsible=icon]:opacity-100 !p-0"
            onMouseEnter={() => setIsFavoritesHovered(true)}
            onMouseLeave={() => setIsFavoritesHovered(false)}
          >
            <CollapsibleTrigger
              className={cn(
                "h-8 flex items-center px-2 rounded-md hover:bg-zinc-800/50 w-full",
              )}
            >
              <div className="flex items-center justify-between w-full">
                <div className="flex items-center gap-2">
                  <IconChevronDown className="h-4 w-4 transition-transform group-data-[state=open]/collapsible:rotate-180" />
                  <span className="text-sm">Favorites</span>
                  {items.length > 0 && (
                    <span className="text-xs bg-blue-500/20 text-blue-400 px-1.5 py-0.5 rounded-full">
                      {items.length}
                    </span>
                  )}
                </div>
                {isFavoritesHovered && (
                  <IconFolderPlus
                    className="h-4 w-4 text-muted-foreground hover:text-accent-foreground cursor-pointer"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleCreateFolder();
                    }}
                  />
                )}
              </div>
            </CollapsibleTrigger>
          </SidebarGroupLabel>

          {items.length > 0 && (
            <div className="px-2 py-1 text-xs text-blue-400 bg-blue-500/10 rounded mx-2 my-1 hidden">
              Debug: {items.length} favorites: {items.map(i => i.objectId.__tableName).join(', ')}
            </div>
          )}

          <CollapsibleContent className="overflow-hidden data-[state=open]:animate-collapsible-down data-[state=closed]:animate-collapsible-up">
            <SidebarMenu>
              <AnimatePresence>
                <>
                  {isCreatingFolder && (
                    <motion.div
                      key="create-folder-input"
                      initial={{ opacity: 0, height: 0 }}
                      animate={{ opacity: 1, height: "auto" }}
                      exit={{ opacity: 0, height: 0 }}
                      transition={{ duration: 0.2, ease: "easeInOut" }}
                      className="overflow-hidden"
                    >
                      <div className="px-2 py-1">
                        <Input
                          value={newFolderName}
                          onChange={(e) => setNewFolderName(e.target.value)}
                          onKeyDown={(e) => {
                            if (e.key === "Enter") {
                              handleCreateFolder();
                            }

                            if (e.key === "Escape") {
                              setIsCreatingFolder(false);
                            }
                          }}
                          onBlur={() => setIsCreatingFolder(false)}
                          leftIcon={<IconFolder />}
                          placeholder="Folder name"
                          className="h-7 text-sm border-blue-500"
                          autoFocus
                        />
                      </div>
                    </motion.div>
                  )}

                  {items.length < 1 && folders.length < 1 && (
                    <motion.span
                      key="no-favorites"
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      exit={{ opacity: 0 }}
                      transition={{ duration: 0.2 }}
                      className={cn(
                        "text-xs cursor-default text-foreground/50 pl-3",
                        "group-data-[collapsible=icon]:hidden",
                      )}
                    >
                      No favorites yet
                    </motion.span>
                  )}
                </>
              </AnimatePresence>

              <Droppable id="root">
                <div className="space-y-1">
                  <SortableContext
                    items={[
                      ...sortedFolders.map((f) => f._id),
                      ...rootItems.map((i) => i._id),
                    ]}
                  >
                    {sortedFolders.map((folder) => (
                      <DraggableFolder
                        key={folder._id}
                        folder={folder as FavoriteFolder}
                      >
                        <div className="relative">
                          <Droppable id={`before-${folder._id}`}>
                            <div
                              className={cn(
                                "absolute w-full h-2 -top-1 z-10",
                                "transition-all duration-200",
                              )}
                            />
                          </Droppable>

                          <Droppable id={folder._id} isFolder>
                            <div>
                              <div
                                className="flex items-center rounded-md justify-between px-2 py-1 cursor-pointer hover:bg-zinc-800/50 relative group/folder"
                                onMouseEnter={() =>
                                  setHoveredFolder(folder._id)
                                }
                                onMouseLeave={() => setHoveredFolder(null)}
                              >
                                <div
                                  className="flex items-center flex-1 gap-2"
                                  onClick={() =>
                                    handleFolderClick(folder as FavoriteFolder)
                                  }
                                >
                                  <div className="relative w-4 h-4">
                                    <IconFolderOpen
                                      className={cn(
                                        "absolute h-4 w-4 stroke-1 transition-all duration-300 ease-in-out transform",
                                        folder.isOpen
                                          ? "opacity-100 scale-100"
                                          : "opacity-0 scale-95",
                                      )}
                                    />
                                    <IconFolder
                                      className={cn(
                                        "absolute h-4 w-4 stroke-1 transition-all duration-300 ease-in-out transform",
                                        folder.isOpen
                                          ? "opacity-0 scale-95"
                                          : "opacity-100 scale-100",
                                      )}
                                    />
                                  </div>
                                  {editingFolder?.id === folder._id ? (
                                    <Input
                                      value={editingFolder.name}
                                      onChange={(e) =>
                                        setEditingFolder({
                                          ...editingFolder,
                                          name: e.target.value,
                                        })
                                      }
                                      onKeyDown={(e) => {
                                        if (e.key === "Enter") {
                                          updateFolder({
                                            folderId: folder._id,
                                            name: editingFolder.name,
                                          });
                                          setEditingFolder(null);
                                        }
                                      }}
                                      onBlur={() => {
                                        updateFolder({
                                          folderId: folder._id,
                                          name: editingFolder.name,
                                        });
                                        setEditingFolder(null);
                                      }}
                                      className="h-6 text-sm"
                                      autoFocus
                                    />
                                  ) : (
                                    <span className="text-sm font-medium">
                                      {folder.name}
                                    </span>
                                  )}
                                </div>

                                <DropdownMenu>
                                  <DropdownMenuTrigger asChild>
                                    <IconDotsVertical
                                      className={cn(
                                        "h-4 w-4 opacity-0 group-hover/folder:opacity-100 transition-opacity text-muted-foreground hover:text-accent-foreground",
                                        hoveredFolder === folder._id &&
                                          "opacity-100",
                                      )}
                                      onClick={(e) => e.stopPropagation()}
                                    />
                                  </DropdownMenuTrigger>
                                  <DropdownMenuContent align="start">
                                    <DropdownMenuItem
                                      onClick={() =>
                                        setEditingFolder({
                                          id: folder._id,
                                          name: folder.name,
                                        })
                                      }
                                    >
                                      <IconPencil className="h-4 w-4" />
                                      Rename
                                    </DropdownMenuItem>
                                    <DropdownMenuItem
                                      className="text-red-500 hover:!text-red-500"
                                      onClick={() =>
                                        deleteFolder({ folderId: folder._id })
                                      }
                                    >
                                      <IconTrash className="h-4 w-4 text-red-500" />
                                      Delete folder
                                    </DropdownMenuItem>
                                  </DropdownMenuContent>
                                </DropdownMenu>
                              </div>

                              <AnimatePresence>
                                {folder.isOpen && (
                                  <motion.div
                                    initial={{ height: 0, opacity: 0 }}
                                    animate={{ height: "auto", opacity: 1 }}
                                    exit={{ height: 0, opacity: 0 }}
                                    transition={{
                                      duration: 0.2,
                                      ease: "easeInOut",
                                    }}
                                    className="overflow-hidden"
                                  >
                                    <Droppable
                                      id={`folder-content-${folder._id}`}
                                      isFolder
                                    >
                                      <div className="pl-6">
                                        {(folderItems.get(folder._id) || [])
                                          .length > 0 && (
                                          <SortableContext
                                            items={(
                                              folderItems.get(folder._id) || []
                                            ).map((i) => i._id)}
                                          >
                                            {(
                                              folderItems.get(folder._id) || []
                                            ).map((item) => (
                                              <DraggableItem
                                                key={item._id}
                                                item={item}
                                              >
                                                <FavoriteItemContent
                                                  item={item}
                                                  onHover={setHoveredItem}
                                                  isHovered={
                                                    hoveredItem === item._id
                                                  }
                                                  toggleFavorite={
                                                    toggleFavorite
                                                  }
                                                  updateItem={updateItem}
                                                />
                                              </DraggableItem>
                                            ))}
                                          </SortableContext>
                                        )}
                                      </div>
                                    </Droppable>
                                  </motion.div>
                                )}
                              </AnimatePresence>
                            </div>
                          </Droppable>

                          <Droppable id={`after-${folder._id}`}>
                            <div
                              className={cn(
                                "absolute w-full h-2 -bottom-1 z-10",
                                "transition-all duration-200",
                              )}
                            />
                          </Droppable>
                        </div>

                        <AnimatePresence>
                          {(folderItems.get(folder._id) || []).length === 0 &&
                            folder.isOpen && (
                              <motion.div
                                initial={{ height: 0, opacity: 0 }}
                                animate={{ height: "auto", opacity: 1 }}
                                exit={{ height: 0, opacity: 0 }}
                                transition={{
                                  duration: 0.2,
                                  ease: "easeInOut",
                                }}
                                className="overflow-hidden"
                              >
                                <div className="text-xs text-muted-foreground pl-8 px-2">
                                  No items
                                </div>
                              </motion.div>
                            )}
                        </AnimatePresence>
                      </DraggableFolder>
                    ))}

                    {rootItems.map((item) => (
                      <DraggableItem key={item._id} item={item}>
                        <FavoriteItemContent
                          item={item}
                          onHover={setHoveredItem}
                          isHovered={hoveredItem === item._id}
                          toggleFavorite={toggleFavorite}
                          updateItem={updateItem}
                        />
                      </DraggableItem>
                    ))}
                  </SortableContext>
                </div>
              </Droppable>

              <DragOverlay>
                {draggedItem && (
                  <div className="opacity-80 bg-zinc-800 rounded-md shadow-lg border border-zinc-700">
                    {draggedItem.hasOwnProperty("_id") ? (
                      <FavoriteItemContent
                        item={draggedItem as FavoriteItem}
                        onHover={() => {}}
                        isHovered={false}
                        toggleFavorite={() => {}}
                        updateItem={() => {}}
                      />
                    ) : (
                      <div className="flex items-center gap-2 px-2 py-1">
                        <IconFolder className="h-4 w-4 text-blue-500" />
                        <span className="text-sm">
                          {(draggedItem as FavoriteFolder).name}
                        </span>
                      </div>
                    )}
                  </div>
                )}
              </DragOverlay>
            </SidebarMenu>
          </CollapsibleContent>
        </SidebarGroup>
      </Collapsible>
    </DndContext>
  );
}

const FavoriteItemContent = ({
  item,
  onHover,
  isHovered,
  toggleFavorite,
  updateItem,
}: {
  item: FavoriteItem;
  onHover: (id: string | null) => void;
  isHovered: boolean;
  toggleFavorite: any;
  updateItem: any;
}) => (
  <SidebarMenuItem
    onMouseEnter={() => onHover(item._id)}
    onMouseLeave={() => onHover(null)}
    className="hover:bg-zinc-800/50 rounded-md"
  >
    <SidebarMenuButton tooltip={item.name} asChild>
      <Link 
        href={`/v/${item.tableName || "users"}/${item.objectId}`}
        className="group"
      >
        <Avatar className="h-4 w-4 rounded-md">
          <div className="h-full w-full bg-zinc-100 dark:bg-zinc-800 rounded-md flex items-center justify-center">
            {React.createElement(item.icon, { className: "h-4 w-4" })}
          </div>
        </Avatar>
        <span className="text-sm">{item.name || "Unnamed"}</span>
        <span className="ml-1 text-xs text-muted-foreground opacity-0 group-hover:opacity-100">
          ({item.tableName || "users"})
        </span>
      </Link>
    </SidebarMenuButton>
    {isHovered && (
      <SidebarMenuAction
        onClick={(e) => {
          e.preventDefault();

          if (item.objectId) {
            toggleFavorite({
              objectId: item.objectId,
            });
          }
        }}
      >
        <IconStarOff className="text-muted-foreground hover:text-accent-foreground h-4 w-4" />
      </SidebarMenuAction>
    )}
  </SidebarMenuItem>
);
