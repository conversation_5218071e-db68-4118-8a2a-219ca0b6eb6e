"use client";

import * as React from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON>Title,
  DialogDescription,
} from "@workspace/ui/components/dialog";
import { <PERSON><PERSON> } from "@workspace/ui/components/button";
import { Input } from "@workspace/ui/components/input";
import {
  Pop<PERSON>,
  <PERSON>over<PERSON>ontent,
  PopoverTrigger,
} from "@workspace/ui/components/popover";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@workspace/ui/components/command";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { Textarea } from "@workspace/ui/components/textarea";
import { cn } from "@workspace/ui/lib/utils";
import { FEEDBACK_STATUS, FEEDBACK_TYPE } from "@/lib/constants";
import {
  IconSquareRoundedCheck,
} from "@tabler/icons-react";
import { useMutation, useQuery } from "convex/react";
import { api } from "@workspace/backend/convex/_generated/api";
import { CloseButton } from "../close-button";
import { Id } from "@workspace/backend/convex/_generated/dataModel";
import { FeedbackStatus, FeedbackType } from "@workspace/backend/convex/lib/types";

export interface FeedbackToEdit {
  _id: Id<"feedback">;
  message: string;
  notes?: string;
  status: FeedbackStatus;
  type: FeedbackType;
  position?: number;
  metadata?: {
    browser?: string;
    device?: string;
    os?: string;
  };
  createdAt?: number;
  updatedAt?: number;
  userId?: Id<"users">;
  updatedBy?: Id<"users">;
  statusHistory?: Array<{
    status: string;
    timestamp: number;
    userId: Id<"users">;
  }>;
  url?: string;
  _creationTime?: number;
}

interface CreateFeedbackModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  feedbackToEdit?: FeedbackToEdit;
  defaultStatus?: FeedbackStatus;
}

const feedbackSchema = z.object({
  message: z.string().min(1, "Message is required"),
  notes: z.string().optional(),
  status: z.enum(["new", "in_progress", "done", "rejected"]).default("new"),
  type: z.enum(["bug", "feature_request", "other"]).default("bug"),
});

type FeedbackFormData = z.infer<typeof feedbackSchema>;

const defaultFormValues: FeedbackFormData = {
  message: "",
  notes: "",
  status: "new",
  type: "bug",
};

export function CreateFeedbackModal({
  open,
  onOpenChange,
  feedbackToEdit,
  defaultStatus,
}: CreateFeedbackModalProps) {
  const submitFeedback = useMutation(api.feedback.submitFeedback);
  const updateBatch = useMutation(api.feedback.updateBatchFeedbacks);
  const updateStatus = useMutation(api.feedback.updateFeedbackStatus);

  const [statusOpen, setStatusOpen] = React.useState(false);
  const [typeOpen, setTypeOpen] = React.useState(false);
  const [showFullData, setShowFullData] = React.useState(false);

  const form = useForm<FeedbackFormData>({
    resolver: zodResolver(feedbackSchema),
    defaultValues: defaultFormValues,
  });

  React.useEffect(() => {
    if (!open) {
      form.reset(defaultFormValues);
      return;
    }

    if (feedbackToEdit) {
      form.reset({
        message: feedbackToEdit.message,
        notes: feedbackToEdit.notes || "",
        status: feedbackToEdit.status as FeedbackStatus,
        type: feedbackToEdit.type,
      });
    } else {
      form.reset({
        ...defaultFormValues,
        status: defaultStatus || defaultFormValues.status,
      });
    }
  }, [open, feedbackToEdit, form, defaultStatus]);

  const onSubmit = async (formData: FeedbackFormData) => {
    try {
      if (feedbackToEdit) {
        if (feedbackToEdit.status !== formData.status) {
          await updateStatus({
            feedbackId: feedbackToEdit._id,
            status: formData.status,
            notes: formData.notes,
          });
        } 
        
        await updateBatch({
          feedbackIds: [feedbackToEdit._id],
          type: formData.type,
        });
          
        onOpenChange(false);
      } else {
        const result = await submitFeedback({
          message: formData.message,
          type: formData.type === "other" ? "feature_request" : formData.type,
          metadata: {
            browser: navigator.userAgent || "",
          }
        });

        if (result) {
          if (formData.status !== "new") {
            await updateStatus({
              feedbackId: result as any,
              status: formData.status,
              notes: formData.notes,
            });
          }
          
          onOpenChange(false);
        }
      }
    } catch (error) {
      console.error("Failed to save feedback:", error);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent
        close={false}
        className="sm:max-w-3xl p-0 m-0"
        onPointerDownOutside={(e) => e.preventDefault()}
      >
        <DialogTitle className="sr-only">
          {feedbackToEdit ? "Edit Feedback" : "Create Feedback"}
        </DialogTitle>
        <DialogDescription className="sr-only">
          {feedbackToEdit ? "Edit existing feedback" : "Create new feedback"}
        </DialogDescription>

        <div className="flex items-center justify-between p-2 px-4">
          <div className="flex flex-row items-center gap-x-2">
            <IconSquareRoundedCheck className="h-5 w-5" />
            <span className="text-md font-light">
              {feedbackToEdit ? "Edit Feedback" : "Create Feedback"}
            </span>
          </div>
          <div className="flex items-center gap-2">
            {feedbackToEdit && (
              <Button 
                type="button" 
                variant="outline" 
                size="sm"
                onClick={() => setShowFullData(!showFullData)}
              >
                {showFullData ? "Hide Details" : "Show Details"}
              </Button>
            )}
            <CloseButton onClick={() => onOpenChange(false)} />
          </div>
        </div>

        {feedbackToEdit && showFullData && (
          <div className="p-4 border-t border-input overflow-auto max-h-80 text-sm font-mono">
            <pre className="whitespace-pre-wrap">
              {JSON.stringify(feedbackToEdit, null, 2)}
            </pre>
          </div>
        )}

        <form
          onSubmit={form.handleSubmit(onSubmit)}
          className="flex flex-col w-full border-t border-input -mt-4"
        >
          <div className="flex flex-col p-2 dark:bg-input/30">
            <Input
              {...form.register("message")}
              autoFocus
              className="shadow-none selection:bg-transparent dark:bg-transparent bg-transparent file:bg-transparent ring-0 focus-visible:ring-0 focus-visible:ring-offset-0 w-full font-semibold border-none rounded-none text-xl md:text-xl"
              placeholder="Feedback message"
            />
            {form.formState.errors.message && (
              <span className="text-red-500 text-sm">
                {form.formState.errors.message.message}
              </span>
            )}
            <Textarea
              {...form.register("notes")}
              className="shadow-none ring-0 focus-visible:ring-0 focus-visible:ring-offset-0 w-full border-none rounded-none p-3 bg-transparent dark:bg-transparent text-muted-foreground text-md md:text-md"
              placeholder="Add notes..."
              style={{ resize: "none" }}
            />
          </div>

          <div className="flex flex-row items-center p-2 justify-between border-t border-input">
            <div className="flex flex-row items-center gap-x-2">
              {/* Status */}
              <Popover open={statusOpen} onOpenChange={setStatusOpen}>
                <PopoverTrigger asChild>
                  <Button type="button" variant="small" size="sm">
                    <div className="flex items-center gap-2">
                      <span>
                        {FEEDBACK_STATUS.find(
                          (st) => st.value === form.watch("status")
                        )?.label || "Status"}
                      </span>
                    </div>
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-fit p-0">
                  <Command>
                    <CommandInput placeholder="Search status..." />
                    <CommandList>
                      <CommandEmpty>No status found.</CommandEmpty>
                      <CommandGroup>
                        {FEEDBACK_STATUS.map((st) => (
                          <CommandItem
                            key={st.value}
                            value={st.value}
                            onSelect={() => {
                              form.setValue(
                                "status",
                                st.value as FeedbackFormData["status"]
                              );
                              setStatusOpen(false);
                            }}
                          >
                            {st.label}
                          </CommandItem>
                        ))}
                      </CommandGroup>
                    </CommandList>
                  </Command>
                </PopoverContent>
              </Popover>

              {/* Type */}
              <Popover open={typeOpen} onOpenChange={setTypeOpen}>
                <PopoverTrigger asChild>
                  <Button type="button" variant="small" size="sm">
                    <div className="flex items-center gap-2">
                      <span>
                        {FEEDBACK_TYPE.find(
                          (t) => t.value === form.watch("type")
                        )?.label || "Type"}
                      </span>
                    </div>
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-fit p-0">
                  <Command>
                    <CommandInput placeholder="Search type..." />
                    <CommandList>
                      <CommandEmpty>No type found.</CommandEmpty>
                      <CommandGroup>
                        {FEEDBACK_TYPE.map((t) => (
                          <CommandItem
                            key={t.value}
                            value={t.value}
                            onSelect={() => {
                              form.setValue(
                                "type",
                                t.value as FeedbackFormData["type"]
                              );
                              setTypeOpen(false);
                            }}
                          >
                            {t.label}
                          </CommandItem>
                        ))}
                      </CommandGroup>
                    </CommandList>
                  </Command>
                </PopoverContent>
              </Popover>
            </div>

            <div className="flex items-center gap-2">
              <Button
                type="button"
                variant="ghost"
                size="sm"
                onClick={() => onOpenChange(false)}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                variant="primary"
                size="sm"
                disabled={form.formState.isSubmitting}
              >
                {feedbackToEdit ? "Update Feedback" : "Create Feedback"}
              </Button>
            </div>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
} 