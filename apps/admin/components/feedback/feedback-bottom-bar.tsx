import * as React from "react";

import type { Id } from "@workspace/backend/convex/_generated/dataModel";
import { But<PERSON> } from "@workspace/ui/components/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@workspace/ui/components/dropdown-menu";
import { FEEDBACK_TYPE, FEEDBACK_STATUS, TASK_PRIORITY, TASK_STATUS } from "@/lib/constants";
import { useMutation, usePreloadedQuery, useQuery } from "convex/react";
import { api } from "@workspace/backend/convex/_generated/api";
import { IconTrash, IconX } from "@tabler/icons-react";
import { Feedback, User } from "@workspace/backend/convex/lib/types";
import { usePreloadedData } from "@/hooks/use-preloaded-data";
import {
  Avatar,
  AvatarFallback,
  AvatarImage,
} from "@workspace/ui/components/avatar";
import {
  DropdownMenuRadioGroup,
  DropdownMenuRadioItem,
  DropdownMenuSub,
  DropdownMenuSubContent,
  DropdownMenuSubTrigger,
} from "@workspace/ui/components/dropdown-menu";

interface BottomBarProps {
  selectedFeedbacks?: Feedback[];
  onDeselectAll?: () => void;
}

const BottomBarFeedback = ({ selectedFeedbacks, onDeselectAll }: BottomBarProps) => {
  const updateBatchFeedbacks = useMutation(api.feedback.updateBatchFeedbacks);
  const { preloadedUser } = usePreloadedData();
  const user = usePreloadedQuery(preloadedUser) as User;

  if (!selectedFeedbacks || selectedFeedbacks.length === 0) return null;

  return (
    <div className="fixed bottom-5 left-1/2 transform -translate-x-1/2 app-bg/75 border border-muted rounded-xl shadow-lg p-1 pl-4 flex justify-between items-center w-11/12 max-w-3xl h-12 backdrop-blur-xl z-50">
      <div className="flex items-center">
        <span className="rounded-sm bg-zinc-100 dark:bg-zinc-900 px-1 py-0 text-xs font-medium border border-input font-mono mr-2">
          {selectedFeedbacks.length}
        </span>
        <span className="text-xs text-muted-foreground">selected</span>
      </div>
      <div className="flex items-center space-x-1">
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" className="rounded-lg" size="sm">
              Type
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent
            className="rounded-xl space-y-1 mb-4 -mr-4"
            align="end"
          >
            {FEEDBACK_TYPE.map((type) => (
              <DropdownMenuItem
                key={type.value}
                className="rounded-lg flex items-center"
                onClick={() => {
                  updateBatchFeedbacks({
                    feedbackIds: selectedFeedbacks?.map(
                      (feedback: Feedback) => feedback?._id,
                    ) as Id<"feedback">[],
                    type: type.value,
                  });
                }}
              >
                {type.label}
              </DropdownMenuItem>
            ))}
          </DropdownMenuContent>
        </DropdownMenu>

        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" className="rounded-lg" size="sm">
              Status
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent
            className="rounded-xl space-y-1 mb-4 -mr-4"
            align="end"
          >
            {FEEDBACK_STATUS.map((status) => (
              <DropdownMenuItem
                key={status.value}
                className="rounded-lg flex items-center"
                onClick={() => {
                  updateBatchFeedbacks({
                    feedbackIds: selectedFeedbacks?.map(
                      (feedback: Feedback) => feedback?._id,
                    ) as Id<"feedback">[],
                    status: status.value,
                  });
                }}
              >
                {status.label}
              </DropdownMenuItem>
            ))}
          </DropdownMenuContent>
        </DropdownMenu>

        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" className="rounded-lg" size="sm">
              More
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent
            className="rounded-xl space-y-1 mb-4 -mr-4"
            align="end"
          >
            <DropdownMenuItem
              className="rounded-lg flex items-center"
              onClick={onDeselectAll}
            >
              Deselect All
            </DropdownMenuItem>
            <DropdownMenuItem
              className="rounded-lg !bg-destructive hover:!bg-red-800 text-white hover:text-white"
              onClick={() => {
                updateBatchFeedbacks({
                  feedbackIds: selectedFeedbacks?.map(
                    (feedback: Feedback) => feedback?._id,
                  ) as Id<"feedback">[],
                  isDelete: true,
                });
              }}
            >
              Delete {selectedFeedbacks?.length} feedback
              {selectedFeedbacks && selectedFeedbacks?.length > 1 ? "s" : ""}
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </div>
  );
};

export default BottomBarFeedback;
