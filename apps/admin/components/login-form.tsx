"use client";

import { cn } from "@workspace/ui/lib/utils";
import { Button } from "@workspace/ui/components/button";
import { Input } from "@workspace/ui/components/input";
import { useState, useEffect } from "react";
import { Badge } from "@workspace/ui/components/badge";
import { ArrowLeft, Eye, EyeOff } from "lucide-react";
import { useAuthActions } from "@convex-dev/auth/react";
import { useMutation, useQuery } from "convex/react";
import { api } from "@workspace/backend/convex/_generated/api";
import { toast } from "sonner";
import Image from "next/image";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useDebouncedCallback } from "@/hooks/use-debounce-callback";
import { ConvexError } from "convex/values";
import { z } from "zod";

const emailSchema = z.string().email("Invalid email address");
const passwordSchema = z
  .string()
  .min(8, "Password must be at least 8 characters long");

const signInSchema = z.object({
  email: emailSchema,
  password: passwordSchema,
  flow: z.literal("signIn"),
  loginType: z.literal("password"),
});

const signUpSchema = z
  .object({
    email: emailSchema,
    password: passwordSchema,
    confirmPassword: passwordSchema,
    flow: z.literal("signUp"),
    loginType: z.literal("password"),
  })
  .refine((data) => data.password === data.confirmPassword, {
    message: "Passwords do not match",
    path: ["confirmPassword"],
  });

const ALLOWED_EMAILS = ["<EMAIL>", "<EMAIL>"];

export function LoginForm({
  className,
  ...props
}: React.ComponentProps<"div">) {
  const router = useRouter();
  const { signIn } = useAuthActions();
  const [email, setEmail] = useState("");
  const [debouncedEmail, setDebouncedEmail] = useState(email);
  const [showPassword, setShowPassword] = useState(false);
  const [emailSubmitted, setEmailSubmitted] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [flow, setFlow] = useState<"signIn" | "signUp">("signIn");
  const update = useMutation(api.users.updateUserLoginType);
  const [errors, setErrors] = useState<{ [key: string]: string }>({});

  const lastLoginType =
    useQuery(api.users.getUserLoginType, { email: debouncedEmail }) || null;
  const isNewUser = emailSubmitted && !lastLoginType;

  const debouncedSetEmail = useDebouncedCallback((value: string) => {
    setDebouncedEmail(value);
  }, 300);

  useEffect(() => {
    const savedEmail = localStorage.getItem("liveciety_user_email");
    if (savedEmail) {
      setEmail(savedEmail);
      setDebouncedEmail(savedEmail);
    }
  }, []);

  useEffect(() => {
    if (isNewUser) {
      setFlow("signUp");
    } else if (emailSubmitted) {
      setFlow("signIn");
    }
  }, [isNewUser, emailSubmitted]);

  const validateForm = () => {
    setErrors({});
    try {
      if (flow === "signUp") {
        signUpSchema.parse({
          email: email.toLowerCase().trim(),
          password,
          confirmPassword,
          flow,
          loginType: "password",
        });
      } else {
        signInSchema.parse({
          email: email.toLowerCase().trim(),
          password,
          flow,
          loginType: "password",
        });
      }
      return true;
    } catch (error) {
      if (error instanceof z.ZodError) {
        const newErrors: { [key: string]: string } = {};
        error.errors.forEach((err) => {
          const path = err.path.join(".");
          newErrors[path] = err.message;
        });
        setErrors(newErrors);
      }
      return false;
    }
  };

  const handleEmailSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    try {
      emailSchema.parse(email);
      if (!ALLOWED_EMAILS.includes(email.toLowerCase().trim())) {
        setErrors({ email: "This email is not allowed to login." });
        return;
      }
      localStorage.setItem("liveciety_user_email", email);
      setEmailSubmitted(true);
      setErrors({});
    } catch (error) {
      if (error instanceof z.ZodError) {
        setErrors({
          email: error.errors[0]?.message || "Invalid email address",
        });
      }
    }
  };

  const handleUseAnotherEmail = () => {
    setEmailSubmitted(false);
    setEmail("");
    setDebouncedEmail("");
    setPassword("");
    setConfirmPassword("");
    localStorage.removeItem("liveciety_user_email");
  };

  const handlePasswordSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsLoading(true);

    try {
      const credentials = {
        email: email.toLowerCase().trim(),
        password,
        flow,
        loginType: "password" as const,
      };

      await signIn("password", credentials);
      await update({
        email: email.toLowerCase().trim(),
        loginType: "password",
      });
      toast.success(
        flow === "signIn"
          ? "Signed in successfully!"
          : "Account created successfully!",
      );

      setTimeout(() => {
        router.push("/v/dashboard");
      }, 500);
    } catch (error) {
      if (
        error instanceof Error &&
        error.message.includes(
          "Cannot read properties of null (reading 'redirect')",
        )
      ) {
        setErrors({ password: "Invalid password" });
      } else if (error instanceof ConvexError) {
        toast.error(
          flow === "signIn"
            ? "Could not sign in, did you mean to sign up?"
            : "Could not sign up, did you mean to sign in?",
        );
      } else {
        toast.error("An error occurred. Please try again.");
      }
    } finally {
      setIsLoading(false);
    }
  };

  const handleGoogleSignIn = async () => {
    setIsLoading(true);
    try {
      await signIn("google");
      await update({ email: email.toLowerCase().trim(), loginType: "oauth" });
    } catch (error) {
      toast.error("An error occurred. Please try again.", {
        description:
          error instanceof Error ? error.message : "An unknown error occurred",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className={cn("flex flex-col gap-6", className)} {...props}>
      <form
        onSubmit={emailSubmitted ? handlePasswordSubmit : handleEmailSubmit}
      >
        <div className="flex flex-col gap-6">
          <div className="flex flex-col items-center gap-2">
            <Link
              href="/"
              aria-label="home"
              className="flex items-center space-x-2"
            >
              <Image
                className="rounded-md z-1 relative border dark:hidden"
                src={'/icon.png'}
                alt="liveciety logo"
                width={48}
                height={48}
              />
              <Image
                className="rounded-md z-1 relative hidden border dark:block"
                src={'/icon.png'}
                alt="liveciety logo"
                width={48}
                height={48}
              />
            </Link>
            <h1 className="text-xl font-bold">
              {isNewUser ? "Sign up for an account" : "Liveciety Admins only"}
            </h1>
          </div>
          <div className="flex flex-col gap-2">
            <div
              className={`${emailSubmitted ? "!cursor-not-allowed" : ""} grid gap-2`}
            >
              <div className="relative">
                <Input
                  id="email"
                  name="email"
                  type="email"
                  value={email}
                  onChange={(e) => {
                    const value = e.target.value;
                    setEmail(value);
                    debouncedSetEmail(value);
                  }}
                  placeholder="Your email (required)"
                  required
                  disabled={emailSubmitted}
                  className="rounded-none"
                  autoComplete="email"
                  aria-invalid={!!errors.email}
                  aria-errormessage={errors.email}
                />
              </div>
              {errors.email && (
                <p className="text-sm text-destructive">{errors.email}</p>
              )}
            </div>

            {emailSubmitted && (
              <>
                {isNewUser ? (
                  <div className="space-y-2">
                    <div className="relative">
                      <Input
                        id="password"
                        name="password"
                        type={showPassword ? "text" : "password"}
                        value={password}
                        onChange={(e) => setPassword(e.target.value)}
                        placeholder="Create a password"
                        required
                        className="rounded-none pr-10"
                        autoComplete="new-password"
                        aria-invalid={!!errors.password}
                        aria-errormessage={errors.password}
                      />
                      <button
                        type="button"
                        onClick={() => setShowPassword(!showPassword)}
                        className="absolute right-3 top-1/2 -translate-y-1/2"
                      >
                        {showPassword ? (
                          <EyeOff className="h-4 w-4 text-gray-500" />
                        ) : (
                          <Eye className="h-4 w-4 text-gray-500" />
                        )}
                      </button>
                    </div>
                    <div className="relative">
                      <Input
                        id="confirmPassword"
                        name="confirmPassword"
                        type={showPassword ? "text" : "password"}
                        value={confirmPassword}
                        onChange={(e) => setConfirmPassword(e.target.value)}
                        placeholder="Confirm password"
                        required
                        className="rounded-none pr-10"
                        autoComplete="new-password"
                        aria-invalid={!!errors.confirmPassword}
                        aria-errormessage={errors.confirmPassword}
                      />
                      <button
                        type="button"
                        onClick={() => setShowPassword(!showPassword)}
                        className="absolute right-3 top-1/2 -translate-y-1/2"
                      >
                        {showPassword ? (
                          <EyeOff className="h-4 w-4 text-gray-500" />
                        ) : (
                          <Eye className="h-4 w-4 text-gray-500" />
                        )}
                      </button>
                    </div>
                    <p className="text-xs text-muted-foreground">
                      Password must be at least 8 characters long
                    </p>
                  </div>
                ) : (
                  <div className="relative">
                    <Input
                      id="password"
                      name="password"
                      type={showPassword ? "text" : "password"}
                      value={password}
                      onChange={(e) => setPassword(e.target.value)}
                      placeholder="Enter your password"
                      required
                      className="rounded-none pr-10"
                      autoComplete="off"
                      aria-invalid={!!errors.password}
                      aria-errormessage={errors.password}
                    />
                    <button
                      type="button"
                      onClick={() => setShowPassword(!showPassword)}
                      className="absolute right-3 top-1/2 -translate-y-1/2"
                    >
                      {showPassword ? (
                        <EyeOff className="h-4 w-4 text-gray-500" />
                      ) : (
                        <Eye className="h-4 w-4 text-gray-500" />
                      )}
                    </button>
                  </div>
                )}
              </>
            )}

            {!emailSubmitted ? (
              <Button
                type="submit"
                className="w-full rounded-none"
                disabled={isLoading}
              >
                Continue
              </Button>
            ) : (
              <div className="flex flex-col gap-4">
                <div className="relative">
                  <Button
                    type="submit"
                    className="w-full rounded-none"
                    disabled={isLoading}
                  >
                    {isLoading ? (
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 24 24"
                        strokeWidth="1.5"
                        stroke="currentColor"
                        className="h-4 w-4 animate-spin"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          d="M16.023 9.348h4.992v-.001M2.985 19.644v-4.992m0 0h4.992m-4.993 0 3.181 3.183a8.25 8.25 0 0 0 13.803-3.7M4.031 9.865a8.25 8.25 0 0 1 13.803-3.7l3.181 3.182m0-4.991v4.99"
                        />
                      </svg>
                    ) : isNewUser ? (
                      "Create Account"
                    ) : (
                      "Sign In"
                    )}
                  </Button>
                  {lastLoginType === "password" && !isNewUser && (
                    <Badge className="absolute rounded-sm -right-2 -top-2 bg-card text-muted-foreground border-input">
                      Last used
                    </Badge>
                  )}
                </div>
                <div className="flex items-center justify-between gap-4">
                  <Button
                    type="button"
                    onClick={handleUseAnotherEmail}
                    className="flex items-center gap-2 h-6 bg-transparent hover:bg-transparent text-muted-foreground hover:text-primary cursor-pointer"
                  >
                    <ArrowLeft className="h-4 w-4" />
                    Use another email
                  </Button>
                  {!isNewUser && (
                    <Button
                      type="button"
                      onClick={() =>
                        toast.info("Password reset functionality coming soon")
                      }
                      className="h-6 bg-transparent hover:bg-transparent text-muted-foreground hover:text-primary cursor-pointer"
                    >
                      Reset password
                    </Button>
                  )}
                </div>
              </div>
            )}
          </div>

          {emailSubmitted && (
            <>
              <div className="after:border-border relative text-center text-sm after:absolute after:inset-0 after:top-1/2 after:z-0 after:flex after:items-center after:border-t">
                <span className="bg-background text-muted-foreground relative z-10 px-2">
                  or
                </span>
              </div>

              <div className="relative">
                <Button
                  variant="outline"
                  type="button"
                  className="w-full rounded-none"
                  onClick={handleGoogleSignIn}
                  disabled={isLoading}
                >
                  {isLoading ? (
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                      strokeWidth="1.5"
                      stroke="currentColor"
                      className="h-4 w-4 animate-spin"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        d="M16.023 9.348h4.992v-.001M2.985 19.644v-4.992m0 0h4.992m-4.993 0 3.181 3.183a8.25 8.25 0 0 0 13.803-3.7M4.031 9.865a8.25 8.25 0 0 1 13.803-3.7l3.181 3.182m0-4.991v4.99"
                      />
                    </svg>
                  ) : (
                    <>
                      <svg
                        className="mr-2 h-4 w-4"
                        xmlns="http://www.w3.org/2000/svg"
                        viewBox="0 0 24 24"
                      >
                        <path
                          d="M12.48 10.92v3.28h7.84c-.24 1.84-.853 3.187-1.787 4.133-1.147 1.147-2.933 2.4-6.053 2.4-4.827 0-8.6-3.893-8.6-8.72s3.773-8.72 8.6-8.72c2.6 0 4.507 1.027 5.907 2.347l2.307-2.307C18.747 1.44 16.133 0 12.48 0 5.867 0 .307 5.387.307 12s5.56 12 12.173 12c3.573 0 6.267-1.173 8.373-3.36 2.16-2.16 2.84-5.213 2.84-7.667 0-.76-.053-1.467-.173-2.053H12.48z"
                          fill="currentColor"
                        />
                      </svg>
                      {isNewUser
                        ? "Register with Google"
                        : "Continue with Google"}
                    </>
                  )}
                </Button>
                {lastLoginType === "oauth" && !isNewUser && (
                  <Badge className="absolute rounded-sm -right-2 -top-2">
                    Last used
                  </Badge>
                )}
              </div>
            </>
          )}
        </div>

        {/* @MSY - Add hidden input for flow */}
        <input type="hidden" name="flow" value={flow} />
      </form>
    </div>
  );
}
