"use client";

import * as React from "react";
import { useQuery } from "convex/react";
import { api } from "@workspace/backend/convex/_generated/api";
import { useRouter } from "next/navigation";
import { User as UserIcon } from "lucide-react";
import { CommandButton } from "./command-button";
import {
  CommandDialog,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
  CommandSeparator,
  Command,
} from "@workspace/ui/components/command";
import { Id } from "@workspace/backend/convex/_generated/dataModel";
import { navigateTo } from "@/lib/utils";
import {
  useCommandGroups,
  CommandItem as CommandItemType,
} from "@/lib/command-menu-config";
import { CommandShortcutDisplay } from "./command-shortcut-display";
import { Icon, IconArrowDown, IconArrowUp } from "@tabler/icons-react";

export function CommandMenu() {
  const router = useRouter();
  const commandGroups = useCommandGroups();
  const [open, setOpen] = React.useState(false);
  const [selectedCommand, setSelectedCommand] =
    React.useState<CommandItemType | null>(null);
  const [inputValue, setInputValue] = React.useState("");
  const commandListRef = React.useRef<HTMLDivElement>(null);
  const user = useQuery(api.users.viewer);
  const shouldSearch = inputValue.trim().length > 0;
  const users = useQuery(
    api.users.searchUsers,
    shouldSearch
      ? {
          searchQuery: inputValue,
          paginationOpts: {
            numItems: 10,
            cursor: null,
          },
        }
      : "skip",
  );

  React.useEffect(() => {
    const down = (e: KeyboardEvent) => {
      if (e.key === "k" && (e.metaKey || e.ctrlKey)) {
        e.preventDefault();
        setOpen((open) => !open);
      }
    };
    document.addEventListener("keydown", down);
    return () => document.removeEventListener("keydown", down);
  }, []);

  React.useEffect(() => {
    if (!open || !commandListRef.current) return;

    const observer = new MutationObserver((mutations) => {
      for (const mutation of mutations) {
        if (
          mutation.type === "attributes" &&
          mutation.attributeName === "data-selected"
        ) {
          const selectedElement = commandListRef.current?.querySelector(
            '[data-selected="true"]',
          ) as HTMLElement;
          if (selectedElement) {
            const value = selectedElement.getAttribute("data-value");
            for (const group of commandGroups) {
              const command = group.items.find((item) => item.label === value);
              if (command) {
                setSelectedCommand(command);
                return;
              }
            }
            if (users) {
              const userCommand = users.users.find(
                (user) => user?.name === value,
              );
              if (userCommand) {
                setSelectedCommand({
                  icon: UserIcon as Icon,
                  label: userCommand.name || "",
                  href: `users/${userCommand._id}`,
                  buttonText: "View",
                });
              }
            }
          }
        }
      }
    });

    observer.observe(commandListRef.current, {
      attributes: true,
      subtree: true,
      attributeFilter: ["data-selected"],
    });

    return () => observer.disconnect();
  }, [open, commandGroups, users]);

  const runCommand = React.useCallback(
    (command: CommandItemType) => {
      setOpen(false);
      if (!user) {
        console.warn("User data not available", { user });
        return;
      }

      if (command.isExternal && command.href) {
        window.open(command.href, "_blank");
      } else if (command.href) {
        navigateTo(command.href, router);
      } else if (command.action) {
        command.action();
      }
    },
    [user, router],
  );

  return (
    <>
      <CommandButton setOpen={setOpen} />
      <CommandDialog open={open} onOpenChange={setOpen}>
        <Command value={inputValue} onValueChange={setInputValue}>
          <CommandInput placeholder="Type a command or search..." />
          <CommandList
            ref={commandListRef}
            className="bg-secondary/20 min-h-[450px]"
          >
            <CommandEmpty>No results found.</CommandEmpty>
            {commandGroups.map((group, index) => (
              <React.Fragment key={group.heading}>
                {index > 0 && <CommandSeparator />}
                <CommandGroup heading={group.heading}>
                  {group.items.map((item) => (
                    <CommandItem
                      key={item.label}
                      onSelect={() => runCommand(item)}
                      value={item.label}
                      onMouseEnter={() => setSelectedCommand(item)}
                      data-value={item.label}
                      className="flex items-center justify-between"
                    >
                      <div className="flex items-center gap-2">
                        <div className="p-1 rounded-md bg-muted border border-border dark:border-zinc-700">
                          <item.icon className="h-4 w-4" />
                        </div>
                        <span>{item.label}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        {item.shortcut && (
                          <CommandShortcutDisplay shortcut={item.shortcut} />
                        )}
                      </div>
                    </CommandItem>
                  ))}
                </CommandGroup>
              </React.Fragment>
            ))}
            {users && (
              <>
                <CommandSeparator />
                <CommandGroup heading="Team Members">
                  {users.users
                    .filter((user) => {
                      return true;
                    })
                    .map((member) => {
                      const memberCommand = {
                        icon: UserIcon as Icon,
                        label: member?.name || "",
                        href: `users/${member?._id}`,
                        buttonText: "View",
                      };
                      return (
                        <CommandItem
                          key={member?._id}
                          onSelect={() => member && runCommand(memberCommand)}
                          value={memberCommand.label}
                          onMouseEnter={() => setSelectedCommand(memberCommand)}
                          data-value={memberCommand.label}
                          className="flex items-center justify-between"
                        >
                          <div className="flex items-center gap-2">
                            <div className="p-1 rounded-md bg-muted border border-border dark:border-zinc-700">
                              <UserIcon className="h-4 w-4" />
                            </div>
                            <span>{member?.name}</span>
                          </div>
                        </CommandItem>
                      );
                    })}
                </CommandGroup>
              </>
            )}
          </CommandList>
        </Command>
        <div className="flex items-center justify-between p-2 border-t border-border">
          <div className="flex items-center gap-2">
            <div className="flex items-center gap-1">
              <div className="border border-input rounded-md p-1">
                <IconArrowUp className="h-3 w-3 text-muted-foreground" />
              </div>
              <div className="border border-input rounded-md p-1">
                <IconArrowDown className="h-3 w-3 text-muted-foreground" />
              </div>
            </div>
            <span className="text-xs text-muted-foreground">to navigate</span>
          </div>
          <div className="flex items-center gap-2">
            <span className="text-xs bg-primary text-primary-foreground px-2 py-1 rounded-md">
              {selectedCommand?.buttonText || "Select"}
            </span>
          </div>
        </div>
      </CommandDialog>
    </>
  );
}
