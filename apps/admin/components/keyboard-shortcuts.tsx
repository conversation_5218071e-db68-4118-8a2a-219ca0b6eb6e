import React from "react";
import { Input } from "@workspace/ui/components/input";
import { IconX, IconArrowLeft, IconSearch } from "@tabler/icons-react";
import { Button } from "@workspace/ui/components/button";
import { useState } from "react";

interface KeyboardShortcutsProps {
  isOpen: boolean;
  onClose: () => void;
  onBack: () => void;
}

interface Shortcut {
  name: string;
  keys: string[];
}

const shortcuts: { category: string; items: Shortcut[] }[] = [
  {
    category: "General",
    items: [
      { name: "Open quick actions", keys: ["⌘", "k"] },
      { name: "Open help", keys: ["⌘", "h"] },
      { name: "Create task", keys: ["t"] },
    ],
  },
];

export function KeyboardShortcuts({ onClose, onBack }: KeyboardShortcutsProps) {
  const [searchQuery, setSearchQuery] = useState("");

  const filteredShortcuts = shortcuts
    .map((category) => ({
      category: category.category,
      items: category.items.filter((shortcut) =>
        shortcut.name.toLowerCase().includes(searchQuery.toLowerCase()),
      ),
    }))
    .filter((category) => category.items.length > 0);

  return (
    <div className="flex h-full flex-col rounded-lg overflow-hidden">
      <div className="flex-1 overflow-y-auto">
        <div className="px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Button
                variant="ghost"
                size="icon"
                className="h-6 w-6 mr-2 hover:bg-zinc-800/50"
                onClick={() => {
                  onBack();
                }}
              >
                <IconArrowLeft className="h-4 w-4" />
              </Button>
              <h2 className="text-lg font-medium text-white">
                Keyboard shortcuts
              </h2>
            </div>
            <Button
              className="h-6 w-6"
              variant="ghost"
              size="icon"
              onClick={onClose}
            >
              <IconX className="h-5 w-5" />
            </Button>
          </div>
          <div className="mt-4">
            <div className="relative">
              <Input
                placeholder="Search..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                leftIcon={<IconSearch className="h-4 w-4" />}
              />
            </div>
          </div>
        </div>

        <div className="px-4 py-2">
          {filteredShortcuts.map((category) => (
            <div key={category.category} className="mb-6">
              <h3 className="text-xs font-medium text-gray-400 px-2 mb-2">
                {category.category}
              </h3>
              <div className="space-y-1">
                {category.items.map((shortcut) => (
                  <div
                    key={shortcut.name}
                    className="flex items-center justify-between px-2 py-1.5 rounded-md  dark:hover:bg-zinc-800/50 hover:bg-zinc-200/50"
                  >
                    <span className="text-sm text-zinc-900 dark:text-zinc-100">
                      {shortcut.name}
                    </span>
                    <div className="flex items-center gap-1">
                      {shortcut.keys.map((key, index) => (
                        <React.Fragment key={index}>
                          {key === "then" ? (
                            <span className="text-[10px] tracking-tighter text-zinc-500">
                              then
                            </span>
                          ) : (
                            <kbd
                              key={index}
                              className="px-1.5 py-0.5 text-xs font-medium dark:bg-zinc-800 dark:text-zinc-400 bg-zinc-200 text-zinc-900 rounded-sm border dark:border-zinc-700 dark:hover:bg-zinc-700/50 hover:bg-zinc-200/50"
                            >
                              {key}
                            </kbd>
                          )}
                        </React.Fragment>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
