"use client";

import { <PERSON><PERSON> } from "@workspace/ui/components/button";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@workspace/ui/components/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@workspace/ui/components/popover";
import { CheckIcon } from "lucide-react";
import { useEffect, useId, useState } from "react";
import { TaskPriority } from "@workspace/backend/convex/lib/types";
import { TASK_PRIORITY } from "@/lib/constants";
import { useMutation } from "convex/react";
import { api } from "@workspace/backend/convex/_generated/api";
import React from "react";
import { IconSquareRoundedCheckFilled } from "@tabler/icons-react";

interface PrioritySelectorProps {
  priority: TaskPriority;
  taskId?: string;
  onPriorityChange?: (taskId: string, priority: TaskPriority) => void;
}

export function PrioritySelector({
  priority,
  taskId,
  onPriorityChange,
}: PrioritySelectorProps) {
  const id = useId();
  const [open, setOpen] = useState<boolean>(false);
  const [value, setValue] = useState<TaskPriority>(priority);

  useEffect(() => {
    setValue(priority);
  }, [priority]);

  const handlePriorityChange = (priorityId: TaskPriority) => {
    setValue(priorityId);
    setOpen(false);

    if (taskId && onPriorityChange) {
      onPriorityChange(taskId, priorityId);
    }
  };

  return (
    <div className="*:not-first:mt-2" onClick={(e) => e.stopPropagation()}>
      <Popover
        open={open}
        onOpenChange={(isOpen) => {
          setOpen(isOpen);
        }}
      >
        <PopoverTrigger asChild>
          <Button
            id={id}
            className="size-7 flex items-center justify-center"
            size="icon"
            variant="ghost"
            role="combobox"
            aria-expanded={open}
            onClick={(e) => e.stopPropagation()}
          >
            {(() => {
              const selectedItem = TASK_PRIORITY.find(
                (item) => item.value === value,
              );
              if (selectedItem) {
                const Icon = selectedItem.icon;
                return <Icon className={`size-4 ${selectedItem.color}`} />;
              }
              return null;
            })()}
          </Button>
        </PopoverTrigger>
        <PopoverContent
          className="border-input p-0 bg-zinc-100 dark:bg-zinc-900 min-w-[var(--radix-popper-anchor-width)]"
          align="start"
        >
          <Command className="bg-zinc-100 dark:bg-zinc-900">
            <CommandInput placeholder="Set priority..." />
            <CommandList className="!h-fit">
              <CommandEmpty>No priority found.</CommandEmpty>
              <CommandGroup>
                {TASK_PRIORITY.map((item) => (
                  <CommandItem
                    key={item.value}
                    value={item.value}
                    onSelect={handlePriorityChange}
                    className="flex items-center justify-between"
                  >
                    <div className="flex items-center gap-2">
                      {React.createElement(item.icon, {
                        className: `size-4 ${item.color}`,
                      })}
                      {item.label}
                    </div>
                    {value === item.value && (
                      <IconSquareRoundedCheckFilled
                        size={16}
                        className="ml-auto text-blue-400"
                      />
                    )}
                  </CommandItem>
                ))}
              </CommandGroup>
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>
    </div>
  );
}
