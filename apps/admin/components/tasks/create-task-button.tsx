"use client";

import * as React from "react";
import { PlusIcon } from "lucide-react";
import { Button } from "@workspace/ui/components/button";
import { useKanban } from "@/context/kanban-wrapper";
import { cn } from "@workspace/ui/lib/utils";
import { useTasks } from "@/context/tasks-wrapper";

interface CreateTaskButtonProps {
  icon?: boolean;
  className?: string;
}

export function CreateTaskButton({
  icon = true,
  className,
}: CreateTaskButtonProps) {
  const { openCreateTask } = useTasks();
  let hoveredColumn = null;

  try {
    const kanbanContext = useKanban();
    hoveredColumn = kanbanContext.hoveredColumn;
  } catch (e) {
    console.error("Not in Kanban context", e);
  }

  const handleClick = () => {
    openCreateTask(hoveredColumn || undefined);
  };

  return (
    <>
      {icon ? (
        <Button
          className={cn("h-7 w-7", className)}
          variant="outline"
          size="icon"
          onClick={handleClick}
        >
          <PlusIcon />
        </Button>
      ) : (
        <Button variant="outline" onClick={handleClick}>
          Create Task
        </Button>
      )}
    </>
  );
}
