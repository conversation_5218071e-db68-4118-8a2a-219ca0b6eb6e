"use client";

import {
  IconSchool,
  IconBolt,
  IconPlant2,
  IconCode,
  IconDatabase,
  IconX,
  IconHelp,
  IconSearch,
  IconKeyboard,
  IconVocabularyOff,
} from "@tabler/icons-react";
import { Button } from "@workspace/ui/components/button";
import {
  Dialog,
  DialogContent,
  DialogTitle,
} from "@workspace/ui/components/dialog";
import { Input } from "@workspace/ui/components/input";
import { useState } from "react";
import { KeyboardShortcuts } from "./keyboard-shortcuts";

interface HelpDrawerProps {
  isOpen: boolean;
  onClose: () => void;
}

interface HelpItem {
  icon: React.ReactNode;
  title: string;
  description: string;
  href: string;
}

const helpItems: { category: string; items: HelpItem[] }[] = [
  {
    category: "Suggestions",
    items: [
      {
        icon: <IconCode className="h-5 w-5" />,
        title: "Mutations",
        description: "Learn how to write mutations to your data.",
        href: "https://docs.convex.dev/functions/mutation-functions",
      },
      {
        icon: <IconDatabase className="h-5 w-5" />,
        title: "Reading data",
        description: "Learn how to read data from your database.",
        href: "https://docs.convex.dev/database/reading-data",
      },
      {
        icon: <IconSchool className="h-5 w-5" />,
        title: "Convex Tutorial: A chat app",
        description: "Convex provides you with a fully featured backend...",
        href: "https://docs.convex.dev/tutorial/",
      },
    ],
  },
  {
    category: "Getting started",
    items: [
      {
        icon: <IconBolt className="h-5 w-5" />,
        title: "Quickstarts",
        description: "Quickly get up and running.",
        href: "https://docs.convex.dev/quickstarts",
      },
      {
        icon: <IconPlant2 className="h-5 w-5" />,
        title: "Best practices",
        description: "Best practices and common anti-patterns around...",
        href: "https://docs.convex.dev/understanding/best-practices/",
      },
      {
        icon: <IconBolt className="h-5 w-5" />,
        title: "Realtime",
        description: "Convex is automatically realtime!",
        href: "https://docs.convex.dev/realtime",
      },
    ],
  },
];

export function HelpDrawer({ isOpen, onClose }: HelpDrawerProps) {
  const [searchQuery, setSearchQuery] = useState("");
  const [showKeyboardShortcuts, setShowKeyboardShortcuts] = useState(false);

  const handleBackFromShortcuts = () => {
    setShowKeyboardShortcuts(false);
  };

  const handleOpenChange = (open: boolean) => {
    if (!open) {
      setSearchQuery("");
    }
    onClose();
  };

  const filteredHelpItems = helpItems
    .map((category) => ({
      category: category.category,
      items: category.items.filter(
        (item) =>
          item.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
          item.description.toLowerCase().includes(searchQuery.toLowerCase()),
      ),
    }))
    .filter((category) => category.items.length > 0);

  return (
    <>
      {isOpen && (
        <Dialog open={isOpen} onOpenChange={handleOpenChange}>
          <DialogContent
            className="!fixed !top-2 !bottom-2 !right-2 !left-auto !translate-x-0 !translate-y-0 !h-[unset] !rounded-xl flex w-[400px] flex-col p-0 shadow-xl border border-input"
            overlay={false}
            close={false}
          >
            {!showKeyboardShortcuts ? (
              <div className="flex h-full flex-col rounded-lg overflow-hidden">
                <div className="flex-1 overflow-y-auto">
                  <div className="px-4 py-4">
                    <div className="flex items-center justify-between">
                      <DialogTitle>Help Center</DialogTitle>
                      <Button
                        className="h-6 w-6"
                        variant="ghost"
                        size="icon"
                        onClick={onClose}
                      >
                        <IconX className="h-5 w-5" />
                      </Button>
                    </div>
                    <div className="mt-4">
                      <div className="relative">
                        <Input
                          placeholder="Search help"
                          value={searchQuery}
                          onChange={(e) => setSearchQuery(e.target.value)}
                          leftIcon={<IconSearch className="h-4 w-4" />}
                        />
                      </div>
                    </div>
                  </div>

                  {filteredHelpItems.length > 0 ? (
                    filteredHelpItems.map((category) => (
                      <div key={category.category} className="px-4 py-4">
                        <h3 className="text-sm font-medium tracking-wide px-2">
                          {category.category}
                        </h3>
                        <div className="mt-2 space-y-1">
                          {category.items.map((item) => (
                            <HelpCard
                              key={item.title}
                              icon={item.icon}
                              title={item.title}
                              description={item.description}
                              href={item.href}
                            />
                          ))}
                        </div>
                      </div>
                    ))
                  ) : (
                    <div className="flex-1 flex flex-col items-center justify-center px-4 h-full">
                      <IconVocabularyOff className="h-12 w-12 text-muted-foreground mb-3" />
                      <p className="text-sm text-muted-foreground">
                        No results for "{searchQuery}". Try something else!
                      </p>
                    </div>
                  )}
                </div>

                <div className="p-2 space-y-1">
                  <div className="cursor-pointer gap-1 flex items-center text-sm text-zinc-800 hover:text-zinc-900 dark:text-zinc-400 hover:dark:text-zinc-300 px-2 py-1 rounded-md hover:bg-zinc-200/50 dark:hover:bg-zinc-800/50 flex items-center">
                    <IconHelp className="h-4 w-4" />
                    <a
                      href="https://docs.convex.dev?utm_source=convex-phx"
                      target="_blank"
                    >
                      Convex Docs ↗
                    </a>
                  </div>
                  <div className="cursor-pointer gap-1 flex items-center text-sm text-zinc-800 hover:text-zinc-900 dark:text-zinc-400 hover:dark:text-zinc-300 px-2 py-1 rounded-md hover:bg-zinc-200/50 dark:hover:bg-zinc-800/50 flex items-center">
                    <IconSearch className="h-4 w-4" />
                    <a
                      href="https://search.convex.dev?utm_source=convex-phx"
                      target="_blank"
                    >
                      Search Convex ↗
                    </a>
                  </div>
                  <div className="cursor-pointer gap-1 flex items-center text-sm text-zinc-800 hover:text-zinc-900 dark:text-zinc-400 hover:dark:text-zinc-300 px-2 py-1 rounded-md hover:bg-zinc-200/50 dark:hover:bg-zinc-800/50 flex items-center">
                    <IconKeyboard className="h-4 w-4" />
                    <button
                      onClick={() => {
                        setShowKeyboardShortcuts(true);
                      }}
                      className="cursor-pointer"
                    >
                      Shortcuts
                    </button>
                  </div>
                </div>
              </div>
            ) : (
              <KeyboardShortcuts
                isOpen={showKeyboardShortcuts}
                onClose={() => {
                  setShowKeyboardShortcuts(false);
                  onClose();
                }}
                onBack={() => {
                  setShowKeyboardShortcuts(false);
                }}
              />
            )}
          </DialogContent>
        </Dialog>
      )}
    </>
  );
}
function HelpCard({
  icon,
  title,
  description,
  href,
}: {
  icon: React.ReactNode;
  title: string;
  description: string;
  href: string;
}) {
  return (
    <div
      className="group p-2 border border-input rounded-xl cursor-pointer transition-colors bg-white/50 dark:bg-zinc-900/50 hover:bg-zinc-100/80 dark:hover:bg-zinc-800/80"
      onClick={() => window.open(href, "_blank")}
    >
      <div className="flex gap-3">
        <div className="bg-zinc-100 dark:bg-zinc-800 border border-input h-12 w-12 rounded-lg flex items-center justify-center flex-shrink-0 text-zinc-500 dark:text-zinc-400 group-hover:text-zinc-600 dark:group-hover:text-zinc-300 transition-colors">
          {icon}
        </div>
        <div className="py-0.5">
          <h4 className="text-sm font-medium text-zinc-800 dark:text-zinc-200 group-hover:text-zinc-900 dark:group-hover:text-zinc-100 transition-colors">
            {title}
          </h4>
          <p className="mt-0.5 text-xs leading-tight text-zinc-600 dark:text-zinc-400 group-hover:text-zinc-700 dark:group-hover:text-zinc-300 transition-colors">
            {description}
          </p>
        </div>
      </div>
    </div>
  );
}
