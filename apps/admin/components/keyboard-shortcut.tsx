import { cn } from "@workspace/ui/lib/utils";

interface KeyboardShortcutProps {
  keys: string[];
  className?: string;
}

export function KeyboardShortcut({ keys, className }: KeyboardShortcutProps) {
  return (
    <div
      className={cn(
        "flex items-center gap-1 text-xs text-muted-foreground",
        className,
      )}
    >
      {keys.map((key, index) => (
        <>
          <kbd
            key={key}
            className="pointer-events-none h-5 select-none items-center gap-1 rounded border bg-muted px-1.5 font-mono text-[10px] font-medium opacity-100 flex justify-center"
          >
            {key}
          </kbd>
          {index < keys.length - 1 && <span>+</span>}
        </>
      ))}
    </div>
  );
}
