"use client";

import * as React from "react";
import {
  IconBell,
  IconUserPlus,
  IconBuilding,
  IconGitPullRequest,
  IconMessage,
  IconAlertCircle,
} from "@tabler/icons-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from "@workspace/ui/components/dropdown-menu";
import { Button } from "@workspace/ui/components/button";
import { Badge } from "@workspace/ui/components/badge";
import { api } from "@workspace/backend/convex/_generated/api";
import {
  useQuery,
  useMutation,
  usePreloadedQuery,
  Preloaded,
} from "convex/react";
import { toast } from "sonner";
import { Id } from "@workspace/backend/convex/_generated/dataModel";
import { SidebarMenuButton } from "@workspace/ui/components/sidebar";
import {
  Tabs,
  TabsList,
  TabsTrigger,
  TabsContent,
} from "@workspace/ui/components/tabs";
import { useIsMobile } from "@workspace/ui/hooks/use-mobile";
import { cn } from "@workspace/ui/lib/utils";
import Link from "next/link";

type NotificationType =
  | "invite"
  | "system"
  | "user"
  | "comment"
  | "mention"
  | "alert";

interface NotificationConfig {
  icon: React.ReactNode;
  bgColor: string;
  getActions?: (
    notification: any,
    handlers: NotificationHandlers,
  ) => React.ReactNode;
  getMessage?: (notification: any, context: NotificationContext) => string;
  getMetadata?: (
    notification: any,
    context: NotificationContext,
  ) => React.ReactNode;
}

interface NotificationContext {
  users: Record<string, any>;
}

interface NotificationHandlers {
  handleAccept: (
    notificationId: Id<"notifications">,
    userId: Id<"users">,
  ) => Promise<void>;
  handleReject: (
    notificationId: Id<"notifications">,
    userId: Id<"users">,
  ) => Promise<void>;
  handleMarkAsRead: (notificationId: Id<"adminNotifications">) => Promise<void>;
}

const notificationConfigs: Record<NotificationType, NotificationConfig> = {
  invite: {
    icon: <IconBuilding className="h-4 w-4 text-white" />,
    bgColor: "bg-blue-500",
    getActions: (notification, handlers) =>
      !notification.handled &&
      !notification.read && (
        <div className="flex gap-2 mt-2">
          <Button
            size="sm"
            variant="default"
            className="h-8"
            onClick={() =>
              handlers.handleAccept(notification._id, notification.userId)
            }
          >
            Accept
          </Button>
          <Button
            size="sm"
            variant="outline"
            className="h-8"
            onClick={() =>
              handlers.handleReject(notification._id, notification.userId)
            }
          >
            Decline
          </Button>
        </div>
      ),
    getMetadata: (notification) => {
      return (
        <div className="flex flex-col gap-1">
          <p className="text-sm text-muted-foreground">
            {notification.message}
          </p>
        </div>
      );
    },
  },
  system: {
    icon: <IconBell className="h-4 w-4 text-white" />,
    bgColor: "bg-purple-500",
  },
  user: {
    icon: <IconUserPlus className="h-4 w-4" />,
    bgColor: "bg-green-500",
  },
  comment: {
    icon: <IconMessage className="h-4 w-4 text-white" />,
    bgColor: "bg-orange-500",
    getMessage: (notification, { users }) => {
      const user = users[notification.metadata?.createdBy];
      return `${user?.name || "Someone"} commented on your post`;
    },
  },
  mention: {
    icon: <IconGitPullRequest className="h-4 w-4 text-white" />,
    bgColor: "bg-pink-500",
    getMessage: (notification, { users }) => {
      const user = users[notification.metadata?.createdBy];
      return `${user?.name || "Someone"} mentioned you`;
    },
  },
  alert: {
    icon: <IconAlertCircle className="h-4 w-4 text-white" />,
    bgColor: "bg-red-500",
  },
};

const NotificationIcon = ({
  type,
  color,
}: {
  type: NotificationType;
  color?: string;
}) => {
  const config = notificationConfigs[type];
  if (!config) return null;

  return (
    <div
      className={cn(
        "w-8 h-8 rounded-full flex items-center justify-center",
        config.bgColor,
      )}
    >
      {config.icon}
    </div>
  );
};

const NotificationContent = ({
  notification,
  context,
  handlers,
}: {
  notification: any;
  context: NotificationContext;
  handlers: NotificationHandlers;
}) => {
  const config = notificationConfigs[notification.type as NotificationType];
  if (!config) return null;

  const message =
    config.getMessage?.(notification, context) || notification.message;
  const metadata = config.getMetadata?.(notification, context);
  const actions = config.getActions?.(notification, handlers);

  return (
    <div className="flex-1">
      <div className="flex items-start justify-between mb-1">
        <div className="space-y-1">
          <p className="text-sm font-medium">{message}</p>
          {metadata}
        </div>
        <span className="text-xs text-muted-foreground ml-4">
          {getRelativeTime(notification._creationTime)}
        </span>
      </div>
      {actions}
    </div>
  );
};

const getRelativeTime = (timestamp: number) => {
  const now = Date.now();
  const diff = now - timestamp;
  const hours = Math.floor(diff / (1000 * 60 * 60));
  const days = Math.floor(hours / 24);

  if (days > 0) return `${days}d ago`;
  if (hours > 0) return `${hours}h ago`;
  return "Just now";
};

export function Notifications({
  preloadedUser,
}: {
  preloadedUser: Preloaded<typeof api.users.viewer>;
}) {
  const mobile = useIsMobile();
  const result = useQuery(api.adminNotifications.list);
  const user = usePreloadedQuery(preloadedUser);
  const notifications = result?.notifications || [];
  const unreadCount = result?.unreadCount || 0;
  const archived = result?.archived || [];
  const markAsRead = useMutation(api.adminNotifications.markAsRead);
  const updateStatus = useMutation(api.adminNotifications.updateStatus);

  const handleMarkAllAsRead = async () => {
    try {
      await Promise.all(
        notifications.map((notification) =>
          markAsRead({
            notificationId: notification._id,
          }),
        ),
      );
      toast.success("All notifications marked as read");
    } catch (error) {
      toast.error("Failed to mark notifications as read");
    }
  };

  const notificationHandlers: NotificationHandlers = {
    handleMarkAsRead: (notificationId: Id<"adminNotifications">) =>
      markAsRead({ notificationId })
        .then(() => {
          toast.success("Notification marked as read");
        })
        .catch((error) => {
          toast.error("Failed to mark notification as read", error);
        }),
    handleAccept: async () => {},
    handleReject: async () => {},
  };

  const notificationContext: NotificationContext = {
    users: {},
  };

  const renderNotification = (notification: any) => (
    <div
      key={notification._id}
      className="flex items-start gap-3 p-3 rounded-lg hover:bg-zinc-200/50 dark:hover:bg-zinc-800/50 dark:bg-accent/50"
    >
      <NotificationIcon type={notification.type as NotificationType} />
      <NotificationContent
        notification={notification}
        context={notificationContext}
        handlers={notificationHandlers}
      />
    </div>
  );

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <div className="w-full">
          <SidebarMenuButton tooltip="Notifications">
            <IconBell className="stroke-1" />
            <span>Notifications</span>
            {unreadCount > 0 && (
              <span className="inline-flex items-center justify-center gap-1 px-1 py-0 ml-auto rounded-sm text-[12px] font-medium leading-4 tracking-[-0.02em] text-[#EEEFF1] bg-[#266DF0] tabular-nums select-none cursor-pointer">
                {unreadCount}
              </span>
            )}
          </SidebarMenuButton>
        </div>
      </DropdownMenuTrigger>
      <DropdownMenuContent
        className={cn(
          "w-[450px] h-[500px] bg-neutral-100 dark:bg-neutral-900 relative",
          mobile ? "left-2" : "top-15.5 left-3.5",
        )}
        align="end"
        side={mobile ? "bottom" : "right"}
      >
        <div className="flex items-center justify-between p-4">
          <h2 className="text-lg font-semibold">Notifications</h2>
        </div>
        <Tabs defaultValue="unread" className="w-full px-4">
          <TabsList className="w-full justify-start">
            <TabsTrigger
              value="unread"
              className="shadow-none cursor-pointer data-[state=active]:shadow-none"
            >
              Unread{" "}
              <Badge variant="default" className="ml-1">
                {notifications.length}
              </Badge>
            </TabsTrigger>
            <TabsTrigger
              value="archived"
              className="shadow-none cursor-pointer data-[state=active]:shadow-none"
            >
              Archived{" "}
              <Badge variant="secondary" className="ml-1">
                {archived.length > 0 ? archived.length : ""}
              </Badge>
            </TabsTrigger>
          </TabsList>
          <TabsContent value="unread" className="space-y-1">
            {notifications.map(renderNotification)}
          </TabsContent>
          <TabsContent value="archived" className="space-y-1">
            {archived.map(renderNotification)}
          </TabsContent>
        </Tabs>
        <div className="absolute bottom-0 left-0 right-0 p-4 border-t bg-neutral-100 dark:bg-neutral-900 flex items-center justify-between">
          {notifications.length > 0 && (
            <Button
              variant="link"
              className="text-accent-foreground"
              onClick={handleMarkAllAsRead}
            >
              Mark all as read
            </Button>
          )}
          <Button
            variant="outline"
            className="text-neutral-500 hover:text-neutral-600"
          >
            <Link href={`/v/settings/notifications`}>
              Go to notification center
            </Link>
          </Button>
        </div>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
