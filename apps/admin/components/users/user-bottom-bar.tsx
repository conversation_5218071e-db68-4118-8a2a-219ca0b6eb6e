import * as React from "react";

import type { Id } from "@workspace/backend/convex/_generated/dataModel";
import { But<PERSON> } from "@workspace/ui/components/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@workspace/ui/components/dropdown-menu";
import { PUBLIC_USER_ROLES, TASK_PRIORITY, TASK_STATUS } from "@/lib/constants";
import { useMutation, usePreloadedQuery, useQuery } from "convex/react";
import { api } from "@workspace/backend/convex/_generated/api";
import { IconTrash, IconX } from "@tabler/icons-react";
import { Task, User } from "@workspace/backend/convex/lib/types";
import { usePreloadedData } from "@/hooks/use-preloaded-data";
import {
  Avatar,
  AvatarFallback,
  AvatarImage,
} from "@workspace/ui/components/avatar";
import {
  DropdownMenuRadioGroup,
  DropdownMenuRadioItem,
  DropdownMenuSub,
  DropdownMenuSubContent,
  DropdownMenuSubTrigger,
} from "@workspace/ui/components/dropdown-menu";

interface BottomBarProps {
  selectedUsers?: User[];
  onDeselectAll?: () => void;
}

const BottomBarUsers = ({ selectedUsers, onDeselectAll }: BottomBarProps) => {
  const updateBatchUsers = useMutation(api.users.updateBatchUsers);
  const { preloadedUser } = usePreloadedData();
  const user = usePreloadedQuery(preloadedUser) as User;

  if (!selectedUsers || selectedUsers.length === 0) return null;

  return (
    <div className="fixed bottom-5 left-1/2 transform -translate-x-1/2 app-bg/75 border border-muted rounded-xl shadow-lg p-1 pl-4 flex justify-between items-center w-11/12 max-w-3xl h-12 backdrop-blur-xl z-50">
      <div className="flex items-center">
        <span className="rounded-sm bg-zinc-100 dark:bg-zinc-900 px-1 py-0 text-xs font-medium border border-input font-mono mr-2">
          {selectedUsers.length}
        </span>
        <span className="text-xs text-muted-foreground">selected</span>
      </div>
      <div className="flex items-center space-x-1">
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" className="rounded-lg" size="sm">
              Role
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent
            className="rounded-xl space-y-1 mb-4 -mr-4"
            align="end"
          >
            {PUBLIC_USER_ROLES.map((role) => (
              <DropdownMenuItem
                key={role.value}
                className="rounded-lg flex items-center"
                onClick={() => {
                  updateBatchUsers({
                    userIds: selectedUsers?.map(
                      (user) => user?._id,
                    ) as Id<"users">[],
                    role: role.value,
                  });
                }}
              >
                {React.createElement(role.icon, {
                  className: `h-3 w-3 mr-2 ${role.color}`,
                })}
                {role.label}
              </DropdownMenuItem>
            ))}
          </DropdownMenuContent>
        </DropdownMenu>

        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" className="rounded-lg" size="sm">
              More
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent
            className="rounded-xl space-y-1 mb-4 -mr-4"
            align="end"
          >
            <DropdownMenuItem
              className="rounded-lg flex items-center"
              onClick={onDeselectAll}
            >
              Deselect All
            </DropdownMenuItem>
            <DropdownMenuItem
              className="rounded-lg !bg-destructive hover:!bg-red-800 text-white hover:text-white"
              onClick={() => {
                updateBatchUsers({
                  userIds: selectedUsers?.map(
                    (user) => user?._id,
                  ) as Id<"users">[],
                  isDelete: true,
                });
              }}
            >
              Delete {selectedUsers?.length} user
              {selectedUsers && selectedUsers?.length > 1 ? "s" : ""}
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </div>
  );
};

export default BottomBarUsers;
