import type { User, UserRole } from "@workspace/backend/convex/lib/types";
import { USER_ROLES } from "./constants";

export const ADMIN_ROLES: UserRole[] = [USER_ROLES.find((role) => role.value === "org:admin")?.value as UserRole];

export function hasAdminRole(user: any): boolean {
  return ADMIN_ROLES.includes((user as User).role as UserRole);
}

export function hasRole(user: any, roles: UserRole[]): boolean {
  return roles.includes((user as User).role as UserRole);
}

export const Permissions = {
  canManageUsers: hasAdminRole,
  canManageSettings: hasAdminRole,
  canFilterTasks: hasAdminRole,
  canGroupByAssignee: hasAdminRole,
  canGroupByStatus: hasAdminRole,
  canGroupByPriority: hasAdminRole,
  canGroupByDueDate: hasAdminRole,
  canGroupByCreatedAt: hasAdminRole,
  canGroupByUpdatedAt: hasAdminRole,
  canGroupByCompletedAt: hasAdminRole,
  canFilterUsers: hasAdminRole,
} as const;
