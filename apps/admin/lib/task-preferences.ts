import { STORAGE_KEYS, TaskPreferences } from "./constants";
import { getLocalStorage, setLocalStorage } from "./local-storage";

const defaultPreferences: TaskPreferences = {
  view: "list",
  sortBy: "dueDate",
  sortOrder: "asc",
  groupBy: "status",
  filters: {
    status: [],
    priority: [],
    assignee: [],
    title: "",
  },
  showCompleted: false,
  columnVisibility: {},
  statusColumnVisibility: {
    backlog: true,
    todo: true,
    in_progress: true,
    review: true,
    done: true,
  },
};

export const getTaskPreferences = (): TaskPreferences => {
  const stored = getLocalStorage(STORAGE_KEYS.TASKS_PREFERENCES as string);
  if (!stored) return defaultPreferences;

  try {
    return JSON.parse(stored) as TaskPreferences;
  } catch (e) {
    console.error("Error parsing task preferences", e);
    return defaultPreferences;
  }
};

export const updateTaskPreferences = (
  updates: Partial<TaskPreferences>,
): TaskPreferences => {
  const current = getTaskPreferences();
  const updated = {
    ...current,
    ...updates,
    filters: {
      ...current.filters,
      ...updates.filters,
    },
  };

  setLocalStorage(STORAGE_KEYS.TASKS_PREFERENCES as string, JSON.stringify(updated));
  return updated;
};

export const clearTaskPreferences = () => {
  setLocalStorage(
    STORAGE_KEYS.TASKS_PREFERENCES as string,
    JSON.stringify(defaultPreferences),
  );
  return defaultPreferences;
};
