import {
  IconAlertSquareRoundedFilled,
  IconAntennaBars1,
  IconAntennaBars2,
  IconAntennaBars3,
  IconAntennaBars5,
  IconBrandAsana,
  IconCircle,
  IconCircleDotted,
  IconCircleHalf2,
  IconMoneybag,
  IconShield,
  IconSquareRou<PERSON><PERSON>heck,
  IconUserCircle,
} from "@tabler/icons-react";

export const USER_ROLES: {
  label: string;
  value: string;
  description: string;
}[] = [
  {
    label: "Owner",
    value: "org:owner",
    description: "full access",
  },
  {
    label: "Admin",
    value: "org:admin",
    description: "full access",
  },
  {
    label: "Member",
    value: "org:member",
    description: "Can edit",
  },
  {
    label: "Guest",
    value: "org:guest",
    description: "Can view",
  },
];

export const STORAGE_PREFIX = "liveciety_";

export const STORAGE_KEYS: Record<string, string> = {
  TASKS_VIEW: `${STORAGE_PREFIX}tasks_view`,
  TASKS_PREFERENCES: `${STORAGE_PREFIX}tasks_preferences`,
  FEEDBACK_VIEW: `${STORAGE_PREFIX}feedback_view`,
  FEEDBACK_PREFERENCES: `${STORAGE_PREFIX}feedback_preferences`,
  CALENDAR_VIEW: `${STORAGE_PREFIX}calendar_view`,
  SIDEBAR_STATE: `${STORAGE_PREFIX}sidebar_state`,
  USERS_PREFERENCES: `${STORAGE_PREFIX}users_preferences`,
  USERS_VIEW: `${STORAGE_PREFIX}users_view`,
};

export const TASK_PRIORITY = [
  {
    value: "urgent",
    label: "Urgent",
    icon: IconAlertSquareRoundedFilled,
    color: "text-red-500",
  },
  {
    value: "high",
    label: "High",
    icon: IconAntennaBars5,
    color: "text-zinc-500",
  },
  {
    value: "medium",
    label: "Medium",
    icon: IconAntennaBars3,
    color: "text-zinc-500",
  },
  {
    value: "low",
    label: "Low",
    icon: IconAntennaBars2,
    color: "text-zinc-500",
  },
  {
    value: "no_priority",
    label: "No priority",
    icon: IconAntennaBars1,
    color: "text-zinc-500",
  },
] as const;

export const TASK_STATUS = [
  {
    label: "Backlog",
    value: "backlog",
    icon: IconCircleDotted,
    color: "text-zinc-500",
  },
  {
    label: "Todo",
    value: "todo",
    icon: IconCircle,
    color: "text-zinc-500",
  },
  {
    label: "In Progress",
    value: "in_progress",
    icon: IconCircleHalf2,
    color: "text-yellow-500",
  },
  {
    label: "Review",
    value: "review",
    icon: IconBrandAsana,
    color: "text-orange-500",
  },
  {
    label: "Done",
    value: "done",
    icon: IconSquareRoundedCheck,
    color: "text-blue-400",
  },
] as const;

export const FEEDBACK_STATUS = [
  {
    label: "New",
    value: "new",
  },
  {
    label: "In Progress",
    value: "in_progress",
  },
  {
    label: "Done",
    value: "done",
  },
  {
    label: "Rejected",
    value: "rejected",
  },
] as const;

export const FEEDBACK_TYPE = [
  {
    label: "Bug",
    value: "bug",
  },
  {
    label: "Feature Request",
    value: "feature_request",
  },
] as const;

export type TaskStatus = (typeof TASK_STATUS)[number]["value"];
export type TaskPriority = (typeof TASK_PRIORITY)[number]["value"];
export type FeedbackStatus = (typeof FEEDBACK_STATUS)[number]["value"];
export type FeedbackType = (typeof FEEDBACK_TYPE)[number]["value"];

export interface TaskPreferences {
  view: string;
  sortBy?: string;
  sortOrder?: "asc" | "desc";
  showCompleted?: boolean;
  columnVisibility?: Record<string, boolean>;
  statusColumnVisibility?: Record<TaskStatus, boolean>;
  groupBy?: string;
  filters?: {
    status?: string[];
    priority?: string[];
    assignee?: string[];
    title?: string;
  };
}

export interface FeedbackPreferences {
  view: string;
  sortBy?: string;
  sortOrder?: "asc" | "desc";
  showCompleted?: boolean;
  columnVisibility?: Record<string, boolean>;
  statusColumnVisibility?: Record<FeedbackStatus, boolean>;
  groupBy?: string;
  filters?: {
    status?: string[];
    priority?: string[];
    assignee?: string[];
    title?: string;
  };
}

export interface UserPreferences {
  view: string;
  sortBy?: string;
  sortOrder?: "asc" | "desc";
  filters?: {
    name?: string;
    email?: string;
    role?: "user" | "admin" | "seller";
    username?: string;
  };
}

export const PUBLIC_USER_ROLES = [
  {
    label: "User",
    value: "user",
    color: "text-zinc-500",
    icon: IconUserCircle,
  },
  {
    label: "Seller",
    value: "seller",
    color: "text-violet-500",
    icon: IconMoneybag,
  },
  {
    label: "Admin",
    value: "admin",
    color: "text-blue-500",
    icon: IconShield,
  },
];
