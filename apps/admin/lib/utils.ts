import { User } from "@workspace/backend/convex/lib/types";
import { AppRouterInstance } from "next/dist/shared/lib/app-router-context.shared-runtime";

/**
 * Get the URL for a path
 * @param baseUrl - The base URL to get the URL for
 * @returns The URL for the path
 */
export function getUrl(baseUrl: string) {
  if (baseUrl === "#") return baseUrl;

  const url = baseUrl.startsWith("/") ? `/v${baseUrl}` : `/v${baseUrl}`;

  return url;
}

/**
 * Navigate to a path
 * @param path - The path to navigate to
 * @param router - The router to navigate to
 */
export const navigateTo = (path: string, router: AppRouterInstance) => {
  const url = getUrl(path);
  router.push(url);
};

/**
 * @function formatFileSize
 * @param bytes
 * @returns {string}
 * @description This function will format the file size in bytes to a human-readable format
 */
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return "0 Bytes";
  const k = 1024;
  const sizes = ["Bytes", "KB", "MB", "GB", "TB", "PB", "EB", "ZB", "YB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  const size = parseFloat((bytes / Math.pow(k, i)).toFixed(2));
  return `${size} ${sizes[i]}`;
};

/**
 * Get platform-specific keyboard shortcut symbol
 * @returns {Object} Object containing platform info and shortcut symbol
 */
export const getPlatformShortcuts = () => {
  if (typeof window === "undefined")
    return { isMac: false, shortcutSymbol: "Ctrl" };

  const isMac = navigator.userAgent.toLowerCase().indexOf("mac") !== -1;
  return {
    isMac,
    shortcutSymbol: isMac ? "⌘" : "Ctrl",
  };
};

/**
 * Get the avatar image URL
 * @param image - The image to get the URL for
 * @returns The URL for the image
 */
export function getAvatarImageUrl(image?: string) {
  if (typeof image !== "string" || !image) return undefined;

  if (image.startsWith("http://") || image.startsWith("https://")) {
    return image;
  }
  if (image.includes("/getImage?storageId=")) {
    return image;
  }
  return `${process.env.NEXT_PUBLIC_SITE_URL}/getImage?storageId=${image}`;
}
