import { format, isToday, isSameMonth } from "date-fns";
import { Task } from "@workspace/backend/convex/lib/types";
import { cn } from "@workspace/ui/lib/utils";
import { CalendarTask } from "./calendar-task";
import { useDroppable } from "@dnd-kit/core";
import { useCalendar } from "./calendar-context";
import { useSidePanel } from "@/components/side-panel";
import { CreateTaskModal } from "@/components/tasks/create-task-modal";
import { useState } from "react";
import { Id } from "@workspace/backend/convex/_generated/dataModel";

interface CalendarDayProps {
  date: Date;
  tasks: Task[];
  isLastColumn?: boolean;
  isLastRow?: boolean;
}

export function CalendarDay({
  date,
  tasks,
  isLastColumn,
  isLastRow,
}: CalendarDayProps) {
  const { currentDate } = useCalendar();
  const { setOpen, setTitle, setContent } = useSidePanel();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedTask, setSelectedTask] = useState<Task | null>(null);
  const isCurrentDay = isToday(date);
  const isCurrentMonth = isSameMonth(date, currentDate);
  const dateString = `date-${date.toISOString()}`;

  const { setNodeRef, isOver, active } = useDroppable({
    id: dateString,
  });

  const handleTaskEdit = (task: Task) => {
    setSelectedTask(task);
    setIsModalOpen(true);
  };

  const handleModalOpenChange = (open: boolean) => {
    setIsModalOpen(open);
    if (!open) {
      setSelectedTask(null);
    }
  };

  const showTasksInSidePanel = () => {
    if (tasks.length === 0) return;

    setTitle(format(date, "MMMM d, yyyy"));
    setContent(
      <div className="space-y-2">
        {tasks.map((task) => (
          <CalendarTask
            key={task._id}
            task={task}
            isDraggable={true}
            onEdit={() => handleTaskEdit(task)}
          />
        ))}
      </div>,
    );
    setOpen(true);
  };

  const lastTask = tasks[tasks.length - 1];
  const remainingTasks = tasks.length > 1 ? tasks.length - 1 : 0;

  return (
    <>
      <div
        ref={setNodeRef}
        className={cn(
          "min-h-[120px] relative border-r border-b transition-colors overflow-hidden group",
          isLastColumn && "border-r-0",
          isLastRow && "border-b-0",
          isOver && active && "bg-accent/30 border-accent",
        )}
      >
        {/* Droppable area that covers entire cell */}
        <div className="absolute inset-0" />

        {/* Content container */}
        <div
          className="relative h-full p-2 cursor-pointer"
          onClick={showTasksInSidePanel}
        >
          <div className="flex flex-col gap-1">
            <div
              className={cn(
                "flex items-center justify-center h-6 w-6 rounded-full text-sm mx-auto",
                isCurrentDay &&
                  "bg-primary text-primary-foreground font-medium",
                !isCurrentMonth && "text-muted-foreground/50",
              )}
            >
              {format(date, "d")}
            </div>
            <div className="space-y-1">
              {lastTask && (
                <>
                  <div className="relative">
                    <CalendarTask
                      key={lastTask._id}
                      task={lastTask}
                      className={cn(!isCurrentMonth ? "opacity-50" : "")}
                      onEdit={() => handleTaskEdit(lastTask)}
                    />
                  </div>
                  {remainingTasks > 0 && (
                    <div className="text-xs text-muted-foreground text-center mt-1 py-1 rounded-md bg-accent/50">
                      + {remainingTasks} more
                    </div>
                  )}
                </>
              )}
            </div>
          </div>
        </div>
      </div>
      <CreateTaskModal
        open={isModalOpen}
        onOpenChange={handleModalOpenChange}
        taskToEdit={
          selectedTask
            ? {
                ...selectedTask,
                assignee:
                  selectedTask.assignee &&
                  typeof selectedTask.assignee === "object" &&
                  "_id" in selectedTask.assignee
                    ? {
                        _id: (selectedTask.assignee as { _id: Id<"users"> })
                          ._id,
                        name:
                          (
                            selectedTask.assignee as {
                              name?: string;
                              email?: string;
                            }
                          ).name ||
                          (selectedTask.assignee as { email?: string }).email ||
                          "",
                      }
                    : undefined,
                related: selectedTask.related?.[0]
                  ? {
                      _id: (selectedTask.related[0] as { _id: string })._id,
                      name: `${(selectedTask.related[0] as { firstName?: string }).firstName || ""} ${(selectedTask.related[0] as { lastName?: string }).lastName || ""}`.trim(),
                      recordType: "contact",
                    }
                  : undefined,
              }
            : undefined
        }
      />
    </>
  );
}
