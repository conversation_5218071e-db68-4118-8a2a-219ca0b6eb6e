"use client";

import { createContext, useContext, useState } from "react";
import { STORAGE_KEYS } from "@/lib/constants";

type CalendarView = "day" | "week" | "month";

interface CalendarContextType {
  currentDate: Date;
  setCurrentDate: (date: Date) => void;
  view: CalendarView;
  setView: (view: CalendarView) => void;
}

const CalendarContext = createContext<CalendarContextType | undefined>(
  undefined,
);

export function CalendarProvider({ children }: { children: React.ReactNode }) {
  const [currentDate, setCurrentDate] = useState(new Date());
  const [view, setView] = useState<CalendarView>(() => {
    if (typeof window !== "undefined") {
      const savedView = localStorage.getItem(
        STORAGE_KEYS.CALENDAR_VIEW as string,
      ) as CalendarView | null;
      return (savedView || "month") as CalendarView;
    }
    return "month";
  });

  const handleViewChange = (newView: CalendarView) => {
    setView(newView);
    localStorage.setItem(STORAGE_KEYS.CALENDAR_VIEW as string, newView);
  };

  return (
    <CalendarContext.Provider
      value={{
        currentDate,
        setCurrentDate,
        view,
        setView: handleViewChange,
      }}
    >
      {children}
    </CalendarContext.Provider>
  );
}

export function useCalendar() {
  const context = useContext(CalendarContext);
  if (context === undefined) {
    throw new Error("useCalendar must be used within a CalendarProvider");
  }
  return context;
}
