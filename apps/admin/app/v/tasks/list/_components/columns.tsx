import React from "react";
import { ColumnDef } from "@tanstack/react-table";
import { DataTableColumnHeader } from "./column-header";
import { TASK_PRIORITY, TASK_STATUS } from "@/lib/constants";
import { cn } from "@workspace/ui/lib/utils";
import { formatDistanceToNow, isPast, format } from "date-fns";
import { Checkbox } from "@workspace/ui/components/checkbox";
import { useMutation, useQuery } from "convex/react";
import { usePreloadedData } from "@/hooks/use-preloaded-data";
import { IconStar, IconStarFilled } from "@tabler/icons-react";
import { api } from "@workspace/backend/convex/_generated/api";
import { Id } from "@workspace/backend/convex/_generated/dataModel";
import { Task, Contact, User } from "@workspace/backend/convex/lib/types";
import { useRouter } from "next/navigation";
import { UserAvatar } from "@workspace/ui/components/user-avatar";

interface Related {
  _id: Id<"contacts">;
  name?: string;
}

interface TaskRow {
  original: Task & {
    related?: Contact[];
  };
}

function includesString(rowValue: string, filterValue: string | string[]) {
  if (typeof rowValue !== "string") {
    return false;
  }

  if (Array.isArray(filterValue)) {
    return filterValue.some((value) =>
      rowValue.toLowerCase().includes(value.toLowerCase()),
    );
  }

  if (typeof filterValue !== "string") {
    return false;
  }

  return rowValue.toLowerCase().includes(filterValue.toLowerCase());
}

const RelatedCell = React.memo(({ row }: { row: TaskRow }) => {
  const router = useRouter();
  const contactIds = (row.original?.related ?? []) as { _id: Id<"contacts"> }[];

  const contact = useQuery(
    api.contacts.getById,
    contactIds[0]?._id ? { id: contactIds[0]._id } : "skip",
  );

  if (!contact) {
    return null;
  }

  const handleClick = () => {
    router.push(`/v/contacts/${contact._id}`);
  };

  return (
    <div
      className="flex items-center cursor-pointer hover:text-blue-500"
      onClick={handleClick}
    >
      {contact.firstName} {contact.lastName}
    </div>
  );
});

RelatedCell.displayName = "RelatedCell";

interface Favorite {
  _id: Id<"favorites">;
  objectId: Id<"tasks">;
  userId: Id<"users">;
}

const FavoriteButton = React.memo(
  ({ taskId, favorites }: { taskId: Id<"tasks">; favorites: Favorite[] }) => {
    const toggleFavorite = useMutation(api.favorites.toggle);
    const isFavorited = favorites.some((f) => f.objectId === taskId);

    const handleToggle = (e: React.MouseEvent) => {
      e.stopPropagation();
      toggleFavorite({ objectId: taskId });
    };

    const StarIcon = isFavorited ? IconStarFilled : IconStar;

    return (
      <button
        onClick={handleToggle}
        className={cn(
          "opacity-0 group-hover:opacity-100 transition-opacity",
          "hover:text-yellow-500",
          isFavorited && "opacity-100 text-yellow-500",
        )}
      >
        <StarIcon className="size-4" />
      </button>
    );
  },
);

FavoriteButton.displayName = "FavoriteButton";

const TitleCell = React.memo(({ row }: { row: TaskRow }) => {
  const favorites = useQuery(api.favorites.get) || [];
  const toggleFavorite = useMutation(api.favorites.toggle);
  const taskId = row.original?._id;
  const isFavorited = favorites.some((f) => f?.objectId === taskId);

  return (
    <div className="flex flex-row items-center space-x-2 px-2 group">
      {taskId && (
        <button
          onClick={(e) => {
            e.stopPropagation();
            toggleFavorite({
              objectId: taskId,
            });
          }}
          className={cn(
            "cursor-pointer",
            "hover:bg-muted/50 p-2 rounded-lg",
            "opacity-0 group-hover:opacity-100 transition-opacity",
            "hover:text-yellow-500",
            isFavorited && "opacity-100 text-yellow-500",
          )}
        >
          {isFavorited ? (
            <IconStarFilled className="size-4" />
          ) : (
            <IconStar className="size-4" />
          )}
        </button>
      )}
      <span className="max-w-[500px] truncate font-medium cursor-pointer">
        {row.original?.title}
      </span>
    </div>
  );
});

TitleCell.displayName = "TitleCell";

const AssigneeCell = React.memo(({ row }: { row: any }) => {
  const assignee = row.original?.assignee;

  if (!assignee || !assignee.name) return null;

  return (
    <div className="flex items-center gap-1 border border-input dark:border-zinc-700 bg-zinc-100 dark:bg-zinc-800/50 p-1 rounded-lg w-fit">
      <UserAvatar user={assignee} size="sm" />
      <span className="text-sm">{assignee.name}</span>
    </div>
  );
});

AssigneeCell.displayName = "AssigneeCell";

const priorityOrder: Record<string, number> = {};
TASK_PRIORITY.forEach((priority, index) => {
  priorityOrder[priority.value] = index;
});

export const columns: ColumnDef<Task>[] = [
  {
    id: "select",
    header: ({ table }) => (
      <Checkbox
        checked={
          table.getIsAllPageRowsSelected() ||
          (table.getIsSomePageRowsSelected() && true)
        }
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all"
        className="translate-y-[2px] !h-4 !w-4"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onClick={(e) => {
          e.stopPropagation();
        }}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label="Select row"
        className="translate-y-[2px] !h-4 !w-4"
      />
    ),
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: "title",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Title" />
    ),
    cell: ({ row }) => <TitleCell row={row as TaskRow} />,
    enableSorting: true,
    sortingFn: (rowA, rowB) => {
      const a = rowA.original?.title || "";
      const b = rowB.original?.title || "";
      return a.localeCompare(b);
    },
  },
  {
    accessorKey: "status",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Status" />
    ),
    cell: ({ row }) => {
      const status = row.original?.status;
      const statusObj = TASK_STATUS.find((item) => item.value === status);
      const StatusLabel = statusObj?.label || "No Status";
      const StatusIcon = statusObj?.icon || TASK_STATUS[0].icon;

      return (
        <div className="flex items-center">
          {React.createElement(StatusIcon, {
            className: cn("mr-2 size-4", statusObj?.color),
            "aria-hidden": true,
          })}
          <span>{StatusLabel}</span>
        </div>
      );
    },
    filterFn: (row, columnId, filterValue: string | string[]) =>
      includesString(row.getValue(columnId), filterValue),
    enableSorting: true,
    sortingFn: (rowA, rowB) => {
      const a = rowA.original?.status || "";
      const b = rowB.original?.status || "";
      return a.localeCompare(b);
    },
  },
  {
    accessorKey: "priority",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Priority" />
    ),
    cell: ({ row }) => {
      const priority = row.original?.priority;
      const priorityObj = TASK_PRIORITY.find((item) => item.value === priority);
      const PriorityLabel = priorityObj?.label;
      const PriorityIcon = priorityObj?.icon;

      if (!PriorityIcon) {
        console.error(`No icon found for priority ${priority}`);
        return null;
      }

      return (
        <div className="flex items-center">
          {React.createElement(PriorityIcon, {
            className: cn("mr-2 size-4", priorityObj?.color),
            "aria-hidden": true,
          })}
          <span>{PriorityLabel}</span>
        </div>
      );
    },
    filterFn: (row, columnId, filterValue: string | string[]) =>
      includesString(row.getValue(columnId), filterValue),
    enableSorting: true,
    sortingFn: (rowA, rowB) => {
      const a = rowA.original?.priority || "no_priority";
      const b = rowB.original?.priority || "no_priority";
      return (priorityOrder[a] ?? 0) - (priorityOrder[b] ?? 0);
    },
  },
  {
    accessorKey: "dueDate",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Date Due" />
    ),
    cell: ({ row }) => {
      const dateDue = row.original?.dueDate
        ? new Date(row.original.dueDate)
        : null;

      const formatDueDate = (date: Date) => {
        const today = new Date();
        const tomorrow = new Date(today);
        tomorrow.setDate(tomorrow.getDate() + 1);
        const yesterday = new Date(today);
        yesterday.setDate(yesterday.getDate() - 1);

        today.setHours(0, 0, 0, 0);
        tomorrow.setHours(0, 0, 0, 0);
        yesterday.setHours(0, 0, 0, 0);
        const compareDate = new Date(date);
        compareDate.setHours(0, 0, 0, 0);

        if (compareDate.getTime() === today.getTime()) return "Today";
        if (compareDate.getTime() === tomorrow.getTime()) return "Tomorrow";
        if (compareDate.getTime() === yesterday.getTime()) return "Yesterday";

        const diffInDays = Math.ceil(
          (compareDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24),
        );

        if (diffInDays > 0 && diffInDays <= 7) return "Next week";

        if (compareDate.getFullYear() === today.getFullYear()) {
          return format(date, "MMM d");
        }
        return format(date, "MMM d yyyy");
      };

      const getDateColor = (date: Date) => {
        const today = new Date();
        const compareDate = new Date(date);

        today.setHours(0, 0, 0, 0);
        compareDate.setHours(0, 0, 0, 0);

        if (compareDate.getTime() === today.getTime()) return "text-yellow-500";
        if (
          compareDate.getTime() < today.getTime() &&
          row.original?.status !== "done"
        )
          return "text-red-500";
        return "";
      };

      return (
        <div className="flex items-center cursor-default">
          <span className={`ml-2 ${dateDue ? getDateColor(dateDue) : ""}`}>
            {dateDue ? formatDueDate(dateDue) : ""}
          </span>
        </div>
      );
    },
    enableSorting: true,
    sortingFn: (rowA, rowB) => {
      const a = rowA.original?.dueDate
        ? new Date(rowA.original.dueDate).getTime()
        : 0;
      const b = rowB.original?.dueDate
        ? new Date(rowB.original.dueDate).getTime()
        : 0;
      return a - b;
    },
  },
  {
    accessorKey: "related",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Related" />
    ),
    cell: ({ row }) => {
      const related = row.original?.related;
      if (!related || related.length === 0) return null;
      return <RelatedCell row={row as TaskRow} />;
    },
  },
  {
    accessorKey: "assignee",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Assigned to" />
    ),
    cell: ({ row }) => <AssigneeCell row={row} />,
    filterFn: (row, columnId, filterValue: string | string[]) => {
      const assignee = row.original?.assignee;
      return assignee && "_id" in assignee
        ? filterValue.includes((assignee as { _id: string })._id)
        : false;
    },
    enableSorting: true,
    sortingFn: (rowA, rowB) => {
      const aName =
        (rowA.original?.assignee as { name?: string } | undefined)?.name || "";
      const bName =
        (rowB.original?.assignee as { name?: string } | undefined)?.name || "";
      return aName.localeCompare(bName);
    },
  },
  {
    accessorKey: "_creationTime",

    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Created" />
    ),
    cell: ({ row }) => {
      const createdAt = row.original?._creationTime
        ? new Date(row.original._creationTime)
        : null;
      return (
        <div className="flex items-center cursor-default">
          <span className="ml-2">
            {createdAt
              ? formatDistanceToNow(createdAt, { addSuffix: true })
              : ""}
          </span>
        </div>
      );
    },
    enableSorting: true,
    sortingFn: (rowA, rowB) => {
      const a = rowA.original?._creationTime || 0;
      const b = rowB.original?._creationTime || 0;
      return a - b;
    },
  },
  {
    id: "_id",
    accessorKey: "_id",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="ID" />
    ),
    cell: ({ row }) => (
      <span className="font-mono text-xs bg-zinc-100 dark:bg-zinc-800 px-2 py-1 rounded">{row.original._id}</span>
    ),
    enableSorting: true,
    enableColumnFilter: true,
    filterFn: (row, columnId, filterValue: string | string[]) =>
      includesString(row.getValue(columnId), filterValue),
    meta: {
      label: "ID",
      placeholder: "Search by ID...",
      variant: "text",
    },
  },
];
