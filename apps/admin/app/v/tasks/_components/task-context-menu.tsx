import React from "react";
import { Task } from "@workspace/backend/convex/lib/types";
import { ContextMenu, ContextMenuItemType } from "@/components/context-menu";
import { TASK_STATUS, TASK_PRIORITY } from "@/lib/constants";
import {
  IconAntennaBars5,
  IconEdit,
  IconProgress,
  IconStar,
  IconStarFilled,
  IconTrash,
} from "@tabler/icons-react";

interface TaskContextMenuProps {
  children: React.ReactNode;
  task: Task;
  isFavorite: boolean;
  onEdit: (e: React.MouseEvent, taskId: string) => void;
  onFavorite: (e: React.MouseEvent, taskId: string) => void;
  onStatusChange?: (taskId: string, newStatus: Task["status"]) => void;
  onPriorityChange?: (taskId: string, newPriority: Task["priority"]) => void;
  onDelete: (e: React.MouseEvent, taskId: string) => void;
}

export function TaskContextMenu({
  children,
  task,
  isFavorite,
  onEdit,
  onFavorite,
  onStatusChange,
  onPriorityChange,
  onDelete,
}: TaskContextMenuProps) {
  const menuItems: (ContextMenuItemType | "separator")[] = [
    {
      id: "edit",
      label: "Edit",
      icon: IconEdit,
      onClick: (e) => onEdit(e, task._id),
    },
    {
      id: "favorite",
      label: isFavorite ? "Remove from favorites" : "Add to favorites",
      icon: isFavorite ? IconStarFilled : IconStar,
      iconClassName: isFavorite ? "text-yellow-400" : "",
      onClick: (e) => onFavorite(e, task._id),
    },
    "separator",
    {
      id: "status",
      label: "Status",
      icon: IconProgress,
      indicator: {
        show: true,
        color: TASK_STATUS.find((s) => s.value === task.status)?.color,
      },
      items: TASK_STATUS.map((status) => ({
        id: `status-${status.value}`,
        label: status.label,
        icon: status.icon,
        iconClassName: status.color,
        onClick: () => onStatusChange?.(task._id, status.value),
        indicator: {
          show: task.status === status.value,
          color: status.color,
        },
      })),
    },
    {
      id: "priority",
      label: "Priority",
      icon: IconAntennaBars5,
      indicator: {
        show: task.priority && task.priority !== "no_priority",
        color: TASK_PRIORITY.find((p) => p.value === task.priority)?.color,
      },
      items: TASK_PRIORITY.map((priority) => ({
        id: `priority-${priority.value}`,
        label: priority.label,
        icon: priority.icon,
        iconClassName: priority.color,
        onClick: () => onPriorityChange?.(task._id, priority.value),
        indicator: {
          show: task.priority === priority.value,
          color: priority.color,
        },
      })),
    },
    "separator",
    {
      id: "delete",
      label: "Delete task",
      icon: IconTrash,
      iconClassName: "text-red-400",
      onClick: (e) => onDelete(e, task._id),
    },
  ];

  return <ContextMenu items={menuItems}>{children}</ContextMenu>;
}
