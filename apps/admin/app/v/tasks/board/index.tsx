"use client";

import { use<PERSON><PERSON><PERSON>, useState, useEffect, useMemo } from "react";
import {
  DndContext,
  DragEndEvent,
  DragOverlay,
  DragStartEvent,
  MouseSensor,
  TouchSensor,
  useSensor,
  useSensors,
  DragOverEvent,
  MeasuringStrategy,
} from "@dnd-kit/core";
import { SortableContext, arrayMove } from "@dnd-kit/sortable";
import { Task } from "@workspace/backend/convex/lib/types";
import { TASK_STATUS, TaskStatus } from "@/lib/constants";
import { useQuery, useMutation, usePreloadedQuery } from "convex/react";
import { api } from "@workspace/backend/convex/_generated/api";
import { KanbanColumn } from "./_components/column";
import { KanbanCard } from "./_components/card";
import { useParams } from "next/navigation";
import { Id } from "@workspace/backend/convex/_generated/dataModel";
import BottomBarTasks from "@/components/tasks/tasks-bottom-bar";
import { CreateTaskModal } from "@/components/tasks/create-task-modal";
import { Table } from "@tanstack/react-table";
import confetti from "canvas-confetti";
import { usePreloadedData } from "@/hooks/use-preloaded-data";

interface TaskToEdit {
  _id: Id<"tasks">;
  title: string;
  description?: string;
  status: Task["status"];
  priority: Task["priority"];
  dueDate?: number;
  assignee?: { _id: Id<"users">; name: string } | null;
  related?: {
    _id: string;
    name: string | undefined;
    recordType: string | undefined;
  } | null;
}

interface TasksBoardProps {
  table: Table<Task>;
  statusVisibility: Record<TaskStatus, boolean>;
  onStatusVisibilityChange: (status: TaskStatus, visible: boolean) => void;
}

export default function TasksBoard({
  table,
  statusVisibility,
  onStatusVisibilityChange,
}: TasksBoardProps) {
  const params = useParams();
  const { preloadedUser } = usePreloadedData();
  const user = usePreloadedQuery(preloadedUser);
  const tasks = useQuery(api.tasks.getAll, { favorites: false }) || [];
  const [activeTask, setActiveTask] = useState<Task | null>(null);
  const [selectedTasks, setSelectedTasks] = useState<Set<string>>(new Set());
  const [selectedTaskObjects, setSelectedTaskObjects] = useState<Task[]>([]);
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [defaultStatus, setDefaultStatus] = useState<Task["status"] | null>(
    null,
  );
  const [taskToEdit, setTaskToEdit] = useState<TaskToEdit | null>(null);
  const updateTask = useMutation(api.tasks.update);
  const deleteTask = useMutation(api.tasks.deleteTask);

  const processedTasks = useMemo(() => {
    const columnFilters = table.getState().columnFilters;
    const sorting = table.getState().sorting[0];

    let result = tasks.filter((task) => {
      if (!statusVisibility[task.status]) {
        return false;
      }

      return columnFilters.every((filter) => {
        const value = filter.value;

        if (filter.id === "title" && typeof value === "string") {
          return task.title.toLowerCase().includes(value.toLowerCase());
        }

        if (filter.id === "status" && Array.isArray(value)) {
          return value.length === 0 || value.includes(task.status);
        }

        if (filter.id === "priority" && Array.isArray(value)) {
          return value.length === 0 || value.includes(task.priority);
        }

        if (filter.id === "assignee" && Array.isArray(value)) {
          return (
            value.length === 0 ||
            (task.assignee && value.includes(task.assignee._id))
          );
        }

        return true;
      });
    });

    if (sorting) {
      result = [...result].sort((a, b) => {
        let comparison = 0;

        switch (sorting.id) {
          case "title":
            comparison = (a.title || "").localeCompare(b.title || "");
            break;
          case "dueDate":
            comparison = (a.dueDate || 0) - (b.dueDate || 0);
            break;
          case "priority":
            const priorityOrder = {
              high: 3,
              medium: 2,
              low: 1,
              no_priority: 0,
            };
            comparison =
              (priorityOrder[a.priority as keyof typeof priorityOrder] || 0) -
              (priorityOrder[b.priority as keyof typeof priorityOrder] || 0);
            break;
          case "assignee":
            comparison = (a.assignee?.name || "").localeCompare(
              b.assignee?.name || "",
            );
            break;
          case "createdAt":
            comparison = (a._creationTime || 0) - (b._creationTime || 0);
            break;
          default:
            comparison = 0;
        }

        return sorting.desc ? -comparison : comparison;
      });
    }

    result = result.sort((a, b) => {
      if (a.status === b.status) {
        return (a.position ?? 0) - (b.position ?? 0);
      }
      return 0;
    });

    return result;
  }, [
    tasks,
    table.getState().columnFilters,
    table.getState().sorting,
    statusVisibility,
  ]);

  const columnPreferences = useQuery(api.columnPreferences.getAll) || [];

  const columnPrefsMap = useMemo(() => {
    const prefsMap: Record<string, any> = {
      backlog: {
        trackTimeInStatus: false,
        showConfetti: false,
        hidden: false,
        targetTimeInStatus: null,
      },
      todo: {
        trackTimeInStatus: false,
        showConfetti: false,
        hidden: false,
        targetTimeInStatus: null,
      },
      in_progress: {
        trackTimeInStatus: false,
        showConfetti: false,
        hidden: false,
        targetTimeInStatus: null,
      },
      review: {
        trackTimeInStatus: false,
        showConfetti: false,
        hidden: false,
        targetTimeInStatus: null,
      },
      done: {
        trackTimeInStatus: false,
        showConfetti: false,
        hidden: false,
        targetTimeInStatus: null,
      },
    };

    columnPreferences.forEach((pref) => {
      if (pref.column in prefsMap) {
        prefsMap[pref.column] = pref;
      }
    });

    return prefsMap;
  }, [columnPreferences]);

  const handleSelectTask = useCallback((taskId: string) => {
    setSelectedTasks((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(taskId)) {
        newSet.delete(taskId);
      } else {
        newSet.add(taskId);
      }
      return newSet;
    });
  }, []);

  const handleEditTask = useCallback(
    (taskId: string) => {
      if (!tasks || tasks.length === 0) {
        console.warn("Tasks not loaded yet");
        return;
      }

      const task = tasks.find((t) => t._id === taskId);
      if (!task) {
        console.warn("Task not found:", taskId);
        return;
      }

      try {
        const formattedTask: TaskToEdit = {
          _id: task._id,
          title: task.title,
          description: task.description,
          status: task.status,
          priority: task.priority,
          dueDate: task.dueDate,
          assignee: task.assignee
            ? {
                _id: task.assignee._id,
                name:
                  task.assignee.name ||
                  `${task.assignee.firstName} ${task.assignee.lastName}`.trim(),
              }
            : null,
          related:
            task.related && task.related.length > 0 && task.related[0]?._id
              ? {
                  _id: String(task.related[0]._id),
                  name: task.related[0]?.firstName
                    ? `${task.related[0]?.firstName} ${task.related[0]?.lastName}`.trim()
                    : undefined,
                  recordType: task.related[0]?.type,
                }
              : null,
        };
        setTaskToEdit(formattedTask);
        setIsCreateModalOpen(true);
      } catch (error) {
        console.error("Error formatting task for edit:", error);
      }
    },
    [tasks],
  );

  const handleCreateTask = useCallback((status: Task["status"]) => {
    setTaskToEdit(null);
    setDefaultStatus(status);
    setIsCreateModalOpen(true);
  }, []);

  const handleModalClose = useCallback((open: boolean) => {
    setIsCreateModalOpen(open);
    if (!open) {
      setTaskToEdit(null);
      setDefaultStatus(null);
    }
  }, []);

  useEffect(() => {
    const selectedTasksList = Array.from(selectedTasks)
      .map((taskId) => tasks.find((task) => task._id === taskId))
      .filter((task): task is NonNullable<typeof task> => task !== undefined);

    setSelectedTaskObjects(selectedTasksList as Task[]);
  }, [selectedTasks, tasks]);

  const mouseSensor = useSensor(MouseSensor, {
    activationConstraint: {
      distance: 3,
    },
  });
  const touchSensor = useSensor(TouchSensor, {
    activationConstraint: {
      delay: 100,
      tolerance: 5,
    },
  });
  const sensors = useSensors(mouseSensor, touchSensor);

  const handleDragStart = useCallback(
    (event: DragStartEvent) => {
      const { active } = event;

      const task = tasks.find((t) => t._id === active.id);
      if (task) setActiveTask(task as Task);
    },
    [tasks],
  );

  const handleDragOver = useCallback((event: DragOverEvent) => {
    const { active, over } = event;
  }, []);

  const handleDragEnd = useCallback(
    async (event: DragEndEvent) => {
      const { active, over } = event;

      if (!over) {
        return;
      }

      const activeTask = tasks.find((t) => t._id === active.id);
      if (!activeTask) {
        return;
      }

      const tasksToMove = selectedTasks.has(activeTask._id)
        ? tasks.filter((task) => selectedTasks.has(task._id))
        : [activeTask];

      if (
        !over.id.toString().startsWith("column-") &&
        over.data.current?.type === "task"
      ) {
        const overTask = tasks.find((t) => t._id === over.id);
        if (!overTask) return;

        if (activeTask.status !== overTask.status) {
          const positionedTasks = tasks
            .filter((t) => t.status === overTask.status)
            .sort((a, b) => (a.position ?? 0) - (b.position ?? 0));

          const newPosition = overTask.position ?? 0;

          await Promise.all([
            ...tasksToMove.map((task) =>
              updateTask({
                id: task._id,
                status: overTask.status,
                position: newPosition,
                statusHistory:
                  task.status !== overTask.status
                    ? [
                        ...(task.statusHistory || []),
                        {
                          status: overTask.status,
                          timestamp: Date.now(),
                          userId: task.createdBy,
                        },
                      ]
                    : undefined,
              }),
            ),
            ...positionedTasks
              .filter(
                (t) =>
                  (t.position ?? 0) >= newPosition &&
                  !tasksToMove.some((m) => m._id === t._id),
              )
              .map((task, index) =>
                updateTask({
                  id: task._id,
                  position: newPosition + index + tasksToMove.length,
                }),
              ),
          ]);

          return;
        }

        const columnTasks = tasks
          .filter((t) => t.status === activeTask.status)
          .sort((a, b) => (a.position ?? 0) - (b.position ?? 0));

        const activeIndex = columnTasks.findIndex(
          (t) => t._id === activeTask._id,
        );
        const overIndex = columnTasks.findIndex((t) => t._id === overTask._id);

        if (activeIndex === -1 || overIndex === -1) {
          return;
        }

        const newOrder = arrayMove(columnTasks, activeIndex, overIndex);

        try {
          await Promise.all(
            newOrder.map((task, index) =>
              updateTask({
                id: task._id,
                position: index,
              }),
            ),
          );
          return;
        } catch (error) {
          console.error("[Board Debug] Error updating task positions:", error);
          return;
        }
      }

      let newStatus: Task["status"] | undefined;
      let newPosition: number | undefined;

      if (over.id.toString().startsWith("column-")) {
        newStatus = over.id.toString().replace("column-", "") as Task["status"];
        newPosition =
          Math.max(
            -1,
            ...tasks
              .filter((t) => t.status === newStatus)
              .map((t) => t.position ?? -1),
          ) + 1;
      } else {
        const overTask = tasks.find((t) => t._id === over.id);
        if (overTask) {
          newStatus = overTask.status;
          newPosition = overTask.position;
        }
      }

      if (!newStatus) {
        return;
      }

      try {
        await Promise.all(
          tasksToMove.map((task) => {
            if (task.status !== newStatus) {
              return updateTask({
                id: task._id,
                status: newStatus,
                position: newPosition,
                statusHistory: [
                  ...(task.statusHistory || []),
                  {
                    status: newStatus,
                    timestamp: Date.now(),
                    userId: task.createdBy,
                  },
                ],
              });
            }
          }),
        );

        const columnPrefs = columnPrefsMap[newStatus];

        if (columnPrefs?.showConfetti) {
          const colors = ["#EE342F", "#F3B01C", "#8D2676"];
          const defaultOpts = {
            particleCount: 50,
            colors: colors,
            spread: 90,
            startVelocity: 45,
            decay: 0.92,
            scalar: 1,
          };

          const fireCorner = (x: number, y: number, opts: any = {}) => {
            confetti({
              ...defaultOpts,
              ...opts,
              origin: { x, y },
            });
          };

          fireCorner(0, 0);
          fireCorner(0.2, 0);

          fireCorner(0.8, 0);
          fireCorner(1, 0);

          fireCorner(0, 1);
          fireCorner(0.2, 1);

          fireCorner(0.8, 1);
          fireCorner(1, 1);

          confetti({
            ...defaultOpts,
            origin: { x: 0.5, y: 0.5 },
            particleCount: 100,
            spread: 360,
          });
        }
      } catch (error) {
        console.error("[Board Debug] Error updating task:", error);
      }

      setActiveTask(null);
    },
    [tasks, updateTask, selectedTasks, columnPrefsMap],
  );

  const handleStatusChange = useCallback(
    async (taskId: string, newStatus: Task["status"]) => {
      const task = tasks.find((t) => t._id === taskId);
      if (!task || task.status === newStatus) return;

      await updateTask({
        id: task._id,
        status: newStatus,
        statusHistory: [
          ...(task.statusHistory || []),
          {
            status: newStatus,
            timestamp: Date.now(),
            userId: task.createdBy,
          },
        ],
      });
    },
    [tasks, updateTask],
  );

  const handlePriorityChange = useCallback(
    async (taskId: string, newPriority: Task["priority"]) => {
      const task = tasks.find((t) => t._id === taskId);
      if (!task || task.priority === newPriority) return;

      await updateTask({
        id: task._id,
        priority: newPriority,
      });
    },
    [tasks, updateTask],
  );

  const handleDeleteTask = useCallback(
    async (taskId: string) => {
      setSelectedTasks((prev) => {
        const newSet = new Set(prev);
        newSet.delete(taskId);
        return newSet;
      });

      await deleteTask({ id: taskId as Id<"tasks"> });
    },
    [tasks, deleteTask],
  );

  return (
    <div className="h-full p-4">
      <DndContext
        sensors={sensors}
        onDragStart={handleDragStart}
        onDragOver={handleDragOver}
        onDragEnd={handleDragEnd}
        measuring={{
          droppable: {
            strategy: MeasuringStrategy.Always,
          },
        }}
      >
        <div className="grid grid-cols-5 gap-2 h-[calc(100vh-10rem)] mx-auto w-full overflow-x-auto overflow-y-none">
          {TASK_STATUS.map(
            (status) =>
              statusVisibility[status.value] && (
                <KanbanColumn
                  key={status.value}
                  id={`column-${status.value}`}
                  title={status.label}
                  icon={status.icon}
                  color={status.color}
                  tasks={
                    processedTasks.filter(
                      (task) => task.status === status.value,
                    ) as Task[]
                  }
                  selectedTasks={selectedTasks}
                  onSelectTask={handleSelectTask}
                  onStatusChange={handleStatusChange}
                  onPriorityChange={handlePriorityChange}
                  onEdit={handleEditTask}
                  onCreateTask={handleCreateTask}
                  onDelete={handleDeleteTask}
                  onStatusVisibilityChange={onStatusVisibilityChange}
                />
              ),
          )}
        </div>

        <DragOverlay>
          {activeTask && (
            <>
              {selectedTasks.has(activeTask._id) && selectedTasks.size > 1 ? (
                <div className="relative space-y-2">
                  <div className="relative rounded-lg border border-blue-500 bg-zinc-100 dark:bg-accent/50 rotate-2 shadow-lg">
                    <KanbanCard task={activeTask} />
                  </div>
                  <div className="absolute inset-0 -rotate-2 rounded-lg border border-blue-500 bg-zinc-100 dark:bg-accent/50 opacity-70 -translate-y-1" />
                  {selectedTasks.size > 2 && (
                    <div className="absolute inset-0 rotate-1 rounded-lg border border-blue-500 bg-zinc-100 dark:bg-accent/50 opacity-50 -translate-y-2" />
                  )}
                </div>
              ) : (
                <div className="relative rounded-lg border border-blue-500 bg-zinc-100 dark:bg-accent/50 rotate-2 shadow-lg">
                  <KanbanCard task={activeTask} />
                </div>
              )}
            </>
          )}
        </DragOverlay>
      </DndContext>

      <BottomBarTasks
        selectedTasks={selectedTaskObjects}
        onDeselectAll={() => {
          setSelectedTasks(new Set());
          setSelectedTaskObjects([]);
        }}
      />

      <CreateTaskModal
        open={isCreateModalOpen}
        onOpenChange={handleModalClose}
        defaultStatus={defaultStatus as Task["status"] | undefined}
        taskToEdit={taskToEdit as TaskToEdit | undefined}
      />
    </div>
  );
}
