"use client";

import React, { useState, useMemo } from "react";
import { useSortable } from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import { DragOverlay, useDndContext } from "@dnd-kit/core";
import { Task, User } from "@workspace/backend/convex/lib/types";
import { format } from "date-fns";
import { UserAvatar } from "@workspace/ui/components/user-avatar";
import { Checkbox } from "@workspace/ui/components/checkbox";
import { useCallback } from "react";
import { useQuery, useMutation } from "convex/react";
import { api } from "@workspace/backend/convex/_generated/api";
import { Id } from "@workspace/backend/convex/_generated/dataModel";
import { differenceInDays } from "date-fns";
import { TASK_PRIORITY } from "@/lib/constants";
import {
  IconClock,
  IconStar,
  IconStarFilled,
  IconEdit,
} from "@tabler/icons-react";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>ontent,
  Too<PERSON><PERSON><PERSON>rigger,
} from "@workspace/ui/components/tooltip";
import { Button } from "@workspace/ui/components/button";
import { TASK_STATUS } from "@/lib/constants";
import DropIndicator from "./drop-indicator";
import { TaskContextMenu } from "../../_components/task-context-menu";
import { PrioritySelector } from "@/components/tasks/priority-selector";
import { StatusSelector } from "@/components/tasks/status-selector";

interface KanbanCardProps {
  task: Task;
  isSelected?: boolean;
  selectedTasks?: Set<string>;
  onSelect?: (taskId: string) => void;
  onEdit?: (taskId: string) => void;
  onDelete?: (taskId: string) => void;
  icon?: React.ReactNode;
  onStatusChange?: (taskId: string, newStatus: Task["status"]) => void;
  onPriorityChange?: (taskId: string, newPriority: Task["priority"]) => void;
  columnPrefs?: {
    trackTimeInStatus: boolean;
    targetTimeInStatus: number | null;
  };
}

export function KanbanCard({
  task,
  isSelected = false,
  selectedTasks = new Set(),
  onSelect,
  onEdit,
  icon,
  onStatusChange,
  onPriorityChange,
  onDelete,
  columnPrefs,
}: KanbanCardProps) {
  const user = useQuery(api.users.viewer);
  const [isHovered, setIsHovered] = useState(false);
  const toggleFavorite = useMutation(api.favorites.toggle);
  const favorites = useQuery(api.favorites.get);
  const isFavorite = favorites?.some((f) => f?.objectId === task._id);

  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
    isOver,
  } = useSortable({
    id: task._id,
    data: {
      type: "task",
      task: task,
      status: task.status,
      position: task.position,
      selectedTasksCount: isSelected ? selectedTasks.size : 1,
    },
    transition: {
      duration: 150,
      easing: "cubic-bezier(0.25, 1, 0.5, 1)",
    },
  });

  const style = useMemo(() => {
    const transformString = transform ? CSS.Transform.toString(transform) : "";
    const styleObj: React.CSSProperties = {
      transform: isDragging
        ? `rotate(2deg) ${transformString}`
        : transformString,
      transition,
      opacity: isDragging ? 0.5 : task.status === "done" ? 0.5 : 1,
      cursor: isDragging ? "grabbing" : "grab",
      touchAction: "none",
      position: "relative",
      zIndex: isDragging ? 100 : 1,
      display: "block",
    };

    return styleObj;
  }, [transform, transition, isDragging, task.status]);

  const handleCheckboxChange = useCallback(
    (checked: boolean | string) => {
      onSelect?.(task._id);
    },
    [onSelect, task._id],
  );

  const handleFavorite = useCallback(
    (e: React.MouseEvent, taskId: string) => {
      e.stopPropagation();
      toggleFavorite({
        objectId: taskId as Id<"tasks">,
      });
    },
    [toggleFavorite],
  );

  const handleEditClick = useCallback(
    (e: React.MouseEvent) => {
      e.stopPropagation();
      onEdit?.(task._id);
    },
    [onEdit, task._id],
  );

  const handleCardClick = useCallback(
    (e: React.MouseEvent) => {
      if (
        (e.target as HTMLElement).closest(".checkbox-container") ||
        (e.target as HTMLElement).closest(".edit-button-container") ||
        (e.target as HTMLElement).closest(".priority-selector-container") ||
        (e.target as HTMLElement).closest(".status-selector-container")
      ) {
        return;
      }
      handleCheckboxChange(true);
    },
    [handleCheckboxChange],
  );

  const statusChangeInfo = useMemo(() => {
    const lastStatusChange = task.statusHistory?.find(
      (h) => h.status === task.status,
    );
    if (!lastStatusChange) return null;

    const days = differenceInDays(Date.now(), lastStatusChange.timestamp);
    const daysText = `${days}d`;
    const columnName =
      TASK_STATUS.find((s) => s.value === task.status)?.label || task.status;
    const fullDate = format(lastStatusChange.timestamp, "h:mm a MMMM d, yyyy");

    return {
      days,
      daysText,
      lastStatusChange,
      tooltipText: (
        <div className="flex flex-col">
          <span>Moved to {columnName}</span>
          <span className="text-xs opacity-80">{fullDate}</span>
          {columnPrefs?.trackTimeInStatus &&
            columnPrefs?.targetTimeInStatus &&
            days > columnPrefs.targetTimeInStatus && (
              <span className="text-xs text-red-500 font-medium">
                Exceeded target by {days - columnPrefs.targetTimeInStatus} days
              </span>
            )}
        </div>
      ),
    };
  }, [task.status, task.statusHistory, columnPrefs]);

  const isOverdue = useMemo(() => {
    if (
      !columnPrefs?.trackTimeInStatus ||
      !columnPrefs?.targetTimeInStatus ||
      !statusChangeInfo?.lastStatusChange
    ) {
      return false;
    }
    const daysInStatus = differenceInDays(
      Date.now(),
      statusChangeInfo.lastStatusChange.timestamp,
    );
    return daysInStatus > columnPrefs.targetTimeInStatus;
  }, [columnPrefs, statusChangeInfo]);

  const SelectionComponent = useMemo(() => {
    if (!user) return null;

    if (isHovered || isSelected) {
      return (
        <Checkbox
          checked={isSelected}
          onCheckedChange={handleCheckboxChange}
          className="translate-y-[2px] cursor-pointer"
        />
      );
    }

    return <UserAvatar user={task.assignee as User} size="sm" />;
  }, [user, isHovered, isSelected, handleCheckboxChange]);

  return (
    <>
      {isOver && (
        <DropIndicator
          beforeId={task._id}
          column={task.status}
          isOver={isOver}
        />
      )}
      <TaskContextMenu
        task={task}
        isFavorite={isFavorite ?? false}
        onEdit={handleEditClick}
        onFavorite={handleFavorite}
        onStatusChange={onStatusChange}
        onPriorityChange={onPriorityChange}
        onDelete={(e, taskId) => onDelete?.(taskId)}
      >
        <div
          ref={setNodeRef}
          style={style}
          {...attributes}
          {...listeners}
          onMouseEnter={() => setIsHovered(true)}
          onMouseLeave={() => setIsHovered(false)}
          onClick={handleCardClick}
          className={`relative rounded-lg border bg-zinc-100 dark:bg-accent/50 dark:hover:hover-bg hover:bg-zinc-100 p-2 ${
            isDragging
              ? "border-blue-500"
              : isSelected
                ? "border-blue-500"
                : isOverdue
                  ? "border-red-500 bg-red-500/50 dark:bg-red-500/10"
                  : "border-accent"
          }`}
        >
          <div className={"flex flex-col"}>
            <div className={"flex flex-row items-start justify-between"}>
              <div className={"flex flex-row items-center gap-2"}>
                <div className={"flex flex-col"}>
                  {task.dueDate && (
                    <div className={"text-sm text-zinc-500"}>
                      {format(new Date(task.dueDate), "MMM dd")}
                    </div>
                  )}
                  <p className="text-sm text-zinc-800 dark:text-zinc-100 gap-2 flex flex-row items-center">
                    {task.title}
                  </p>
                </div>
              </div>
              <div className="checkbox-container">{SelectionComponent}</div>
            </div>
            <div className={"flex flex-row items-center gap-1 mt-1"}>
              <div className="status-selector-container">
                <StatusSelector
                  status={task.status}
                  taskId={task._id}
                  onStatusChange={onStatusChange}
                />
              </div>

              <div className="priority-selector-container">
                <PrioritySelector
                  priority={task.priority}
                  taskId={task._id}
                  onPriorityChange={onPriorityChange}
                />
              </div>

              <div className="ml-auto flex items-center gap-1 edit-button-container">
                <div className="flex items-center gap-1">
                  {isHovered && (
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button
                          onClick={(e) => {
                            e.stopPropagation();
                            onEdit?.(task._id);
                          }}
                          className="h-6 w-6 text-zinc-500 hover:text-zinc-600 dark:hover:text-zinc-300 transition-colors"
                          variant="ghost"
                          size="icon"
                        >
                          <IconEdit className="w-4 h-4" />
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent side="bottom">Edit task</TooltipContent>
                    </Tooltip>
                  )}

                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        onClick={(e) => handleFavorite(e, task._id)}
                        className="h-6 w-6 text-zinc-500 hover:text-yellow-400 transition-colors"
                        variant="ghost"
                        size="icon"
                      >
                        {isFavorite ? (
                          <IconStarFilled className="w-4 h-4 text-yellow-400" />
                        ) : (
                          isHovered && <IconStar className="w-4 h-4" />
                        )}
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent side="bottom">
                      {isFavorite
                        ? "Remove from favorites"
                        : "Add to favorites"}
                    </TooltipContent>
                  </Tooltip>
                </div>

                {statusChangeInfo && (
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <div className="flex items-center gap-1 text-xs text-zinc-500 cursor-default">
                        <IconClock className="w-3 h-3" />
                        <span>{statusChangeInfo.daysText}</span>
                      </div>
                    </TooltipTrigger>
                    <TooltipContent side="bottom">
                      {statusChangeInfo.tooltipText}
                    </TooltipContent>
                  </Tooltip>
                )}
              </div>
            </div>
          </div>
        </div>
      </TaskContextMenu>
    </>
  );
}

export function KanbanCardDragOverlay() {
  const { active } = useDndContext();

  if (!active) return null;

  const task = active.data.current?.task;
  const selectedTasksCount = active.data.current?.selectedTasksCount || 1;

  return (
    <DragOverlay>
      {task && (
        <div
          className="relative rounded-lg border-blue-500 bg-zinc-100 dark:bg-accent/50 p-2 shadow-lg"
          style={{
            borderWidth: `${Math.min(selectedTasksCount * 2, 8)}px`,
            transform: `translateY(${(selectedTasksCount - 1) * -4}px)`,
          }}
        >
          <KanbanCard task={task} />
        </div>
      )}
    </DragOverlay>
  );
}
