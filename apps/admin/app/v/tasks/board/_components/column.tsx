"use client";

import { useDroppable } from "@dnd-kit/core";
import {
  SortableContext,
  verticalListSortingStrategy,
} from "@dnd-kit/sortable";
import { Task } from "@workspace/backend/convex/lib/types";
import {
  IconClock,
  IconConfetti,
  IconDotsVertical,
  IconEyeOff,
  IconPlus,
} from "@tabler/icons-react";
import { KanbanCard } from "./card";
import { Button } from "@workspace/ui/components/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@workspace/ui/components/dropdown-menu";
import { Switch } from "@workspace/ui/components/switch";
import { useMutation, usePreloadedQuery, useQuery } from "convex/react";
import { api } from "@workspace/backend/convex/_generated/api";
import { Id } from "@workspace/backend/convex/_generated/dataModel";
import { usePreloadedData } from "@/hooks/use-preloaded-data";
import { Input } from "@workspace/ui/components/input";
import { useCallback, useState, useMemo, useEffect } from "react";
import { useDebounce } from "@/hooks/use-debounce";

interface KanbanColumnProps {
  id: string;
  title: string;
  icon: React.ComponentType<any>;
  color: string;
  tasks: Task[];
  selectedTasks: Set<string>;
  onSelectTask: (taskId: string) => void;
  onStatusChange?: (taskId: string, newStatus: Task["status"]) => void;
  onPriorityChange?: (taskId: string, newPriority: Task["priority"]) => void;
  onEdit?: (taskId: string) => void;
  onCreateTask?: (status: Task["status"]) => void;
  onDelete?: (taskId: string) => void;
  onStatusVisibilityChange?: (status: Task["status"], visible: boolean) => void;
}

export function KanbanColumn({
  id,
  title,
  icon: Icon,
  color,
  tasks,
  selectedTasks,
  onSelectTask,
  onStatusChange,
  onPriorityChange,
  onEdit,
  onCreateTask,
  onDelete,
  onStatusVisibilityChange,
}: KanbanColumnProps) {
  const { setNodeRef, isOver, active } = useDroppable({
    id,
    data: {
      type: "column",
      status: id.replace("column-", ""),
    },
  });

  const [isHovered, setIsHovered] = useState(false);

  const columnClassName = `p-4 flex flex-col h-full overflow-y-auto rounded-xl border ${
    isOver
      ? "border-blue-500 bg-blue-50 dark:bg-blue-900/10"
      : "border-zinc-200 bg-white dark:border-zinc-800 dark:bg-zinc-900/50"
  } text-zinc-950 shadow-sm dark:text-zinc-50`;

  const status = id.replace("column-", "") as Task["status"];
  const updateColumnPrefs = useMutation(api.columnPreferences.update);
  const { preloadedUser } = usePreloadedData();
  const user = usePreloadedQuery(preloadedUser);

  const columnPrefs = useQuery(api.columnPreferences.get, {
    column: status,
  }) || {
    trackTimeInStatus: false,
    showConfetti: false,
    hidden: false,
    targetTimeInStatus: null,
  };

  const sortedTasks = useMemo(() => tasks, [tasks]);

  const sortableItems = useMemo(
    () => sortedTasks.map((task) => task._id),
    [sortedTasks],
  );

  const [localTargetTime, setLocalTargetTime] = useState<number | null>(
    columnPrefs.targetTimeInStatus ?? null,
  );

  const updateTargetTime = useCallback(
    (value: number | null) => {
      updateColumnPrefs({
        column: status,
        targetTimeInStatus: value ?? undefined,
      });
    },
    [status, updateColumnPrefs],
  );

  const debouncedUpdateTargetTime = useDebounce(updateTargetTime, 500);

  const handlePreferenceChange = useCallback(
    (key: string, value: boolean | number) => {
      if (key === "targetTimeInStatus") {
        setLocalTargetTime(value as number);
        debouncedUpdateTargetTime(value as number);
      } else {
        updateColumnPrefs({
          column: status,
          [key]: value,
        });
      }
    },
    [status, updateColumnPrefs, debouncedUpdateTargetTime],
  );

  const showColumn = tasks.length > 0 || isOver || active;

  return (
    <div
      ref={setNodeRef}
      className={columnClassName}
      data-droppable-id={id}
      data-status={status}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <div className="flex items-center justify-between gap-2 mb-4">
        <div className="flex items-center gap-2">
          <Icon
            className={`flex items-center justify-center w-4 h-4 ${color}`}
          />
          <h2 className="text-xs font-light">{title}</h2>
          {tasks.length > 0 && (
            <span className="rounded-sm bg-zinc-100 dark:bg-zinc-900 px-1 py-0 text-xs font-medium border border-input font-mono">
              {tasks.length}
            </span>
          )}
        </div>
        <div className="flex items-center gap-y-1">
          <>
            <Button
              variant="ghost"
              size="icon"
              className="h-6 w-6"
              onClick={() => onCreateTask?.(status)}
            >
              <IconPlus className="h-4 w-4" />
            </Button>

            <DropdownMenu>
              <DropdownMenuTrigger asChild className="h-4 w-4">
                <Button variant="ghost" size="icon" className="h-6 w-6">
                  <IconDotsVertical className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-56">
                <div className="flex items-center justify-between px-2 py-2">
                  <div className="flex items-center gap-2">
                    <IconClock className="w-4 h-4" />
                    <span className="text-sm">Track time in status</span>
                  </div>
                  <Switch
                    checked={columnPrefs.trackTimeInStatus}
                    onCheckedChange={(checked) =>
                      handlePreferenceChange("trackTimeInStatus", checked)
                    }
                  />
                </div>
                {columnPrefs.trackTimeInStatus && (
                  <div className="px-2 py-2">
                    <Input
                      type="number"
                      placeholder="Target days in status"
                      className="w-full px-2 py-1 text-sm border rounded-lg"
                      value={localTargetTime || ""}
                      onChange={(e) =>
                        handlePreferenceChange(
                          "targetTimeInStatus",
                          parseInt(e.target.value) || 0,
                        )
                      }
                    />
                  </div>
                )}
                <DropdownMenuSeparator />
                <div className="flex items-center justify-between px-2 py-2">
                  <div className="flex items-center gap-2">
                    <IconConfetti className="w-4 h-4" />
                    <span className="text-sm">Show confetti</span>
                  </div>
                  <Switch
                    checked={columnPrefs.showConfetti}
                    onCheckedChange={(checked) =>
                      handlePreferenceChange("showConfetti", checked)
                    }
                  />
                </div>
                <DropdownMenuSeparator />
                <DropdownMenuItem
                  onClick={() => onStatusVisibilityChange?.(status, false)}
                >
                  <IconEyeOff className="w-4 h-4 mr-2" />
                  Hide column
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </>
        </div>
      </div>

      <div
        className="space-y-2 min-h-[200px] flex-grow relative rounded-lg overflow-y-auto"
        style={{
          transition: "background-color 150ms ease",
        }}
      >
        {showColumn ? (
          <SortableContext
            items={sortableItems}
            strategy={verticalListSortingStrategy}
          >
            <div className="space-y-2">
              {sortedTasks.map((task) => (
                <KanbanCard
                  key={task._id}
                  task={task}
                  isSelected={selectedTasks.has(task._id)}
                  selectedTasks={selectedTasks}
                  onSelect={onSelectTask}
                  icon={<Icon className="h-4 w-4" />}
                  onStatusChange={onStatusChange}
                  onPriorityChange={onPriorityChange}
                  onDelete={onDelete}
                  onEdit={onEdit}
                  columnPrefs={{
                    trackTimeInStatus: columnPrefs.trackTimeInStatus,
                    targetTimeInStatus: columnPrefs.targetTimeInStatus ?? null,
                  }}
                />
              ))}
            </div>
          </SortableContext>
        ) : (
          <div
            className="absolute inset-0 flex items-center justify-center text-zinc-400 dark:text-zinc-600 pointer-events-none"
            aria-hidden="true"
          >
            <div className="text-center">
              <div className="flex justify-center">
                <Icon className={`w-6 h-6 mb-2 opacity-30 ${color}`} />
              </div>
              <p className="text-xs">Drop tasks here</p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
