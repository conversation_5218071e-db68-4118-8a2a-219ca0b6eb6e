"use client";

import { notFound } from "next/navigation";
import { UserViewClient } from "../_components/user-view-client";
import { api } from "@workspace/backend/convex/_generated/api";
import { Id } from "@workspace/backend/convex/_generated/dataModel";
import { useQuery } from "convex/react";
import React from "react";

export default function UserViewPage({ params }: { params: Promise<{ id: string }> }) {
  const { id } = React.use(params);
  const user = useQuery(api.users.ADMIN_getUserById, { userId: id as Id<"users"> });
  if (user === undefined) return <div>Loading...</div>;
  if (!user) return notFound();

  return <UserViewClient user={user} />;
} 