"use client";

import UsersClient from "./client";
import { useEffect, useState } from "react";

const UsersPage = () => {
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    const timer = setTimeout(() => {
      setIsMounted(true);
    }, 0);

    return () => clearTimeout(timer);
  }, []);

  if (!isMounted) {
    return null;
  }

  return <UsersClient />;
};

export default UsersPage;
