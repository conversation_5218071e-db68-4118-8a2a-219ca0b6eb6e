"use client";

import * as React from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Sheet<PERSON>ooter, SheetClose } from "@workspace/ui/components/sheet";
import { Button } from "@workspace/ui/components/button";
import { useMutation } from "convex/react";
import { api } from "@workspace/backend/convex/_generated/api";
import { useRouter } from "next/navigation";

interface InterestedSellerSheetProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  interestedSeller: any;
}

export function InterestedSellerSheet({ open, onOpenChange, interestedSeller }: InterestedSellerSheetProps) {
  const [loading, setLoading] = React.useState<"approve" | "reject" | null>(null);
  const [error, setError] = React.useState<string | null>(null);
  const router = useRouter();
  const approveInterestedSeller = useMutation(api.interestedSellers.approveInterestedSeller);
  const rejectInterestedSeller = useMutation(api.interestedSellers.rejectInterestedSeller);

  if (!interestedSeller) return null;

  async function handleApprove() {
    setLoading("approve");
    setError(null);
    try {
      await approveInterestedSeller({
        interestedSellerId: interestedSeller._id,
        userId: interestedSeller.userId,
      });
      onOpenChange(false);
    } catch (e: any) {
      setError(e.message || "Failed to approve");
    } finally {
      setLoading(null);
    }
  }

  async function handleReject() {
    setLoading("reject");
    setError(null);
    try {
      await rejectInterestedSeller({
        interestedSellerId: interestedSeller._id,
      });
      onOpenChange(false);
      router.refresh();
    } catch (e: any) {
      setError(e.message || "Failed to reject");
    } finally {
      setLoading(null);
    }
  }

  const handleLinkClick = (platform: string, username: string) => {
    if (!username) return;
  
    if (
      username.startsWith('http://') || 
      username.startsWith('https://') || 
      username.endsWith('.com') ||
      username.endsWith('.net') ||
      username.endsWith('.org') ||
      username.endsWith('.io') ||
      username.endsWith('.us')
    ) {
      return username;
    }

    switch (platform.toLowerCase()) {
      case 'amazon':
        return `https://amazon.com/shops/${username}`;
      case 'whatnot':
        return `https://whatnot.com/user/${username}`;
      case 'ebay':
        return `https://ebay.com/usr/${username}`;
      case 'facebook':
        return `https://facebook.com/${username}`;
      case 'poshmark':
        return `https://poshmark.com/closet/${username}`;
      case 'mercari':
        return `https://mercari.com/u/${username}`;
      case 'depop':
        return `https://depop.com/${username}`;
      case 'etsy':
        return `https://etsy.com/shop/${username}`;
      case 'offerup':
        return `https://offerup.com/profile/${username}`;
      case 'tcg-player':
        return `https://www.tcgplayer.com/seller/${username}`;
      default:
        return username;
    }
  }

  const handleMonthlyRevenue = (revenue: string) => {
    return `$${revenue.replace(/\B(?=(\d{3})+(?!\d))/g, ",")}`;
  }

  return (
    <Sheet open={open} onOpenChange={onOpenChange}>
      <SheetContent className="flex flex-col gap-6 sm:max-w-md overflow-y-auto -mt-2 mr-2 border-t rounded-br-xl" overlay={false}>
        <SheetHeader className="text-left">
          <SheetTitle>Interested Seller Application</SheetTitle>
          <SheetDescription>
            This user has submitted an application to become a seller. Below are the details.
          </SheetDescription>
        </SheetHeader>
        <div className="space-y-4 text-sm p-4">
          <div><span className="font-semibold">Email:</span> {interestedSeller.email}</div>
          <div><span className="font-semibold">Has Selling Experience:</span> {interestedSeller.hasSellingExperience ? "Yes" : "No"}</div>
          {interestedSeller.platform && (
            <div><span className="font-semibold">Platform:</span> {interestedSeller.platform}</div>
          )}
          {interestedSeller.platformLink && (
            <div><span className="font-semibold">Platform Link:</span> <a href={handleLinkClick(interestedSeller.platform, interestedSeller.platformLink)} target="_blank" rel="noopener noreferrer" className="text-blue-600 underline">{interestedSeller.platformLink}</a></div>
          )}
          {interestedSeller.monthlyRevenue && (
            <div><span className="font-semibold">Monthly Revenue:</span> {handleMonthlyRevenue(interestedSeller.monthlyRevenue)}</div>
          )}
          {interestedSeller.primaryGoal && (
            <div><span className="font-semibold">Primary Goal:</span> {interestedSeller.primaryGoal}</div>
          )}
          {interestedSeller.socialMedia && interestedSeller.socialMedia.length > 0 && (
            <div>
              <span className="font-semibold">Social Media:</span>
              <ul className="list-disc ml-6">
                {interestedSeller.socialMedia.map((item: any, idx: number) => (
                  <li key={idx}>{item.platform}{item.username ? `: ${item.username}` : ""}</li>
                ))}
              </ul>
            </div>
          )}
          {interestedSeller.additionalInfo && (
            <div><span className="font-semibold">Additional Info:</span> {interestedSeller.additionalInfo}</div>
          )}
          <div><span className="font-semibold">Submitted At:</span> {interestedSeller.submittedAt ? new Date(interestedSeller.submittedAt).toLocaleString() : "-"}</div>
          {interestedSeller.status && (
            <div><span className="font-semibold">Status:</span> {interestedSeller.status}</div>
          )}
          {interestedSeller.notes && (
            <div><span className="font-semibold">Notes:</span> {interestedSeller.notes}</div>
          )}
          {error && <div className="text-red-600 text-sm mt-2">{error}</div>}
        </div>
        <SheetFooter className="gap-2 pt-2 sm:space-x-0 flex flex-row justify-end">
          <Button
            type="button"
            variant="destructive"
            onClick={handleReject}
            disabled={loading !== null}
          >
            {loading === "reject" ? "Rejecting..." : "Reject"}
          </Button>
          <Button
            type="button"
            variant="default"
            onClick={handleApprove}
            disabled={loading !== null}
          >
            {loading === "approve" ? "Approving..." : "Approve & Make Seller"}
          </Button>
        </SheetFooter>
      </SheetContent>
    </Sheet>
  );
} 