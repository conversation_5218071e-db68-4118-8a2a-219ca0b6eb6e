"use client";

import React from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON> } from "@workspace/ui/components/tabs";
import { ColorPicker } from "@/components/color-picker";
import { UserColorPicker } from "@/components/user-color-picker";
import { InterestedSellerSheet } from "./interested-seller-sheet";
import { Button } from "@workspace/ui/components/button";
import Image from "next/image";
import { Select, SelectContent, SelectValue, SelectItem, SelectTrigger } from "@workspace/ui/components/select";
import { useQuery, useMutation } from "convex/react";
import { api } from "@workspace/backend/convex/_generated/api";
import { Id } from "@workspace/backend/convex/_generated/dataModel";
import { Input } from "@workspace/ui/components/input";
import { Textarea } from "@workspace/ui/components/textarea";
import { IconBrandStripe, IconLink } from "@tabler/icons-react";
import { categories, subcategories } from "@workspace/lib/constants/categories";
import { getAvatarImageUrl } from "@/lib/utils";

function getInitials(name?: string, username?: string) {
  const base = name || username || "?";
  const parts = base.split(" ").filter(Boolean);
  if (parts.length === 0) return "?";
  if (parts.length === 1) return parts[0]?.[0] || "?";
  const first = parts[0]?.[0] || "?";
  const last = parts[parts.length - 1]?.[0] || "?";
  return first + last;
}

function formatDate(timestamp?: number) {
  if (!timestamp) return "-";
  return new Date(timestamp).toLocaleDateString();
}

function VerifiedBadge() {
  return <span className="ml-2 px-2 py-1 text-xs rounded bg-blue-100 text-blue-700 font-medium">Verified</span>;
}

export function UserViewClient({ user }: { user: any }) {
  const [isSheetOpen, setSheetOpen] = React.useState(false);
  const [role, setRole] = React.useState(user.role);
  const [editMode, setEditMode] = React.useState(false);
  const [editFields, setEditFields] = React.useState<{
    [key: string]: any;
    name: string;
    email: string;
    phone: string;
    bio: string;
    color: string;
    username: string;
    image: string;
    coverImage: string;
    categories?: string[];
    subcategories?: string[];
  }>({
    name: user.name || '',
    email: user.email || '',
    phone: user.phone || '',
    bio: user.bio || '',
    color: user.color || '',
    username: user.username || '',
    image: user.image || '',
    coverImage: user.coverImage || '',
    categories: user.preferences?.categories || [],
    subcategories: user.preferences?.subcategories || [],
  });
  const [imageFile, setImageFile] = React.useState<File | null>(null);
  const [coverFile, setCoverFile] = React.useState<File | null>(null);
  const activities = useQuery(api.users.getUserActivities, { userId: user._id });
  const updateUser = useMutation(api.users.update);
  const [expandedActivityId, setExpandedActivityId] = React.useState<string | null>(null);
  const [sellerProfileFields, setSellerProfileFields] = React.useState<any>(user.sellerProfile || {});
  const adminUpdateSellerProfile = useMutation(api.users.ADMIN_updateSellerProfile);

  const updaterIds = React.useMemo(() => {
    if (!activities) return [];
    return Array.from(
      new Set(
        activities
          .map((a) => a.createdBy)
          .filter((id) => id && id !== "system")
      )
    );
  }, [activities]);

  const updaters = useQuery(api.users.getUsersByIds, { userIds: updaterIds as Id<"users">[] });

  const updaterMap = React.useMemo(() => {
    if (!updaters) return {};
    const map: Record<string, any> = {};
    updaters.forEach((u) => {
      if (u && u._id) map[u._id] = u;
    });
    return map;
  }, [updaters]);

  const handleUpdate = (fields: Record<string, any>) => {
    updateUser({ id: user._id, ...fields });
  };

  const handleEditField = (field: string, value: any) => {
    setEditFields((prev) => ({ ...prev, [field]: value }));
  };

  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setImageFile(file);
      handleEditField('image', URL.createObjectURL(file));
    }
  };

  const handleCoverChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setCoverFile(file);
      handleEditField('coverImage', URL.createObjectURL(file));
    }
  };

  const handleSave = async () => {
    const categoriesChanged = JSON.stringify(editFields.categories) !== JSON.stringify(user.preferences?.categories || []);
    const subcategoriesChanged = JSON.stringify(editFields.subcategories) !== JSON.stringify(user.preferences?.subcategories || []);

    const changed: Record<string, any> = {};
    Object.keys(editFields).forEach((key) => {
      if (key === 'categories' || key === 'subcategories') {
        if (categoriesChanged || subcategoriesChanged) {
          changed.preferences = {
            categories: editFields.categories,
            subcategories: editFields.subcategories,
          };
        }
      } else if (editFields[key] !== (user[key] || '')) {
        changed[key] = editFields[key];
      }
    });
    if (imageFile) {
      /**
       * TODO: Replace with actual upload logic
       * const uploadedUrl = await uploadImage(imageFile);
       * changed.image = uploadedUrl;
       */
      changed.image = editFields.image;
    }
    if (coverFile) {
      /**
       * TODO: Replace with actual upload logic
       * const uploadedUrl = await uploadImage(coverFile);
       * changed.coverImage = uploadedUrl;
       */
      changed.coverImage = editFields.coverImage;
    }
    if (Object.keys(changed).length > 0 || (changed.preferences && Object.keys(changed.preferences).length > 0)) {
      await handleUpdate(changed);
    }
    setEditMode(false);
  };

  const handleCancel = () => {
    setEditFields({
      name: user.name || '',
      email: user.email || '',
      phone: user.phone || '',
      bio: user.bio || '',
      color: user.color || '',
      username: user.username || '',
      image: user.image || '',
      coverImage: user.coverImage || '',
      categories: user.preferences?.categories || [],
      subcategories: user.preferences?.subcategories || [],
    });
    setImageFile(null);
    setCoverFile(null);
    setEditMode(false);
  };

  const handleSellerProfileCancel = () => {
    setSellerProfileFields(user.sellerProfile || {});
    setEditMode(false);
  };

  const handleSellerProfileSave = async () => {
    await adminUpdateSellerProfile({
      userId: user._id,
      sellerProfile: sellerProfileFields,
    });
    setEditMode(false);
  };

  return (
    <div className="max-w-5xl mx-auto p-8 ">
      {/* Interested Seller Notice */}
      {user.interestedSeller && role !== "seller" && (
        <div className="mb-6 p-4 rounded-lg border dark:border-zinc-700 dark:bg-zinc-800/50 border-zinc-200 bg-zinc-50/50 flex items-center justify-between">
          <div className="text-zinc-900 dark:text-zinc-100 font-medium">
            This user has submitted an application to become a seller.
          </div>
          <Button
            variant="secondary"
            onClick={() => setSheetOpen(true)}
          >
            View Application
          </Button>
          <InterestedSellerSheet
            open={isSheetOpen}
            onOpenChange={setSheetOpen}
            interestedSeller={user.interestedSeller}
          />
        </div>
      )}
      {/* Profile Header */}
      <div className="flex flex-col gap-2 mb-8 relative">
        {/* Cover Image */}
        <div className="relative w-full h-32 mb-4 rounded-lg overflow-hidden bg-gray-100 dark:bg-zinc-800">
          {editMode ? (
            <>
              {editFields.coverImage && (
                <Image
                  src={editFields.coverImage}
                  alt="Cover"
                  className="w-full h-32 object-cover"
                  width={1280}
                  height={720}
                />
              )}
              <label className="absolute top-2 right-2 bg-white/80 px-2 py-1 rounded shadow cursor-pointer text-xs">
                Change Cover
                <input type="file" accept="image/*" className="hidden" onChange={handleCoverChange} />
              </label>
            </>
          ) : user.coverImage ? (
            <Image
              src={user.coverImage}
              alt="Cover"
              className="w-full h-32 object-cover"
              width={1280}
              height={720}
            />
          ) : null}
        </div>
        <div className="flex items-center gap-8 relative">
          {/* Edit Button */}
          <div className="absolute top-0 right-0 z-50">
            {editMode ? (
              <div className="flex gap-2">
                <Button type="button" size="sm" variant="secondary" onClick={handleSave}>Save</Button>
                <Button type="button" size="sm" variant="ghost" onClick={handleCancel}>Cancel</Button>
              </div>
            ) : (
              <Button size="sm" variant="outline" onClick={() => setEditMode(true)}>Edit</Button>
            )}
          </div>
          {/* Avatar */}
          <div className="relative">
            {editMode ? (
              <>
                {editFields.image ? (
                  <Image
                    src={editFields.image}
                    alt="Avatar"
                    className="w-20 h-20 rounded-full object-cover border-2 border-gray-200 shadow"
                    width={80}
                    height={80}
                  />
                ) : (
                  <div className="w-20 h-20 rounded-full bg-gray-200 flex items-center justify-center text-3xl font-bold text-white">
                    {getInitials(editFields.name, editFields.username)}
                  </div>
                )}
                <label className="absolute bottom-0 right-0 bg-white/80 px-2 py-1 rounded shadow cursor-pointer text-xs">
                  Change
                  <input type="file" accept="image/*" className="hidden" onChange={handleImageChange} />
                </label>
              </>
            ) : user.image ? (
              <Image
                src={user.image}
                alt={user.name || user.username || "User"}
                className="w-20 h-20 rounded-full object-cover border-2 border-gray-200 shadow"
                width={80}
                height={80}
              />
            ) : (
              <div className="w-20 h-20 rounded-full bg-gray-200 flex items-center justify-center text-3xl font-bold text-white">
                {getInitials(user.name, user.username)}
              </div>
            )}
          </div>
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2 flex-wrap">
              {editMode ? (
                <Input
                  className="w-fit"
                  value={editFields.name}
                  onChange={e => handleEditField('name', e.target.value)}
                  placeholder="Name"
                />
              ) : (
                <h1 className="text-2xl font-semibold truncate">
                  {user.name || `${user.firstName || ""} ${user.lastName || ""}`.trim() || user.username || "Unknown"}
                </h1>
              )}
              {user.status && (
                <span className="ml-2 px-2 py-1 text-xs rounded bg-green-100 text-green-700 font-medium capitalize">{user.status}</span>
              )}
              {editMode ? (
                <Select
                  value={role}
                  onValueChange={async (value) => {
                    setRole(value as "user" | "seller" | "admin");
                    await handleEditField('role', value as string);
                  }}
                >
                  <SelectTrigger className="ml-2 h-6 px-2 py-1 text-xs rounded bg-gray-100 text-gray-700 font-medium capitalize dark:bg-zinc-800 dark:text-zinc-100">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="user">User</SelectItem>
                    <SelectItem value="seller">Seller</SelectItem>
                    <SelectItem value="admin">Admin</SelectItem>
                  </SelectContent>
                </Select>
              ) : (
                <Select
                  value={role}
                  onValueChange={async (value) => {
                    setRole(value as "user" | "seller" | "admin");
                    await handleUpdate({ role: value as "user" | "seller" | "admin" });
                  }}
                >
                  <SelectTrigger className="ml-2 h-6 px-2 py-1 text-xs rounded bg-gray-100 text-gray-700 font-medium capitalize dark:bg-zinc-800 dark:text-zinc-100">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="user">User</SelectItem>
                    <SelectItem value="seller">Seller</SelectItem>
                    <SelectItem value="admin">Admin</SelectItem>
                  </SelectContent>
                </Select>
              )}
              {user.sellerProfile?.verified && <VerifiedBadge />}
            </div>
            {editMode ? (
              <div className="text-gray-400 text-xs mt-1">
                <Input
                  className="w-fit"
                  value={editFields.username}
                  onChange={e => handleEditField('username', e.target.value)}
                  placeholder="Username"
                />
              </div>
            ) : user.username && (
              <div className="text-gray-400 text-xs mt-1">
                <a href={`https://liveciety.com/user/${user.username}`} target="_blank" rel="noopener noreferrer" className="hover:underline">@{user.username}</a>
              </div>
            )}
            {editMode ? (
              <Textarea
                value={editFields.bio}
                onChange={e => handleEditField('bio', e.target.value)}
                placeholder="Bio"
                rows={2}
              />
            ) : user.bio && (
              <div className="text-gray-600 text-sm mt-2 line-clamp-2">{user.bio}</div>
            )}
            <div className="flex gap-4 mt-2 text-sm text-gray-500 flex-wrap">
              {editMode ? (
                <Input
                  className="w-fit"
                  value={editFields.email}
                  onChange={e => handleEditField('email', e.target.value)}
                  placeholder="Email"
                  type="email"
                />
              ) : user.email && <a href={`mailto:${user.email}`} className="hover:underline">{user.email}</a>}
              {editMode ? (
                <Input
                  className="w-fit"
                  value={editFields.phone}
                  onChange={e => handleEditField('phone', e.target.value)}
                  placeholder="Phone"
                  type="tel"
                />
              ) : user.phone && <a href={`tel:${user.phone}`} className="hover:underline">{user.phone}</a>}
              <span>Joined: {formatDate(user._creationTime)}</span>
              {user.color && <UserColorPicker color={user.color} userId={user._id} />}
            </div>
            <div className="flex gap-4 mt-2 text-xs text-gray-400 flex-wrap">
              {user.lastLoginType && <span>Last login: {user.lastLoginType}</span>}
              {user.emailVerificationTime && <span>Email verified: {formatDate(user.emailVerificationTime)}</span>}
              {user.phoneVerificationTime && <span>Phone verified: {formatDate(user.phoneVerificationTime)}</span>}
            </div>
          </div>
        </div>
      </div>

      {/* Seller Info */}
      {user.sellerProfile && (
        <div className="mb-8 p-4 rounded border bg-yellow-50 border-yellow-200">
          <div className="font-semibold text-yellow-900 mb-1">Seller Profile</div>
          <div className="flex flex-wrap gap-4 text-sm text-yellow-800">
            {user.sellerProfile.storeName && <span>Store: <span className="font-medium">{user.sellerProfile.storeName}</span></span>}
            {user.sellerProfile.rating && <span>Rating: <span className="font-medium">{user.sellerProfile.rating}/5</span></span>}
            {user.sellerProfile.totalSales && <span>Sales: <span className="font-medium">{user.sellerProfile.totalSales}</span></span>}
            {user.sellerProfile.verified && <span className="text-green-700">Verified Seller</span>}
            {user.sellerProfile.joinedAt && <span>Seller since: {formatDate(user.sellerProfile.joinedAt)}</span>}
            {user.sellerProfile.percentage && <span>Commission: {user.sellerProfile.percentage}%</span>}
          </div>
          {user.sellerProfile.storeDescription && (
            <div className="mt-2 text-yellow-900 text-xs">{user.sellerProfile.storeDescription}</div>
          )}
        </div>
      )}

      {/* Tabs Section */}
      <Tabs defaultValue="activity" className="mt-8">
        <TabsList>
          <TabsTrigger value="activity">Activity</TabsTrigger>
          <TabsTrigger value="payments">Payments</TabsTrigger>
          <TabsTrigger value="preferences">Preferences</TabsTrigger>
          {user.role === "seller" && (
            <TabsTrigger value="sellerProfile">Seller Profile</TabsTrigger>
          )}
        </TabsList>
        <TabsContent value="activity">
          <div className="space-y-4">
            {activities === undefined ? (
              <div>Loading activity...</div>
            ) : activities.length === 0 ? (
              <div className="text-gray-400 text-sm">No activity</div>
            ) : (
              <ul className="space-y-2">
                {activities.map((activity) => {
                  const changes = activity.description
                    .split('; ')
                    .map((desc) => {
                      const match = desc.match(/Field '(.+?)' changed from '(.+)' to '(.+)'/);
                      if (match) {
                        return {
                          field: match[1],
                          oldValue: match[2],
                          newValue: match[3],
                        };
                      }
                      return null;
                    })
                    .filter((c): c is { field: string; oldValue: string; newValue: string } => !!c);

                  let updater: any = { name: "System", avatarUrl: null };
                  if (activity.createdBy && activity.createdBy !== "system" && typeof activity.createdBy === "string") {
                    updater = updaterMap[activity.createdBy] || null;
                  }

                  const currentUserId = undefined; 
                  /**
                   * TODO: set this if available
                   */
                  const isCurrentUser = currentUserId && updater && updater._id === currentUserId;
                  const updaterDisplay = isCurrentUser ? "You" : (updater?.name || updater?.username || updater?.email || "User");

                  let summary = "";
                  if (changes.length === 1) {
                    summary = `${updaterDisplay} changed ${changes[0]?.field.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}`;
                  } else if (changes.length > 1) {
                    summary = `${updaterDisplay} changed ${changes.length} attributes`;
                  } else {
                    summary = activity.description;
                  }

                  const isExpanded = expandedActivityId === activity._id;

                  return (
                    <li key={activity._id} className="border rounded p-3 bg-gray-50 dark:bg-zinc-900">
                      <div className="flex items-center gap-2 mb-1">
                        {updater && updater.avatarUrl && (
                          <Image src={getAvatarImageUrl(updater.avatarUrl) as string} alt={updater?.name || updater?.username || updater?.email || "User"} className="w-5 h-5 rounded-full object-cover border" />
                        )}
                        <span className="font-medium text-xs text-gray-700 dark:text-zinc-200">{summary}</span>
                        {changes.length > 1 && (
                          <button
                            className="ml-2 text-xs text-blue-500 underline"
                            onClick={() => setExpandedActivityId(isExpanded ? null : activity._id)}
                            type="button"
                          >
                            {isExpanded ? "Hide details" : "Show details"}
                          </button>
                        )}
                        <span className="ml-auto text-xs text-gray-500">
                          {new Date(activity._creationTime).toLocaleString()}
                        </span>
                      </div>
                      {changes.length === 1 && changes[0] && (
                        <div className="font-mono text-sm">
                          <span className="font-semibold">{changes[0].field.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}</span>
                          {": "}
                          <span className="text-red-600 line-through">{changes[0].oldValue}</span>
                          {" → "}
                          <span className="text-green-700">{changes[0].newValue}</span>
                        </div>
                      )}
                      {changes.length > 1 && isExpanded && (
                        <ul className="mt-2 space-y-1">
                          {changes.map((change, idx) => (
                            change ? (
                              <li key={idx} className="font-mono text-sm">
                                <span className="font-semibold">{change.field.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}</span>
                                {": "}
                                <span className="text-red-600 line-through">{change.oldValue}</span>
                                {" → "}
                                <span className="text-green-700">{change.newValue}</span>
                              </li>
                            ) : null
                          ))}
                        </ul>
                      )}
                    </li>
                  );
                })}
              </ul>
            )}
          </div>
        </TabsContent>
        <TabsContent value="payments">
        {user.stripeCustomerId && (
            <div className="w-full flex items-center gap-2 mb-2 px-3 py-2 rounded text-md font-semibold text-violet-300" style={{ backgroundColor: "#675dff" }}>
              <IconBrandStripe className="w-4 h-4 text-white" />
              Stripe Customer:{" "}
              <a
                href={`https://dashboard.stripe.com/customers/${user.stripeCustomerId}`}
                target="_blank"
                rel="noopener noreferrer"
                className="hover:underline font-mono text-white"
              >
                {user.stripeCustomerId}
              </a>
            </div>
            
          )}
          {user.stripeAccountId && (
            <div className="w-full flex items-center gap-2 mb-2 px-3 py-2 rounded text-md font-semibold text-violet-300" style={{ backgroundColor: "#675dff" }}>
              <IconBrandStripe className="w-4 h-4 text-white" />
              Stripe Account:{" "}
              <a
                href={`https://dashboard.stripe.com/test/connect/accounts/${user.stripeAccountId}`}
                target="_blank"
                rel="noopener noreferrer"
                className="hover:underline font-mono text-white"
              >
                {user.stripeAccountId}
              </a>
            </div>
            
          )}
        </TabsContent>
        <TabsContent value="preferences">
          {user.preferences && (
            <div className="mt-4">
              <div className="font-semibold mb-1">Preferences</div>
              <div className="text-sm text-gray-700 space-y-1">
                {user.preferences.notifications !== undefined && <div>Notifications: {user.preferences.notifications ? "On" : "Off"}</div>}
                {user.preferences.emailUpdates !== undefined && <div>Email Updates: {user.preferences.emailUpdates ? "On" : "Off"}</div>}
                {user.preferences.darkMode !== undefined && <div>Dark Mode: {user.preferences.darkMode ? "On" : "Off"}</div>}
                {/* Categories */}
                {editMode ? (
                  <div className="mt-2">
                    <div className="font-semibold text-xs mb-1 text-gray-500">Categories</div>
                    <div className="flex flex-wrap gap-2">
                      {categories.map((cat) => (
                        <label key={cat.id} className="flex items-center gap-2 cursor-pointer">
                          <input
                            type="checkbox"
                            checked={editFields.categories?.includes(cat.id)}
                            onChange={e => {
                              const checked = e.target.checked;
                              let newCats = editFields.categories ? [...editFields.categories] : [];
                              if (checked) {
                                newCats.push(cat.id);
                              } else {
                                newCats = newCats.filter((id) => id !== cat.id);
                              }
                              handleEditField('categories', newCats);
                            }}
                          />
                          <span>{cat.title}</span>
                        </label>
                      ))}
                    </div>
                  </div>
                ) : (
                  user.preferences.categories && user.preferences.categories.length > 0 && (
                    <div className="mt-2">
                      <div className="font-semibold text-xs mb-1 text-gray-500">Categories</div>
                      <div className="flex flex-wrap gap-4">
                        {user.preferences.categories.map((catId: string) => {
                          const cat = categories.find((c: any) => c.id === catId);
                          if (!cat) return null;
                          return (
                            <a
                              key={cat.id}
                              href={`https://liveciety.com/browse/${cat.id}`}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="flex flex-col items-center w-32 p-2 rounded-lg border bg-white shadow hover:shadow-md transition hover:border-violet-400 group"
                            >
                              {cat.image && (
                                <Image
                                  src={cat.image}
                                  alt={cat.title}
                                  className="w-12 h-12 object-contain mb-2"
                                  width={48}
                                  height={48}
                                />
                              )}
                              <div className="font-semibold text-sm text-gray-800 group-hover:text-violet-700">{cat.title}</div>
                              <div className="text-xs text-gray-500 text-center line-clamp-2">{cat.description}</div>
                            </a>
                          );
                        })}
                      </div>
                    </div>
                  )
                )}
                {/* Subcategories */}
                {editMode ? (
                  <div className="mt-4">
                    <div className="font-semibold text-xs mb-1 text-gray-500">Subcategories</div>
                    <div className="flex flex-col gap-2">
                      {categories.map((cat) => (
                        <div key={cat.id}>
                          <div className="font-semibold text-xs text-gray-700 mb-1">{cat.title}</div>
                          <div className="flex flex-wrap gap-2 mb-2">
                            {(subcategories[cat.id] || []).map((sub) => (
                              <label key={sub.id} className="flex items-center gap-2 cursor-pointer">
                                <input
                                  type="checkbox"
                                  checked={editFields.subcategories?.includes(sub.id)}
                                  onChange={e => {
                                    const checked = e.target.checked;
                                    let newSubs = editFields.subcategories ? [...editFields.subcategories] : [];
                                    if (checked) {
                                      newSubs.push(sub.id);
                                    } else {
                                      newSubs = newSubs.filter((id) => id !== sub.id);
                                    }
                                    handleEditField('subcategories', newSubs);
                                  }}
                                />
                                <span>{sub.title}</span>
                              </label>
                            ))}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                ) : (
                  user.preferences.subcategories && user.preferences.subcategories.length > 0 && (
                    <div className="mt-4">
                      <div className="font-semibold text-xs mb-1 text-gray-500">Subcategories</div>
                      <div className="flex flex-wrap gap-2">
                        {user.preferences.subcategories.map((subId: string) => {
                          const parentCat = Object.entries(subcategories).find(([catId, subs]) =>
                            subs?.some((s: any) => s.id === subId)
                          );
                          if (!parentCat) return null;
                          const [catId, subs] = parentCat;
                          const sub = subs.find((s: any) => s.id === subId);
                          if (!sub) return null;

                          return (
                            <a
                              key={sub.id}
                              href={`https://liveciety.com/browse/${catId}?subcategory=${sub.id}`}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="inline-flex items-center gap-2 px-3 py-1 rounded-full bg-violet-100 text-violet-800 text-xs font-medium border border-violet-200 hover:bg-violet-200 transition"
                            >
                              <span>{sub.title}</span>
                            </a>
                          );
                        })}
                      </div>
                    </div>
                  )
                )}
              </div>
            </div>
          )}
        </TabsContent>
        <TabsContent value="documents">
          <div className="text-gray-400 text-sm">No documents uploaded.</div>
        </TabsContent>
        <TabsContent value="family">
          <div className="text-gray-400 text-sm">No family data available.</div>
        </TabsContent>
        <TabsContent value="sellerProfile">
          {user.role === "seller" && (
            <div className="mt-4">
              <div className="font-semibold mb-1">Seller Profile</div>
              <div className="text-sm text-gray-700 space-y-2">
              {editMode ? (
                  <div className="mt-2">
                    <div className="font-semibold text-xs mb-1 text-gray-500">Categories</div>
                    <div className="flex flex-wrap gap-2">
                      {categories.map((cat) => (
                        <label key={cat.id} className="flex items-center gap-2 cursor-pointer">
                          <input
                            type="checkbox"
                            checked={editFields.categories?.includes(cat.id)}
                            onChange={e => {
                              const checked = e.target.checked;
                              let newCats = editFields.categories ? [...editFields.categories] : [];
                              if (checked) {
                                newCats.push(cat.id);
                              } else {
                                newCats = newCats.filter((id) => id !== cat.id);
                              }
                              handleEditField('categories', newCats);
                            }}
                          />
                          <span>{cat.title}</span>
                        </label>
                      ))}
                    </div>
                  </div>
                ) : (
                  user.sellerProfile.categories && user.sellerProfile.categories.length > 0 && (
                    <div className="mt-2">
                      <div className="font-semibold text-xs mb-1 text-gray-500">Categories</div>
                      <div className="flex flex-wrap gap-4">
                        {user.sellerProfile.categories.map((catId: string) => {
                          const cat = categories.find((c: any) => c.id === catId);
                          if (!cat) return null;
                          return (
                            <a
                              key={cat.id}
                              href={`https://liveciety.com/browse/${cat.id}`}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="flex flex-col items-center w-32 p-2 rounded-lg border bg-white shadow hover:shadow-md transition hover:border-violet-400 group"
                            >
                              {cat.image && (
                                <Image
                                  src={cat.image}
                                  alt={cat.title}
                                  className="w-12 h-12 object-contain mb-2"
                                  width={48}
                                  height={48}
                                />
                              )}
                              <div className="font-semibold text-sm text-gray-800 group-hover:text-violet-700">{cat.title}</div>
                              <div className="text-xs text-gray-500 text-center line-clamp-2">{cat.description}</div>
                            </a>
                          );
                        })}
                      </div>
                    </div>
                  )
                )}

                {editMode ? (
                  <>
                    <div>
                      <label className="block text-xs font-medium mb-1">Verified</label>
                      <input
                        type="checkbox"
                        checked={!!sellerProfileFields.verified}
                        onChange={e => setSellerProfileFields((prev: any) => ({ ...prev, verified: e.target.checked }))}
                      />
                    </div>
                    <div>
                      <label className="block text-xs font-medium mb-1">Joined At</label>
                      <Input
                        type="date"
                        value={sellerProfileFields.joinedAt ? new Date(sellerProfileFields.joinedAt).toISOString().substring(0, 10) : ""}
                        onChange={e => setSellerProfileFields((prev: any) => ({ ...prev, joinedAt: new Date(e.target.value).getTime() }))}
                        placeholder="Joined At"
                      />
                    </div>
                    <div>
                      <label className="block text-xs font-medium mb-1">Commission (%)</label>
                      <Input
                        type="number"
                        value={sellerProfileFields.percentage ?? ""}
                        onChange={e => setSellerProfileFields((prev: any) => ({ ...prev, percentage: parseFloat(e.target.value) }))}
                        placeholder="Commission"
                        min={0}
                        max={100}
                        step={0.1}
                      />
                    </div>
                    <div className="flex gap-2 mt-4">
                      <Button type="button" size="sm" variant="secondary" onClick={handleSellerProfileSave}>Save</Button>
                      <Button type="button" size="sm" variant="ghost" onClick={handleSellerProfileCancel}>Cancel</Button>
                    </div>
                  </>
                ) : (
                  <>
                    <div><span className="font-semibold">Store Name:</span> {sellerProfileFields.storeName || <span className="text-gray-400">N/A</span>}</div>
                    <div><span className="font-semibold">Store Description:</span> {sellerProfileFields.storeDescription || <span className="text-gray-400">N/A</span>}</div>
                    <div><span className="font-semibold">Bio:</span> {sellerProfileFields.bio || <span className="text-gray-400">N/A</span>}</div>
                    <div><span className="font-semibold">Verified:</span> {sellerProfileFields.verified ? "Yes" : "No"}</div>
                    <div><span className="font-semibold">Rating:</span> {sellerProfileFields.rating ?? <span className="text-gray-400">N/A</span>}</div>
                    <div><span className="font-semibold">Total Sales:</span> {sellerProfileFields.totalSales ?? <span className="text-gray-400">N/A</span>}</div>
                    <div><span className="font-semibold">Joined At:</span> {sellerProfileFields.joinedAt ? new Date(sellerProfileFields.joinedAt).toLocaleDateString() : <span className="text-gray-400">N/A</span>}</div>
                    <div><span className="font-semibold">Commission:</span> {sellerProfileFields.percentage ?? <span className="text-gray-400">N/A</span>}%</div>
                  </>
                )}
              </div>
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
} 