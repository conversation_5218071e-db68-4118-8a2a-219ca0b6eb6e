import React from "react";
import { ColumnDef } from "@tanstack/react-table";
import { DataTableColumnHeader } from "./column-header";
import { PUBLIC_USER_ROLES } from "@/lib/constants";
import { cn } from "@workspace/ui/lib/utils";
import { formatDistanceToNow } from "date-fns";
import { Checkbox } from "@workspace/ui/components/checkbox";
import { useMutation, useQuery } from "convex/react";
import { IconStar, IconStarFilled } from "@tabler/icons-react";
import { api } from "@workspace/backend/convex/_generated/api";
import { Id } from "@workspace/backend/convex/_generated/dataModel";
import { User } from "@workspace/backend/convex/lib/types";
import { Badge } from "@workspace/ui/components/badge";

interface UserRow {
  original: User & { interestedSellerStatus?: string; role?: string };
}

function includesString(rowValue: string, filterValue: string | string[]) {
  if (typeof rowValue !== "string") {
    return false;
  }

  if (Array.isArray(filterValue)) {
    return filterValue.some((value) =>
      rowValue.toLowerCase().includes(value.toLowerCase()),
    );
  }

  if (typeof filterValue !== "string") {
    return false;
  }

  return rowValue.toLowerCase().includes(filterValue.toLowerCase());
}

interface Favorite {
  _id: Id<"favorites">;
  objectId: Id<"users">;
  userId: Id<"users">;
}

const FavoriteButton = React.memo(
  ({ userId, favorites }: { userId: Id<"users">; favorites: Favorite[] }) => {
    const toggleFavorite = useMutation(api.favorites.toggle);
    const isFavorited = favorites.some((f) => f.objectId === userId);

    const handleToggle = (e: React.MouseEvent) => {
      e.stopPropagation();
      toggleFavorite({ objectId: userId });
    };

    const StarIcon = isFavorited ? IconStarFilled : IconStar;

    return (
      <button
        onClick={handleToggle}
        className={cn(
          "opacity-0 group-hover:opacity-100 transition-opacity",
          "hover:text-yellow-500",
          isFavorited && "opacity-100 text-yellow-500",
        )}
      >
        <StarIcon className="size-4" />
      </button>
    );
  },
);

FavoriteButton.displayName = "FavoriteButton";

const TitleCell = React.memo(({ row }: { row: UserRow }) => {
  const favorites = useQuery(api.favorites.get) || [];
  const toggleFavorite = useMutation(api.favorites.toggle);
  const userId = row.original?._id;
  const isFavorited = favorites.some((f) => f?.objectId === userId);

  return (
    <div className="flex flex-row items-center space-x-2 px-2 group">
      {userId && (
        <button
          onClick={(e) => {
            e.stopPropagation();
            toggleFavorite({
              objectId: userId,
            });
          }}
          className={cn(
            "cursor-pointer",
            "hover:bg-muted/50 p-2 rounded-lg",
            "opacity-0 group-hover:opacity-100 transition-opacity",
            "hover:text-yellow-500",
            isFavorited && "opacity-100 text-yellow-500",
          )}
        >
          {isFavorited ? (
            <IconStarFilled className="size-4" />
          ) : (
            <IconStar className="size-4" />
          )}
        </button>
      )}
      <div className="flex items-center space-x-2">
        <span className="max-w-[500px] truncate font-medium cursor-pointer">
          {row.original?.username}
        </span>
        {String(row.original.role) === "user" && row.original.interestedSellerStatus === "new" && (
          <Badge variant="secondary" className="bg-yellow-500/10 text-yellow-500 border-yellow-500/20">
            Applied
          </Badge>
        )}
      </div>
    </div>
  );
});

TitleCell.displayName = "TitleCell";

export const columns: ColumnDef<User>[] = [
  {
    id: "select",
    header: ({ table }) => (
      <Checkbox
        checked={
          table.getIsAllPageRowsSelected() ||
          (table.getIsSomePageRowsSelected() && true)
        }
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all"
        className="translate-y-[2px] !h-4 !w-4"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onClick={(e) => {
          e.stopPropagation();
        }}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label="Select row"
        className="translate-y-[2px] !h-4 !w-4"
      />
    ),
    enableSorting: false,
    enableHiding: false,
  },
  {
    id: "username",
    accessorKey: "username",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Username" />
    ),
    cell: ({ row }) => <TitleCell row={row as UserRow} />,
    enableSorting: true,
    sortingFn: (rowA, rowB) => {
      const a = rowA.original?.username || "";
      const b = rowB.original?.username || "";
      return a.localeCompare(b);
    },
  },
  {
    id: "name",
    accessorKey: "name",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Name" />
    ),
    cell: ({ row }) => {
      const name = row.original?.name || "";
      return (
        <div className="flex items-center">
          <span className="truncate max-w-[250px]">{name}</span>
        </div>
      );
    },
    filterFn: (row, columnId, filterValue: string | string[]) =>
      includesString(row.getValue(columnId), filterValue),
    enableSorting: true,
    sortingFn: (rowA, rowB) => {
      const a = rowA.original?.name || "";
      const b = rowB.original?.name || "";
      return a.localeCompare(b);
    },
  },
  {
    id: "role",
    accessorKey: "role",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Role" />
    ),
    cell: ({ row }) => {
      const role = row.original?.role;
      const roleObj = PUBLIC_USER_ROLES.find((item) => item.value === role);
      const RoleLabel = roleObj?.label || "No Role";
      const RoleIcon = roleObj?.icon;

      return (
        <div className="flex items-center">
          {RoleIcon && React.createElement(RoleIcon, {
            className: cn("mr-2 size-4", roleObj?.color),
            "aria-hidden": true,
          })}
          <span>{RoleLabel}</span>
        </div>
      );
    },
    filterFn: (row, columnId, filterValue: string | string[]) =>
      includesString(row.getValue(columnId), filterValue),
    enableSorting: true,
    sortingFn: (rowA, rowB) => {
      const a = rowA.original?.role || "";
      const b = rowB.original?.role || "";
      return a.localeCompare(b);
    },
  },
  {
    accessorKey: "email",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Email" />
    ),
    cell: ({ row }) => {
      const email = row.original?.email || "";
      
      return (
        <div className="flex items-center">
          <span className="truncate max-w-[250px]">{email}</span>
        </div>
      );
    },
    filterFn: (row, columnId, filterValue: string | string[]) =>
      includesString(row.getValue(columnId), filterValue),
    enableSorting: true,
    sortingFn: (rowA, rowB) => {
      const a = rowA.original?.email || "no_email";
      const b = rowB.original?.email || "no_email";
      return a.localeCompare(b);
    },
  },
  {
    accessorKey: "_creationTime",

    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Created" />
    ),
    cell: ({ row }) => {
      const createdAt = row.original?._creationTime
        ? new Date(row.original._creationTime)
        : null;
      return (
        <div className="flex items-center cursor-default">
          <span className="ml-2">
            {createdAt
              ? formatDistanceToNow(createdAt, { addSuffix: true })
              : ""}
          </span>
        </div>
      );
    },
    enableSorting: true,
    sortingFn: (rowA, rowB) => {
      const a = rowA.original?._creationTime || 0;
      const b = rowB.original?._creationTime || 0;
      return a - b;
    },
  },
  {
    id: "_id",
    accessorKey: "_id",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="ID" />
    ),
    cell: ({ row }) => (
      <span className="font-mono text-xs bg-zinc-100 dark:bg-zinc-800 px-2 py-1 rounded">{String(row.original._id)}</span>
    ),
    enableSorting: true,
    enableColumnFilter: true,
    filterFn: (row, columnId, filterValue: string | string[]) => {
      const rowValue = String(row.getValue(columnId));
      if (typeof filterValue === 'string') {
        return rowValue.includes(filterValue);
      }
      if (Array.isArray(filterValue)) {
        return filterValue.some(value => rowValue.includes(value));
      }
      return false;
    },
    meta: {
      label: "ID",
      placeholder: "Search by ID...",
      variant: "text",
    },
  },
];
