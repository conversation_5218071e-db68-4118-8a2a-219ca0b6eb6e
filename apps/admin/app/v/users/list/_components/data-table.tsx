"use client";

import React from "react";
import {
  ColumnDef,
  flexRender,
  Row,
  HeaderGroup,
  type Table,
} from "@tanstack/react-table";
import {
  Table as UITable,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@workspace/ui/components/table";
import EmptyContainer from "@/components/empty-container";
import { Skeleton } from "@workspace/ui/components/skeleton";
import { IconSquareRoundedCheck } from "@tabler/icons-react";
import { User } from "@workspace/backend/convex/lib/types";
import { useRouter } from "next/navigation";

interface TableProps {
  columns: ColumnDef<User, unknown>[];
  table: Table<User>;
  isLoading: boolean;
  hasMore?: boolean;
  bottomRef?: React.RefObject<HTMLDivElement>;
}

export default function UserTable({
  columns,
  table,
  isLoading,
  hasMore,
  bottomRef,
}: TableProps) {
  return (
    <DataTable
      columns={columns}
      table={table}
      isLoading={isLoading}
      hasMore={hasMore}
      bottomRef={bottomRef}
    />
  );
}

interface DataTableProps {
  columns: ColumnDef<User, unknown>[];
  table: Table<User>;
  isLoading: boolean;
  hasMore?: boolean;
  bottomRef?: React.RefObject<HTMLDivElement>;
}

export function DataTable({
  columns,
  table,
  isLoading,
  hasMore,
  bottomRef,
}: DataTableProps) {
  const router = useRouter();

  const TableSkeleton = ({
    columns,
  }: {
    columns: ColumnDef<User, unknown>[];
  }) => (
    <>
      {[...Array(5)].map((_, index) => (
        <TableRow key={index}>
          {columns.map((column, cellIndex) => (
            <TableCell key={cellIndex}>
              <Skeleton className="h-6 w-full" />
            </TableCell>
          ))}
        </TableRow>
      ))}
    </>
  );

  const handleRowClick = (row: Row<User>) => {
    router.push(`/v/users/${row.original._id}`);
  };

  return (
    <div className="space-y-4">
      <div className="overflow-auto" style={{ minHeight: "400px" }}>
        <UITable>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup: HeaderGroup<User>) => (
                <TableRow
                  key={headerGroup.id}
                  className="border-b hover:bg-transparent"
                >
                  {headerGroup.headers.map((header) => {
                    return (
                      <TableHead
                        key={header.id}
                        colSpan={header.colSpan}
                        className="h-10 px-4 text-xs font-medium text-zinc-500"
                      >
                        {header.isPlaceholder
                          ? null
                          : flexRender(
                              header.column.columnDef.header,
                              header.getContext(),
                            )}
                      </TableHead>
                    );
                  })}
                </TableRow>
              ))}
            </TableHeader>
            <TableBody>
              {isLoading ? (
                <TableSkeleton columns={columns} />
              ) : (
                <>
                  {table.getRowModel().rows.length > 0 ? (
                    <>
                      {table.getRowModel().rows.map((row) => (
                        <TableRow 
                          key={row.id}
                          className="hover:bg-muted/50 cursor-pointer"
                          onClick={() => handleRowClick(row)}
                        >
                          {row.getVisibleCells().map((cell) => (
                            <TableCell key={cell.id} className="py-2">
                              {flexRender(
                                cell.column.columnDef.cell,
                                cell.getContext()
                              )}
                            </TableCell>
                          ))}
                        </TableRow>
                      ))}
                      {/* Infinite scroll trigger */}
                      {hasMore && !isLoading && (
                        <TableRow>
                          <TableCell colSpan={columns.length} className="text-center">
                            <div ref={bottomRef} style={{ height: 1 }} />
                          </TableCell>
                        </TableRow>
                      )}
                    </>
                  ) : (
                    <TableRow>
                      <TableCell
                        colSpan={columns.length}
                        className="h-24 text-center rounded-b-lg"
                      >
                        <EmptyContainer
                          title="No users found"
                          subtitle="No users match your search criteria."
                          icon={IconSquareRoundedCheck}
                        />
                      </TableCell>
                    </TableRow>
                  )}
                </>
              )}
            </TableBody>
          </UITable>
      </div>
    </div>
  );
}
