import React from "react";
import UserTable from "./_components/data-table";
import { User } from "@workspace/backend/convex/lib/types";
import { Table } from "@tanstack/react-table";

interface UserListProps {
  columns: import("@tanstack/react-table").ColumnDef<User, unknown>[];
  table: Table<User>;
  isLoading: boolean;
  hasMore?: boolean;
  isLoadingMore?: boolean;
  onLoadMore?: () => void;
  bottomRef?: React.RefObject<HTMLDivElement>;
  isMounted: React.RefObject<boolean>;
}

const UserList = ({
  columns,
  table,
  isLoading,
  hasMore,
  isLoadingMore,
  onLoadMore,
  bottomRef,
  isMounted,
}: UserListProps) => {
  return (
    <UserTable
      columns={columns}
      table={table}
      isLoading={isLoading}
      hasMore={hasMore}
      isLoadingMore={isLoadingMore}
      onLoadMore={onLoadMore}
      bottomRef={bottomRef}
      isMounted={isMounted}
    />
  );
};

export default UserList;
