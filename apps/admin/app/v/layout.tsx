import { cookies } from "next/headers";

import {
  SidebarInset,
  SidebarProvider,
} from "@workspace/ui/components/sidebar";
import { AppSidebar } from "@/components/app-sidebar";
import { SiteHeader } from "@/components/site-header";
import { SidePanelProvider } from "@/components/side-panel";

import "@/app/theme.css";
import { preloadQuery } from "convex/nextjs";
import { api } from "@workspace/backend/convex/_generated/api";
import { convexAuthNextjsToken } from "@convex-dev/auth/nextjs/server";
import AppWrapper from "@/context/app-wrapper";

export default async function DashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const token = await convexAuthNextjsToken();
  const cookieStore = await cookies();
  const defaultOpen = cookieStore.get("sidebar_state")?.value === "true";

  const preloadedUser = await preloadQuery(api.users.viewer, {}, { token });
  const preloadedTaskCount = await preloadQuery(api.tasks.count, {}, { token });
  const preloadedFavorites = await preloadQuery(
    api.favorites.get,
    {},
    { token },
  );

  return (
    <SidebarProvider
      defaultOpen={defaultOpen}
      style={
        {
          "--sidebar-width": "calc(var(--spacing) * 72)",
        } as React.CSSProperties
      }
    >
      <AppSidebar
        variant="inset"
        preloadedUser={preloadedUser}
        preloadedTaskCount={preloadedTaskCount}
        preloadedFavorites={preloadedFavorites}
      />
      <SidebarInset>
        <SiteHeader preloadedUser={preloadedUser} />
        <div className="flex flex-1 flex-col">
          <AppWrapper>
            <SidePanelProvider>{children}</SidePanelProvider>
          </AppWrapper>
        </div>
      </SidebarInset>
    </SidebarProvider>
  );
}
