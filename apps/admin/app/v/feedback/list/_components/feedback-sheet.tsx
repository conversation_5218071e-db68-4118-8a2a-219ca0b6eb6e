import React from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  SheetDes<PERSON>,
} from "@workspace/ui/components/sheet";
import { UserAvatar } from "@workspace/ui/components/user-avatar";
import { Badge } from "@workspace/ui/components/badge";
import { Button } from "@workspace/ui/components/button";
import { Feedback } from "@workspace/backend/convex/lib/types";
import { FEEDBACK_STATUS, FEEDBACK_TYPE } from "@/lib/constants";
import { useMutation } from "convex/react";
import { api } from "@workspace/backend/convex/_generated/api";
import {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuItem,
} from "@workspace/ui/components/dropdown-menu";

interface FeedbackSheetProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  feedback: Feedback | null;
}

export function FeedbackSheet({ open, onOpenChange, feedback }: FeedbackSheetProps) {
  const [statusLoading, setStatusLoading] = React.useState(false);
  const [typeLoading, setTypeLoading] = React.useState(false);
  const [localStatus, setLocalStatus] = React.useState(feedback?.status);
  const [localType, setLocalType] = React.useState(feedback?.type);
  const updateStatus = useMutation(api.feedback.updateFeedbackStatus);
  const updateType = useMutation(api.feedback.updateBatchFeedbacks);

  React.useEffect(() => {
    setLocalStatus(feedback?.status);
    setLocalType(feedback?.type);
  }, [feedback?.status, feedback?.type]);

  if (!feedback) return null;

  const { user, metadata } = feedback;

  const handleStatusChange = async (e: React.ChangeEvent<HTMLSelectElement>) => {
    const newStatus = e.target.value;
    if (newStatus === feedback.status) return;
    setStatusLoading(true);
    try {
      await updateStatus({
        feedbackId: feedback._id,
        status: newStatus as Feedback["status"],
      });
    } finally {
      setStatusLoading(false);
    }
  };

  const handleTypeChange = async (e: React.ChangeEvent<HTMLSelectElement>) => {
    const newType = e.target.value;
    if (newType === feedback.type) return;
    setTypeLoading(true);
    try {
      await updateType({
        feedbackIds: [feedback._id],
        type: newType,
      });
    } finally {
      setTypeLoading(false);
    }
  };

  return (
    <Sheet open={open} onOpenChange={onOpenChange}>
      <SheetContent overlay={false} className="max-w-lg w-full -mt-2 border-t mr-2">
        <SheetHeader className="border-b pb-4 mb-4">
          <SheetTitle>Feedback Details</SheetTitle>
          <SheetDescription>
            View all information for this feedback entry.
          </SheetDescription>
        </SheetHeader>
        <div className="space-y-4 px-2">
          <div>
            <h3 className="font-semibold text-sm mb-1">Message</h3>
            <p className="text-base whitespace-pre-line">{feedback.message}</p>
          </div>
          {feedback.notes && (
            <div>
              <h3 className="font-semibold text-sm mb-1">Notes</h3>
              <p className="text-base whitespace-pre-line">{feedback.notes}</p>
            </div>
          )}
          {/* Controls Row */}
          <div className="flex flex-wrap gap-x-4 gap-y-2 items-end mb-2">
            <div className="flex flex-col">
              <label className="text-xs font-medium mb-1">Status</label>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <button
                    className="border rounded px-2 py-1 text-xs bg-background border-input focus:outline-none focus:ring-2 focus:ring-primary min-w-[90px] text-left disabled:opacity-60"
                    disabled={statusLoading}
                  >
                    {FEEDBACK_STATUS.find((s) => s.value === localStatus)?.label || localStatus}
                  </button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="start">
                  {FEEDBACK_STATUS.map((s) => (
                    <DropdownMenuItem
                      key={s.value}
                      onClick={async () => {
                        if (s.value === localStatus) return;
                        const prevStatus = localStatus;
                        setLocalStatus(s.value as Feedback["status"]);
                        setStatusLoading(true);
                        try {
                          await updateStatus({
                            feedbackId: feedback._id,
                            status: s.value as Feedback["status"],
                          });
                        } catch (err) {
                          setLocalStatus(prevStatus);
                        } finally {
                          setStatusLoading(false);
                        }
                      }}
                      disabled={statusLoading || s.value === localStatus}
                    >
                      {s.label}
                    </DropdownMenuItem>
                  ))}
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
            <div className="flex flex-col">
              <label className="text-xs font-medium mb-1">Type</label>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <button
                    className="border rounded px-2 py-1 text-xs bg-background border-input focus:outline-none focus:ring-2 focus:ring-primary min-w-[90px] text-left disabled:opacity-60"
                    disabled={typeLoading}
                  >
                    {FEEDBACK_TYPE.find((t) => t.value === localType)?.label || localType}
                  </button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="start">
                  {FEEDBACK_TYPE.map((t) => (
                    <DropdownMenuItem
                      key={t.value}
                      onClick={async () => {
                        if (t.value === localType) return;
                        const prevType = localType;
                        setLocalType(t.value as Feedback["type"]);
                        setTypeLoading(true);
                        try {
                          await updateType({
                            feedbackIds: [feedback._id],
                            type: t.value,
                          });
                        } catch (err) {
                          setLocalType(prevType);
                        } finally {
                          setTypeLoading(false);
                        }
                      }}
                      disabled={typeLoading || t.value === localType}
                    >
                      {t.label}
                    </DropdownMenuItem>
                  ))}
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>
          {/* Info Row */}
          <div className="flex flex-wrap gap-x-4 gap-y-2 items-center">
            <Badge variant="outline">Created: {new Date(feedback.createdAt).toLocaleString()}</Badge>
            <Badge variant="outline">URL: <a href={feedback.url} target="_blank" rel="noopener noreferrer" className="underline">{feedback.url}</a></Badge>
          </div>
          {metadata && (
            <div>
              <h3 className="font-semibold text-sm mt-2 mb-1">Metadata</h3>
              <ul className="text-sm list-disc pl-5">
                {Object.entries(metadata).map(([key, value]) => (
                  <li key={key}><span className="font-medium">{key}:</span> {String(value)}</li>
                ))}
              </ul>
            </div>
          )}
          {user && (
            <div className="mt-4 p-3 rounded-md border bg-zinc-50 dark:bg-zinc-900 overflow-hidden">
              <div className="flex items-center gap-3 mb-2">
                <UserAvatar user={user as any} />
                <div>
                  <div className="font-medium">{(user as any).username}</div>
                  {(user as any).name && <div className="text-xs text-gray-500">{(user as any).name}</div>}
                </div>
              </div>
              <ul className="text-xs space-y-1">
                <li><span className="font-medium">Email:</span> {(user as any).email}</li>
                <li><span className="font-medium">Role:</span> {(user as any).role}</li>
                {(user as any).bio && <li><span className="font-medium">Bio:</span> {(user as any).bio}</li>}
                {(user as any).phone && <li><span className="font-medium">Phone:</span> {(user as any).phone}</li>}
                {(user as any).firstName && <li><span className="font-medium">First Name:</span> {(user as any).firstName}</li>}
                {(user as any).lastName && <li><span className="font-medium">Last Name:</span> {(user as any).lastName}</li>}
                {(user as any).stripeAccountId && <li><span className="font-medium">Stripe Account:</span> {(user as any).stripeAccountId}</li>}
                {(user as any).preferences && (
                  <li>
                    <span className="font-medium">Preferences:</span> {JSON.stringify((user as any).preferences)}
                  </li>
                )}
              </ul>
            </div>
          )}
        </div>
      </SheetContent>
    </Sheet>
  );
} 