"use client";

import React from "react";
import { ColumnDef } from "@tanstack/react-table";
import { DataTableColumnHeader } from "./column-header";
import { Checkbox } from "@workspace/ui/components/checkbox";
import { cn } from "@workspace/ui/lib/utils";
import { formatDistanceToNow } from "date-fns";
import { Feedback, User } from "@workspace/backend/convex/lib/types";
import { useQuery } from "convex/react";
import { api } from "@workspace/backend/convex/_generated/api";
import { UserAvatar } from "@workspace/ui/components/user-avatar";
import { 
  IconBug, 
  IconStarFilled, 
  IconStar, 
  IconRocket, 
  IconCircleCheck, 
  IconCircleMinus,
  IconClockHour4,
  IconAlertCircle
} from "@tabler/icons-react";

function includesString(rowValue: string, filterValue: string | string[]) {
  if (typeof rowValue !== "string") {
    return false;
  }

  if (Array.isArray(filterValue)) {
    return filterValue.some((value) =>
      rowValue.toLowerCase().includes(value.toLowerCase()),
    );
  }

  if (typeof filterValue !== "string") {
    return false;
  }

  return rowValue.toLowerCase().includes(filterValue.toLowerCase());
}

const UserCell = React.memo(() => {
  const user = useQuery(api.users.viewer);

  if (!user) return <div>Unknown User</div>;

  return (
    <div className="flex items-center gap-2">
      <UserAvatar user={user as User} size="sm" />
      <span>{user.name}</span>
    </div>
  );
});

UserCell.displayName = "UserCell";

const getStatusDetails = (status: Feedback["status"]) => {
  switch (status) {
    case "new":
      return { icon: IconAlertCircle, color: "text-blue-500" };
    case "in_progress":
      return { icon: IconClockHour4, color: "text-yellow-500" };
    case "done":
      return { icon: IconCircleCheck, color: "text-green-500" };
    case "rejected":
      return { icon: IconCircleMinus, color: "text-red-500" };
    default:
      return { icon: IconAlertCircle, color: "text-blue-500" };
  }
};

const getTypeDetails = (type: Feedback["type"]) => {
  switch (type) {
    case "bug":
      return { icon: IconBug, color: "text-red-500" };
    case "feature_request":
      return { icon: IconRocket, color: "text-purple-500" };
    default:
      return { icon: IconBug, color: "text-red-500" };
  }
};

export const columns: ColumnDef<Feedback>[] = [
  {
    id: "select",
    header: ({ table }) => (
      <Checkbox
        checked={
          table.getIsAllPageRowsSelected() ||
          (table.getIsSomePageRowsSelected() && true)
        }
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all"
        className="translate-y-[2px] !h-4 !w-4"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onClick={(e) => {
          e.stopPropagation();
        }}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label="Select row"
        className="translate-y-[2px] !h-4 !w-4"
      />
    ),
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: "message",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Message" />
    ),
    cell: ({ row }) => (
      <div className="max-w-[500px] truncate font-medium">
        {row.original.message}
      </div>
    ),
    enableSorting: true,
    sortingFn: (rowA, rowB) => {
      const a = rowA.original?.message || "";
      const b = rowB.original?.message || "";
      return a.localeCompare(b);
    },
  },
  {
    accessorKey: "type",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Type" />
    ),
    cell: ({ row }) => {
      const type = row.original.type;
      const { icon: TypeIcon, color } = getTypeDetails(type);
      
      return (
        <div className="flex items-center">
          <TypeIcon className={cn("mr-2 size-4", color)} aria-hidden="true" />
          <span className="capitalize">
            {type === "feature_request" ? "Feature Request" : type}
          </span>
        </div>
      );
    },
    filterFn: (row, columnId, filterValue: string | string[]) =>
      includesString(row.getValue(columnId), filterValue),
    enableSorting: true,
  },
  {
    accessorKey: "status",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Status" />
    ),
    cell: ({ row }) => {
      const status = row.original.status;
      const { icon: StatusIcon, color } = getStatusDetails(status);
      
      return (
        <div className="flex items-center">
          <StatusIcon className={cn("mr-2 size-4", color)} aria-hidden="true" />
          <span className="capitalize">{status.replace('_', ' ')}</span>
        </div>
      );
    },
    filterFn: (row, columnId, filterValue: string | string[]) =>
      includesString(row.getValue(columnId), filterValue),
    enableSorting: true,
  },
  {
    accessorKey: "createdAt",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Created" />
    ),
    cell: ({ row }) => {
      const timestamp = row.original.createdAt;
      const date = new Date(timestamp);
      
      return (
        <div className="flex flex-col">
          <span>{formatDistanceToNow(date, { addSuffix: true })}</span>
          <span className="text-xs text-muted-foreground">
            {date.toLocaleString()}
          </span>
        </div>
      );
    },
    enableSorting: true,
    sortingFn: (rowA, rowB) => {
      const a = rowA.original?.createdAt || 0;
      const b = rowB.original?.createdAt || 0;
      return a - b;
    },
  },
  {
    accessorKey: "user.username",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Submitted By" />
    ),
    cell: ({ row }) => {
      const user = row.original.user;
      if (!user || !(user as any).username || !(user as any).name) return <div>Unknown User</div>;
      return (
        <div className="flex items-center gap-2">
          <UserAvatar user={user as User} size="sm" />
          <span className="font-mono text-xs bg-zinc-100 dark:bg-zinc-800 px-2 py-1 rounded">{(user as any).username}</span>
          <span className="ml-2 text-sm text-zinc-700 dark:text-zinc-300">{(user as any).name}</span>
        </div>
      );
    },
    enableSorting: true,
  },
  {
    id: "_id",
    accessorKey: "_id",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="ID" />
    ),
    cell: ({ row }) => (
      <span className="font-mono text-xs bg-zinc-100 dark:bg-zinc-800 px-2 py-1 rounded">{row.original._id}</span>
    ),
    enableSorting: true,
    enableColumnFilter: true,
    filterFn: (row, columnId, filterValue: string | string[]) =>
      includesString(row.getValue(columnId), filterValue),
    meta: {
      label: "ID",
      placeholder: "Search by ID...",
      variant: "text",
    },
  },
];
