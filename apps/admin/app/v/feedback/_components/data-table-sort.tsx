import { Table } from "@tanstack/react-table";
import { But<PERSON> } from "@workspace/ui/components/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@workspace/ui/components/dropdown-menu";
import React from "react";
import {
  IconArrowsSort,
  IconCalendarTime,
  IconUser,
  IconClock,
  IconAntennaBars5,
  IconProgress,
  IconLetterT,
  IconSquareRoundedCheckFilled,
} from "@tabler/icons-react";
import { cn } from "@workspace/ui/lib/utils";

const sortOptions = [
  { value: "dueDate", label: "Due date", icon: IconCalendarTime },
  { value: "assignee", label: "Assignee", icon: IconUser },
  { value: "createdAt", label: "Created At", icon: IconClock },
  { value: "status", label: "Status", icon: IconProgress },
  { value: "priority", label: "Priority", icon: IconAntennaBars5 },
  { value: "title", label: "Title", icon: IconLetterT },
];

interface DataTableSortProps<TData> {
  table: Table<TData>;
}

export function DataTableSort<TData>({ table }: DataTableSortProps<TData>) {
  const [sortBy, setSortBy] = React.useState<string | undefined>();
  const [sortOrder, setSortOrder] = React.useState<"asc" | "desc">("asc");

  const sortingState = table.getState().sorting[0];
  React.useEffect(() => {
    if (sortingState) {
      setSortBy(sortingState.id);
      setSortOrder(sortingState.desc ? "desc" : "asc");
    } else {
      setSortBy(undefined);
      setSortOrder("asc");
    }
  }, [sortingState]);

  const handleSort = (value: string) => {
    const column = table.getColumn(value);
    if (!column) {
      console.warn(`Column with id '${value}' does not exist.`);
      return;
    }

    if (sortBy === value) {
      const newOrder = sortOrder === "asc" ? "desc" : "asc";
      column.toggleSorting(newOrder === "desc");
      setSortOrder(newOrder);
    } else {
      column.toggleSorting(sortOrder === "desc");
      setSortBy(value);
    }
  };

  const currentSortOption = sortOptions.find((opt) => opt.value === sortBy);

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="outline"
          size="sm"
          className="h-8 gap-2 rounded-lg flex items-center justify-start"
        >
          {currentSortOption ? (
            <>
              <span className="text-xs font-mono">
                {currentSortOption.label}
              </span>
              <span className="ml-1 text-xs bg-zinc-200 dark:bg-zinc-800 px-1 py-0.5 rounded-sm font-mono">
                {sortOrder === "asc" ? "asc" : "desc"}
              </span>
            </>
          ) : (
            "Sort"
          )}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="start" className="w-70 space-y-0.5">
        {sortOptions.map((option) => {
          const Icon = option.icon;
          const isActive = sortBy === option.value;
          return (
            <DropdownMenuItem
              key={option.value}
              onClick={() => handleSort(option.value)}
              className="flex items-center gap-2"
            >
              <Icon className="h-4 w-4" />
              <span>{option.label}</span>
              {isActive && (
                <span className="ml-auto">
                  <IconSquareRoundedCheckFilled className="text-blue-400" />
                </span>
              )}
            </DropdownMenuItem>
          );
        })}
        <DropdownMenuSeparator />
        <DropdownMenuItem
          onClick={() => {
            if (!sortBy) return;
            const column = table.getColumn(sortBy);
            if (column) {
              column.toggleSorting(false);
              setSortOrder("asc");
            }
          }}
          className={cn(
            "flex items-center gap-2",
            sortOrder === "asc" && "bg-accent",
            !sortBy && "opacity-50 cursor-not-allowed",
          )}
        >
          <IconArrowsSort className="h-4 w-4 rotate-0" />
          <span>Ascending</span>
          {sortOrder === "asc" && sortBy && (
            <span className="ml-auto">
              <IconSquareRoundedCheckFilled className="text-blue-400" />
            </span>
          )}
        </DropdownMenuItem>
        <DropdownMenuItem
          onClick={() => {
            if (!sortBy) return;
            const column = table.getColumn(sortBy);
            if (column) {
              column.toggleSorting(true);
              setSortOrder("desc");
            }
          }}
          className={cn(
            "flex items-center gap-2",
            sortOrder === "desc" && "bg-accent",
            !sortBy && "opacity-50 cursor-not-allowed",
          )}
        >
          <IconArrowsSort className="h-4 w-4 rotate-180" />
          <span>Descending</span>
          {sortOrder === "desc" && sortBy && (
            <span className="ml-auto">
              <IconSquareRoundedCheckFilled className="text-blue-400" />
            </span>
          )}
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
