"use client";

import React, { useState, useMemo } from "react";
import { useSortable } from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import { DragOverlay, useDndContext } from "@dnd-kit/core";
import { Feedback, User } from "@workspace/backend/convex/lib/types";
import { format } from "date-fns";
import { UserAvatar } from "@workspace/ui/components/user-avatar";
import { Checkbox } from "@workspace/ui/components/checkbox";
import { useCallback } from "react";
import { useQuery, useMutation } from "convex/react";
import { api } from "@workspace/backend/convex/_generated/api";
import { Id } from "@workspace/backend/convex/_generated/dataModel";
import { differenceInDays } from "date-fns";
import {
  IconClock,
  IconStar,
  IconStarFilled,
  IconEdit,
} from "@tabler/icons-react";
import {
  Toolt<PERSON>,
  TooltipContent,
  TooltipTrigger,
} from "@workspace/ui/components/tooltip";
import { But<PERSON> } from "@workspace/ui/components/button";
import { FEEDBACK_STATUS, FeedbackStatus } from "@/lib/constants";
import DropIndicator from "./drop-indicator";
import { FeedbackContextMenu } from "../../_components/feedback-context-menu";
import { StatusSelector } from "@/components/tasks/status-selector";

interface KanbanCardProps {
  feedback: Feedback;
  isSelected?: boolean;
  selectedFeedbacks?: Set<string>;
  onSelect?: (feedbackId: string) => void;
  onEdit?: (feedbackId: string) => void;
  onDelete?: (feedbackId: string) => void;
  onStatusChange?: (feedbackId: string, newStatus: Feedback["status"]) => void;
  onTypeChange?: (feedbackId: string, newType: Feedback["type"]) => void;
  columnPrefs?: {
    trackTimeInStatus: boolean;
    targetTimeInStatus: number | null;
  };
}

export function KanbanCard({
  feedback,
  isSelected = false,
  selectedFeedbacks = new Set(),
  onSelect,
  onEdit,
  onStatusChange,
  onTypeChange,
  onDelete,
  columnPrefs,
}: KanbanCardProps) {
  const user = useQuery(api.users.viewer);
  const [isHovered, setIsHovered] = useState(false);
  const toggleFavorite = useMutation(api.favorites.toggle);
  const favorites = useQuery(api.favorites.get);
  const isFavorite = favorites?.some((f) => f?.objectId === feedback._id);

  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
    isOver,
  } = useSortable({
    id: feedback._id,
    data: {
      type: "feedback",
      feedback: feedback,
      status: feedback.status,
      position: feedback.position,
      selectedFeedbacksCount: isSelected ? selectedFeedbacks.size : 1,
    },
    transition: {
      duration: 150,
      easing: "cubic-bezier(0.25, 1, 0.5, 1)",
    },
  });

  const style = useMemo(() => {
    const transformString = transform ? CSS.Transform.toString(transform) : "";
    const styleObj: React.CSSProperties = {
      transform: isDragging
        ? `rotate(2deg) ${transformString}`
        : transformString,
      transition,
      opacity: isDragging ? 0.5 : feedback.status === "done" ? 0.5 : 1,
      cursor: isDragging ? "grabbing" : "grab",
      touchAction: "none",
      position: "relative",
      zIndex: isDragging ? 100 : 1,
      display: "block",
    };

    return styleObj;
  }, [transform, transition, isDragging, feedback.status]);

  const handleCheckboxChange = useCallback(
    (checked: boolean | string) => {
      onSelect?.(feedback._id);
    },
    [onSelect, feedback._id],
  );

  const handleFavorite = useCallback(
    (e: React.MouseEvent, feedbackId: string) => {
      e.stopPropagation();
      toggleFavorite({
        objectId: feedbackId as Id<"feedback">,
      });
    },
    [toggleFavorite],
  );

  const handleEditClick = useCallback(
    (e: React.MouseEvent) => {
      e.stopPropagation();
      onEdit?.(feedback._id);
    },
    [onEdit, feedback._id],
  );

  const handleCardClick = useCallback(
    (e: React.MouseEvent) => {
      if (
        (e.target as HTMLElement).closest(".checkbox-container") ||
        (e.target as HTMLElement).closest(".edit-button-container") ||
        (e.target as HTMLElement).closest(".priority-selector-container") ||
        (e.target as HTMLElement).closest(".status-selector-container")
      ) {
        return;
      }
      handleCheckboxChange(true);
    },
    [handleCheckboxChange],
  );

  const statusChangeInfo = useMemo(() => {
    const lastStatusChange = feedback.statusHistory?.find(
      (h) => h.status === feedback.status,
    );
    if (!lastStatusChange) return null;

    const days = differenceInDays(Date.now(), lastStatusChange.timestamp);
    const daysText = `${days}d`;
    const columnName =
      FEEDBACK_STATUS.find((s) => s.value === feedback.status)?.label ||
      feedback.status;
    const fullDate = format(lastStatusChange.timestamp, "h:mm a MMMM d, yyyy");

    return {
      days,
      daysText,
      lastStatusChange,
      tooltipText: (
        <div className="flex flex-col">
          <span>Moved to {columnName}</span>
          <span className="text-xs opacity-80">{fullDate}</span>
          {columnPrefs?.trackTimeInStatus &&
            columnPrefs?.targetTimeInStatus &&
            days > columnPrefs.targetTimeInStatus && (
              <span className="text-xs text-red-500 font-medium">
                Exceeded target by {days - columnPrefs.targetTimeInStatus} days
              </span>
            )}
        </div>
      ),
    };
  }, [feedback.status, feedback.statusHistory, columnPrefs]);

  const isOverdue = useMemo(() => {
    if (
      !columnPrefs?.trackTimeInStatus ||
      !columnPrefs?.targetTimeInStatus ||
      !statusChangeInfo?.lastStatusChange
    ) {
      return false;
    }
    const daysInStatus = differenceInDays(
      Date.now(),
      statusChangeInfo.lastStatusChange.timestamp,
    );
    return daysInStatus > columnPrefs.targetTimeInStatus;
  }, [columnPrefs, statusChangeInfo]);

  const SelectionComponent = useMemo(() => {
    if (!user) return null;

    if (isHovered || isSelected) {
      return (
        <Checkbox
          checked={isSelected}
          onCheckedChange={handleCheckboxChange}
          className="translate-y-[2px] cursor-pointer"
        />
      );
    }

    return <UserAvatar user={user as User} size="sm" />;
  }, [user, isHovered, isSelected, handleCheckboxChange]);

  return (
    <>
      {isOver && (
        <DropIndicator
          beforeId={feedback._id}
          column={feedback.status as FeedbackStatus}
          isOver={isOver}
        />
      )}
      <FeedbackContextMenu
        feedback={feedback}
        isFavorite={isFavorite ?? false}
        onEdit={handleEditClick}
        onFavorite={handleFavorite}
        onStatusChange={(feedbackId, status) => 
          onStatusChange?.(feedbackId, status as Feedback["status"])
        }
        onTypeChange={onTypeChange}
        onDelete={(e, feedbackId) => onDelete?.(feedbackId)}
      >
        <div
          ref={setNodeRef}
          style={style}
          {...attributes}
          {...listeners}
          onMouseEnter={() => setIsHovered(true)}
          onMouseLeave={() => setIsHovered(false)}
          onClick={handleCardClick}
          className={`relative rounded-lg border bg-zinc-100 dark:bg-accent/50 dark:hover:hover-bg hover:bg-zinc-100 p-2 ${
            isDragging
              ? "border-blue-500"
              : isSelected
                ? "border-blue-500"
                : isOverdue
                  ? "border-red-500 bg-red-500/50 dark:bg-red-500/10"
                  : "border-accent"
          }`}
        >
          <div className={"flex flex-col"}>
            <div className={"flex flex-row items-start justify-between"}>
              <div className={"flex flex-row items-center gap-2"}>
                <div className={"flex flex-col"}>
                  {feedback._creationTime && (
                    <div className={"text-sm text-zinc-500"}>
                      {format(new Date(feedback._creationTime), "MMM dd")}
                    </div>
                  )}
                  <p className="text-sm text-zinc-800 dark:text-zinc-100 gap-2 flex flex-row items-center">
                    {feedback.message}
                  </p>
                </div>
              </div>
              <div className="checkbox-container">{SelectionComponent}</div>
            </div>
            <div className={"flex flex-row items-center gap-1 mt-1"}>
              <div className="status-selector-container">
                <StatusSelector
                  status={feedback.status as FeedbackStatus}
                  feedbackId={feedback._id}
                  taskId={feedback._id}
                  onStatusChange={(taskId, feedbackId, status) => {
                    if (onStatusChange) {
                      onStatusChange(feedbackId, status as Feedback["status"]);
                    }
                  }}
                />
              </div>

              <div className="ml-auto flex items-center gap-1 edit-button-container">
                <div className="flex items-center gap-1">
                  {isHovered && (
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button
                          onClick={(e) => {
                            e.stopPropagation();
                            onEdit?.(feedback._id);
                          }}
                          className="h-6 w-6 text-zinc-500 hover:text-zinc-600 dark:hover:text-zinc-300 transition-colors"
                          variant="ghost"
                          size="icon"
                        >
                          <IconEdit className="w-4 h-4" />
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent side="bottom">Edit feedback</TooltipContent>
                    </Tooltip>
                  )}

                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        onClick={(e) => handleFavorite(e, feedback._id)}
                        className="h-6 w-6 text-zinc-500 hover:text-yellow-400 transition-colors"
                        variant="ghost"
                        size="icon"
                      >
                        {isFavorite ? (
                          <IconStarFilled className="w-4 h-4 text-yellow-400" />
                        ) : (
                          isHovered && <IconStar className="w-4 h-4" />
                        )}
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent side="bottom">
                      {isFavorite
                        ? "Remove from favorites"
                        : "Add to favorites"}
                    </TooltipContent>
                  </Tooltip>
                </div>

                {statusChangeInfo && (
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <div className="flex items-center gap-1 text-xs text-zinc-500 cursor-default">
                        <IconClock className="w-3 h-3" />
                        <span>{statusChangeInfo.daysText}</span>
                      </div>
                    </TooltipTrigger>
                    <TooltipContent side="bottom">
                      {statusChangeInfo.tooltipText}
                    </TooltipContent>
                  </Tooltip>
                )}
              </div>
            </div>
          </div>
        </div>
      </FeedbackContextMenu>
    </>
  );
}

export function KanbanCardDragOverlay() {
  const { active } = useDndContext();

  if (!active) return null;

  const feedback = active.data.current?.feedback;
  const selectedFeedbacksCount = active.data.current?.selectedFeedbacksCount || 1;

  return (
    <DragOverlay>
      {feedback && (
        <div
          className="relative rounded-lg border-blue-500 bg-zinc-100 dark:bg-accent/50 p-2 shadow-lg"
          style={{
            borderWidth: `${Math.min(selectedFeedbacksCount * 2, 8)}px`,
            transform: `translateY(${(selectedFeedbacksCount - 1) * -4}px)`,
          }}
        >
          <KanbanCard feedback={feedback} />
        </div>
      )}
    </DragOverlay>
  );
}
