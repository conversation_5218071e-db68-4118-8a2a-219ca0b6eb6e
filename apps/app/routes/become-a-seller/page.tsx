"use client";

import { useState } from "react";
import { SellerExperienceSelection } from "../../components/seller-experience-selection";
import { useRouter } from "next/navigation";
import { useQuery } from "convex/react";
import { api } from "@workspace/backend";

export default function BecomeASellerPage() {
  const user = useQuery(api.users.viewer);
  const router = useRouter();
  const [submitted, setSubmitted] = useState(false);

  const handleBack = () => {
    router.push("/");
  };

  const handleNext = (
    hasExperience: boolean,
    data: {
      platform?: string;
      monthlyRevenue?: string;
      goal?: string;
      socialMedia?: any[];
      additionalInfo?: string;
    }
  ) => {
    setSubmitted(true);
    
    setTimeout(() => {
      router.push("/dashboard");
    }, 3000);
  };

  if (submitted) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen p-4 text-center">
        <div className="max-w-md">
          <div className="mb-4 text-green-500">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" className="w-16 h-16 mx-auto">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <h1 className="text-2xl font-bold mb-2">Application Submitted!</h1>
          <p className="text-zinc-600 dark:text-zinc-400 mb-6">
            Thank you for your interest in becoming a seller. We&apos;ll review your application and get back to you soon.
          </p>
          <p className="text-sm text-zinc-500">Redirecting to dashboard...</p>
        </div>
      </div>
    );
  }

  if (user === undefined) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen p-4">
        <div className="w-16 h-16 border-4 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
        <p className="mt-4 text-zinc-600 dark:text-zinc-400">Loading...</p>
      </div>
    );
  }

  return (
    <div className="relative min-h-screen">
      <SellerExperienceSelection 
        onBack={handleBack} 
        onNext={handleNext}
        userEmail={user?.email}
      />
    </div>
  );
} 