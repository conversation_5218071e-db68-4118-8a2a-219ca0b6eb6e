"use client";
import { usePathname, useRouter } from "next/navigation";
import { useQuery } from "convex/react";
import { api } from "@workspace/backend/convex/_generated/api";
import { Dialog, DialogContent, DialogTitle } from "@workspace/ui/components/dialog";
import StreamDetailContent from "@/components/stream-detail-content";
import { VisuallyHidden } from "@workspace/ui/components/visually-hidden";
import { useCurrentUser } from "@/hooks/use-current-user";
import { Id } from "@workspace/backend/convex/_generated/dataModel";
import { useEffect } from "react";

export default function StreamProvider() {
  const pathname = usePathname();
  const router = useRouter();
  const { user: currentUser } = useCurrentUser();

  const match = /^\/stream\/([^/]+)$/.exec(pathname || "");
  const streamId = match ? match[1] : null;

  const stream = useQuery(
    api.streams.getStream,  
    streamId
      ? {
          streamId: streamId as Id<"streams">,
        }
      : "skip"
  );

  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === "Escape" && streamId) {
        router.back();
      }
    };

    if (streamId) {
      document.addEventListener("keydown", handleEscape);
      return () => document.removeEventListener("keydown", handleEscape);
    }
  }, [streamId, router]);

  useEffect(() => {
    if (streamId && stream === null) {
      router.push("/");
    }
  }, [streamId, stream, router]);

  if (!streamId) return null;

  const handleClose = () => {
    router.back();
  };

  return (
    <Dialog open={true} onOpenChange={open => {
      if (!open) router.back();
    }}>
      <VisuallyHidden>
        <DialogTitle>{stream?.title || 'Stream Viewer'}</DialogTitle>
      </VisuallyHidden>
      <DialogContent
        close={false}
        className="fixed !w-screen !h-screen !max-w-none max-h-none p-0 flex flex-col items-center justify-center bg-black !rounded-none !shadow-none"
        style={{
          borderRadius: '0 !important',
          maxWidth: '100vw !important',
          maxHeight: '100vh !important',
          width: '100vw !important',
          height: '100vh !important',
          padding: 0,
        }}
      >
        <div className="w-full h-full flex flex-col items-center justify-center">
          {stream && currentUser ? (
            <StreamDetailContent stream={stream} user={currentUser} onClose={handleClose} />
          ) : (
            <div className="text-white">Loading...</div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
} 