import { useCallback, useEffect, useRef } from "react";
import type { RefObject } from "react";

export function useEventListener(
  eventName: string,
  handler: (event: any) => void,
  elementOrRef?: HTMLElement | Document | Window | RefObject<HTMLElement | null>,
) {
  const savedHandler = useRef(handler);

  useEffect(() => {
    savedHandler.current = handler;
  }, [handler]);

  useEffect(() => {
    let target: HTMLElement | Document | Window | null | undefined = window;
    if (elementOrRef) {
      if (typeof (elementOrRef as RefObject<HTMLElement>).current !== "undefined") {
        target = (elementOrRef as RefObject<HTMLElement | null>).current;
      } else {
        target = elementOrRef as HTMLElement | Document | Window;
      }
    }
    const isSupported = target && target.addEventListener;
    if (!isSupported || !target) return;

    const eventListener = (event: any) => savedHandler.current(event);

    target.addEventListener(eventName, eventListener);

    return () => {
      if (target) {
        target.removeEventListener(eventName, eventListener);
      }
    };
  }, [eventName, elementOrRef]);
}
