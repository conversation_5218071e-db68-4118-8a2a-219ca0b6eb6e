import { toast } from "sonner";
import { useEffect, useState } from "react";
import { JwtPayload, jwtDecode } from "jwt-decode";
import { useAction } from "convex/react";
import { api } from "@workspace/backend/convex/_generated/api";
import { Id } from "@workspace/backend/convex/_generated/dataModel";

export const useViewerToken = (streamId: string) => {
  const [token, setToken] = useState("");
  const [name, setName] = useState("");
  const [identity, setIdentity] = useState("");

  const generateViewerToken = useAction(api.integration.livekit.generateViewerToken);

  useEffect(() => {
    const fetchToken = async () => {
      try {
        const viewerToken = await generateViewerToken({ streamId: streamId as Id<"streams"> });
        setToken(viewerToken);

        const decodedToken = jwtDecode(viewerToken) as JwtPayload & {
          name?: string;
        };
        const name = decodedToken?.name;
        const identity = decodedToken?.sub || decodedToken?.jti;

        if (identity) {
          setIdentity(identity);
        }
        if (name) {
          setName(name);
        }
      } catch (e) {
        console.error("Failed to create viewer token:", e);
        toast.error("Failed to create token");
      }
    };

    if (streamId) {
      fetchToken();
    }
  }, [streamId, generateViewerToken]);

  return { token, name, identity };
};
