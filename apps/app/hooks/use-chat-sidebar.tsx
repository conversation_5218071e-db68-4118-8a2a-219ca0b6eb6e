"use client";

import { createContext, useContext, useState, useEffect, useCallback, ReactNode } from "react";
import { setLocalStorage, getLocalStorage } from "@/lib/local-storage";
import { STORAGE_PREFIX } from "@/lib/constants";

export enum ChatVariant {
  CHAT = `${STORAGE_PREFIX}chat`,
  COMMUNITY = `${STORAGE_PREFIX}community`,
}

interface ChatSidebarStore {
  collapsed: boolean;
  variant: ChatVariant;
  onExpand: () => void;
  onCollapse: () => void;
  onChangeVariant: (variant: ChatVariant) => void;
}

const COLLAPSED_KEY = "chatSidebarCollapsed";
const VARIANT_KEY = "chatSidebarVariant";

function getCollapsed(): boolean {
  const value = getLocalStorage(COLLAPSED_KEY);
  return value === "true";
}

function setCollapsed(val: boolean) {
  setLocalStorage(COLLAPSED_KEY, val ? "true" : "false");
}

function getVariant(): ChatVariant {
  const value = getLocalStorage(VARIANT_KEY);
  return value === ChatVariant.COMMUNITY ? ChatVariant.COMMUNITY : ChatVariant.CHAT;
}

function setVariant(variant: ChatVariant) {
  setLocalStorage(VARIANT_KEY, variant);
}

const ChatSidebarContext = createContext<ChatSidebarStore | undefined>(undefined);

export function ChatSidebarProvider({ children }: { children: ReactNode }) {
  const [collapsed, setCollapsedState] = useState<boolean>(getCollapsed());
  const [variant, setVariantState] = useState<ChatVariant>(getVariant());

  useEffect(() => {
    setCollapsed(collapsed);
  }, [collapsed]);

  useEffect(() => {
    setVariant(variant);
  }, [variant]);

  useEffect(() => {
    const handler = (e: StorageEvent) => {
      if (e.key === COLLAPSED_KEY) {
        setCollapsedState(getCollapsed());
      }
      if (e.key === VARIANT_KEY) {
        setVariantState(getVariant());
      }
    };
    window.addEventListener("storage", handler);
    return () => window.removeEventListener("storage", handler);
  }, []);

  const onExpand = useCallback(() => setCollapsedState(false), []);
  const onCollapse = useCallback(() => setCollapsedState(true), []);
  const onChangeVariant = useCallback((v: ChatVariant) => setVariantState(v), []);

  const value: ChatSidebarStore = {
    collapsed,
    variant,
    onExpand,
    onCollapse,
    onChangeVariant,
  };

  return (
    <ChatSidebarContext.Provider value={value}>{children}</ChatSidebarContext.Provider>
  );
}

export function useChatSidebar(): ChatSidebarStore {
  const ctx = useContext(ChatSidebarContext);
  if (!ctx) {
    throw new Error("useChatSidebar must be used within a ChatSidebarProvider");
  }
  return ctx;
}