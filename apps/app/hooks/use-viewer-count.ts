import { useState, useEffect } from "react";
import { useAction } from "convex/react";
import { api } from "@workspace/backend/convex/_generated/api";
import { Id } from "@workspace/backend/convex/_generated/dataModel";

interface UseViewerCountOptions {
  streamId: Id<"streams">;
  isLive: boolean;
  updateInterval?: number; // in milliseconds, default 30000 (30 seconds)
  enabled?: boolean; // whether to track viewer count, default true
}

interface UseViewerCountReturn {
  viewerCount: number | null;
  isLoading: boolean;
  error: string | null;
  refresh: () => Promise<void>;
}

export function useViewerCount({
  streamId,
  isLive,
  updateInterval = 30000,
  enabled = true,
}: UseViewerCountOptions): UseViewerCountReturn {
  const [viewerCount, setViewerCount] = useState<number | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  const getParticipantCountAction = useAction(api.integration.livekit.getRoomParticipantsCount);

  const fetchViewerCount = async () => {
    if (!enabled || !isLive || !streamId) {
      setViewerCount(null);
      setIsLoading(false);
      setError(null);
      return;
    }

    try {
      setError(null);
      const roomName = `stream-${streamId}`;
      const count = await getParticipantCountAction({ roomName });
      setViewerCount(count);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Failed to fetch viewer count";
      console.error("Failed to fetch viewer count:", err);
      setError(errorMessage);
      setViewerCount(0);
    } finally {
      setIsLoading(false);
    }
  };

  // Initial fetch and periodic updates
  useEffect(() => {
    if (!enabled || !isLive || !streamId) {
      setViewerCount(null);
      setIsLoading(false);
      setError(null);
      return;
    }

    setIsLoading(true);
    
    // Initial fetch
    fetchViewerCount();

    // Set up periodic updates
    const intervalId = setInterval(fetchViewerCount, updateInterval);

    return () => clearInterval(intervalId);
  }, [streamId, isLive, enabled, updateInterval, getParticipantCountAction]);

  const refresh = async () => {
    setIsLoading(true);
    await fetchViewerCount();
  };

  return {
    viewerCount,
    isLoading,
    error,
    refresh,
  };
}

export function formatViewerCount(count: number | null): string {
  if (count === null || count === 0) return "0";
  
  if (count >= 1000000) {
    return `${(count / 1000000).toFixed(1)}M`;
  }
  
  if (count >= 1000) {
    return `${(count / 1000).toFixed(1)}k`;
  }
  
  return count.toString();
} 