import { Id } from "@workspace/backend/convex/_generated/dataModel";
import type { UserRole} from "@workspace/backend/convex/lib/types";

export interface Follow {
  _id: string;
  followerId: string;
  followingId: string;
  _creationTime: number;
}

export type User = {
  name: string | null;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  image: string | Id<"_storage">;
  coverImage: string | Id<"_storage">;
  emailVerificationTime: number;
  phoneVerificationTime: number;
  isAnonymous: boolean;
  lastLoginType: string;
  finishedSignUp: boolean;
  isSellerInterested: boolean;
  color: string;
  theme: string;
  lastSeen: number;
  status: string;
  safetyGuidelinesAgreement: {
    agreed: boolean;
    agreedAt: number;
  };
  username: string;
  role: UserRole;
  bio: string;
  sellerProfile: {
    categories: string[];
    verified: boolean;
    joinedAt: number;
    percentage: number;
  };
  preferences: {
    notifications: boolean;
    emailUpdates: boolean;
    darkMode: boolean;
    categories: string[];
    subcategories: string[];
    shippingAddresses: {
      street: string;
      address2: string;
      city: string;
      state: string;
      country: string;
      zipCode: string;
      isDefault: boolean;
      fullName: string;
      isReturn: boolean;
    }[];
  } | null;
  stripeAccountId: string;
  stripeCustomerId: string;
  paymentMethods: {
    id: string;
    last4: string;
    brand: string;
    expMonth: number;
    expYear: number;
    isDefault: boolean;
  }[];
  _creationTime: number;
  _id: string;
  hashedPassword: boolean;
  isHost?: boolean;
  avatarUrl?: string | undefined;
  coverImageUrl?: string | undefined;
  accounts: { provider: string }[];
  followers: (Follow | null)[];
  following: (Follow | null)[];
}
