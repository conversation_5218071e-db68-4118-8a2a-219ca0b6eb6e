import type { User } from "@workspace/backend/convex/lib/types";
import type { UserRole } from "./types";

export const ADMIN_ROLES: UserRole[] = ["admin", "seller", "user"];

export function hasAdminRole(user: User): boolean {
  return ADMIN_ROLES.includes(user.role as UserRole);
}

export function hasRole(user: User, roles: UserRole[]): boolean {
  return roles.includes(user.role as UserRole);
}

export const Permissions = {
  canManageUsers: hasAdminRole,
  canManageSettings: hasAdminRole,
  canFilterTasks: hasAdminRole,
  canGroupByAssignee: hasAdminRole,
} as const;
