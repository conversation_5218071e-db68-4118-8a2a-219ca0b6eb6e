import React from "react";
import { cva, type VariantProps } from "class-variance-authority";

import { cn } from "@workspace/ui/lib/utils";
import {
  Avatar,
  AvatarFallback,
  AvatarImage,
} from "@workspace/ui/components/avatar";
import { Skeleton } from "@workspace/ui/components/skeleton";
import { User } from "@/lib/types";
import { getAvatarImageUrl } from "@/lib/utils";
import { Id } from "@workspace/backend/convex/_generated/dataModel";
import { LiveBadge } from "@/components/live-badge";

const avatarSizes = cva("", {
  variants: {
    size: {
      default: "h-8 w-8",
      sm: "h-6 w-6",
      lg: "h-14 w-14",
      xs: "h-4 w-4",
    },
  },
  defaultVariants: {
    size: "default",
  },
});

const statusSizes = cva("", {
  variants: {
    size: {
      default: "size-3",
      sm: "size-2.5",
      lg: "size-4",
    },
  },
  defaultVariants: {
    size: "default",
  },
});

interface UserAvatarProps extends VariantProps<typeof avatarSizes> {
  user: Partial<User> & {
    _id?: Id<"users"> | string;
    name: string;
    username: string;
    image?: string | null;
    status?: "online" | "offline";
    color?: string;
    isLive?: boolean;
  } | any;
  height?: number;
  width?: number;
  showOnlineIndicator?: boolean;
  isLive?: boolean;
}

interface UserAvatarSkeletonProps extends VariantProps<typeof avatarSizes> {}

export function UserAvatar({
  user,
  size = "default",
  height,
  width,
  showOnlineIndicator = true,
  isLive,
}: UserAvatarProps) {

  return (
    <div className="relative w-fit flex flex-col items-center">
      <Avatar
        className={cn(
          "ring-1 ring-offset-background ring-input ring-offset-1",
          isLive && "ring-rose-500 ring-1",
          avatarSizes({ size }),
        )}
        style={{ height, width, 
          backgroundColor: user?.color ? `${user.color}50` : undefined, 
          // border: `1px solid ${user?.color}` 
        }}
      >
        <AvatarImage src={getAvatarImageUrl(user.image) ?? user.image} className="object-cover" 
          style={{ height, width, 
          backgroundColor: user?.color ? `${user.color}80` : undefined, 
          // border: `1px solid ${user?.color}` 
        }} />
        <AvatarFallback style={{ height, width, 
          backgroundColor: user?.color ? `${user.color}80` : undefined, 
          // border: `1px solid ${user?.color}` 
        }}>
          {user?.username?.[0] || "?"}
          {user?.username?.[user?.username?.length - 1] || "?"}
        </AvatarFallback>
      </Avatar>
      {isLive && (
        <div className="absolute -bottom-3 right-1/2 transform translate-x-1/2">
          <LiveBadge />
        </div>
      )}
    </div>
  );
}

export function UserAvatarSkeleton({ size }: UserAvatarSkeletonProps) {
  return <Skeleton className={cn("rounded-full", avatarSizes({ size }))} />;
}
