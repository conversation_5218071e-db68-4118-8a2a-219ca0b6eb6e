"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@workspace/ui/components/button";
import { Card } from "@workspace/ui/components/card";
import { RadioGroup, RadioGroupItem } from "@workspace/ui/components/radio-group";
import { Label } from "@workspace/ui/components/label";
import { Checkbox } from "@workspace/ui/components/checkbox";
import { Input } from "@workspace/ui/components/input";
import { Textarea } from "@workspace/ui/components/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@workspace/ui/components/select";
import { useMutation } from "convex/react";
import { api } from "@workspace/backend";
import { PLATFORMS } from "../lib/constants";

interface SocialMediaPlatform {
  id: string;
  name: string;
  username: string;
  selected: boolean;
}

interface SocialMediaSubmission {
  platform: string;
  username?: string;
}

interface Props {
  onBack: () => void;
  onNext: (
    hasExperience: boolean, 
    data: {
      platform?: string; 
      platformLink?: string;
      monthlyRevenue?: string; 
      goal?: string;
      socialMedia?: SocialMediaPlatform[];
      additionalInfo?: string;
    }
  ) => void;
  userEmail?: string;
}

export function SellerExperienceSelection({
  onBack,
  onNext,
  userEmail,
}: Props) {
  const [hasExperience, setHasExperience] = useState<boolean | null>(null);
  const [platform, setPlatform] = useState<string>("");
  const [platformLink, setPlatformLink] = useState<string>("");
  const [monthlyRevenue, setMonthlyRevenue] = useState<string>("");
  const [goal, setGoal] = useState<string>("");
  const [additionalInfo, setAdditionalInfo] = useState<string>("");
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const [errorMessage, setErrorMessage] = useState<string>("");
  const [successMessage, setSuccessMessage] = useState<string>("");
  
  const submitInterest = useMutation(api.interestedSellers.submitSellerInterest);
  
  const [socialMedia, setSocialMedia] = useState<SocialMediaPlatform[]>([
    { id: "instagram", name: "Instagram", username: "", selected: false },
    { id: "tiktok", name: "TikTok", username: "", selected: false },
    { id: "twitch", name: "Twitch", username: "", selected: false },
    { id: "youtube", name: "YouTube", username: "", selected: false },
    { id: "twitter", name: "Twitter", username: "", selected: false },
    { id: "na", name: "N/A", username: "", selected: false },
  ]);
  
  const [step, setStep] = useState<"initial" | "platform" | "revenue" | "goal" | "social" | "additional">("initial");

  const handleOptionSelect = (value: boolean) => {
    setHasExperience(value);
    if (value) {
      setStep("platform");
    } else {
      setPlatform("");
      setPlatformLink("");
      setMonthlyRevenue("");
      setStep("goal");
    }
  };

  const handlePlatformSelect = (value: string) => {
    setPlatform(value);
    
    if (value === "na") {
      setPlatformLink("");
    }
  };

  const handleSocialMediaChange = (id: string, checked: boolean) => {
    if (id === "na" && checked) {
      setSocialMedia(socialMedia.map(item => ({
        ...item,
        selected: item.id === "na",
        username: ""
      })));
      return;
    }
    
    setSocialMedia(socialMedia.map(item => {
      if (item.id === id) {
        return { ...item, selected: checked };
      }
      if (item.id === "na" && checked) {
        return { ...item, selected: false };
      }
      return item;
    }));
  };

  const handleSocialUsernameChange = (id: string, username: string) => {
    setSocialMedia(socialMedia.map(item => 
      item.id === id ? { ...item, username } : item
    ));
  };

  const handleBack = () => {
    if (step === "additional") {
      setStep("social");
    } else if (step === "social") {
      if (hasExperience) {
        setStep("goal");
      } else {
        setStep("goal");
      }
    } else if (step === "goal") {
      if (hasExperience) {
        setStep("revenue");
      } else {
        setStep("initial");
      }
    } else if (step === "revenue") {
      setStep("platform");
    } else if (step === "platform") {
      setStep("initial");
    } else {
      onBack();
    }
  };

  const handleContinue = async () => {
    setErrorMessage("");
    setSuccessMessage("");

    if (step === "initial") {
      if (hasExperience) {
        setStep("platform");
      } else {
        setStep("goal");
      }
    } else if (step === "platform" && platform) {
      setStep("revenue");
    } else if (step === "revenue" && monthlyRevenue) {
      setStep("goal");
    } else if (step === "goal" && goal) {
      setStep("social");
    } else if (step === "social" && isSocialMediaValid) {
      setStep("additional");
    } else if (step === "additional") { 
      const selectedSocialMediaForBackend: SocialMediaSubmission[] = socialMedia
        .filter(item => item.selected)
        .map(item => ({
          platform: item.id,
          username: item.username || undefined
        }));
      
      try {
        setIsSubmitting(true);
        
        const submissionTimeout = setTimeout(() => {
          setErrorMessage("Submission is taking longer than expected. Please try again later.");
          setIsSubmitting(false);
        }, 10000); // 10 seconds timeout
        
        const emailToUse = userEmail || "<EMAIL>";
        
        await submitInterest({
          email: emailToUse,
          hasSellingExperience: !!hasExperience,
          platform: platform || undefined,
          platformLink: platformLink || undefined,
          monthlyRevenue: monthlyRevenue || undefined,
          primaryGoal: goal || undefined,
          socialMedia: selectedSocialMediaForBackend.length > 0 ? selectedSocialMediaForBackend : undefined,
          additionalInfo: additionalInfo || undefined,
          useLoopsEmail: true,
        });
        
        clearTimeout(submissionTimeout);
        
        setSuccessMessage("Application submitted! We've received your seller application and will be in touch soon.");
        setIsSubmitting(false);
        
        setTimeout(() => {
          onBack();
        }, 1500);
        
        if (hasExperience) {
          onNext(true, { additionalInfo: "Submission complete" });
        } else {
          onNext(false, { additionalInfo: "Submission complete" });
        }
      } catch (error) {
        console.error("Error submitting seller interest:", error);
        setErrorMessage("We couldn't submit your application. Please try again.");
        setIsSubmitting(false);
      }
    }
  };
  
  const isAnySocialMediaSelected = socialMedia.some(item => item.selected);
  const areSelectedSocialMediaValid = socialMedia.every(item => 
    !item.selected || item.id === "na" || item.username.trim().length > 0
  );
  const isSocialMediaValid = isAnySocialMediaSelected && areSelectedSocialMediaValid;

  const revenueOptions = [
    { value: "less-than-1000", label: "Less than $1,000" },
    { value: "1000-5000", label: "$1,000 - $5,000" },
    { value: "5000-20000", label: "$5,000 - $20,000" },
    { value: "20000-50000", label: "$20,000 - $50,000" },
    { value: "50000-plus", label: "$50,000+" },
  ];

  const goalOptions = [
    { value: "full-time-business", label: "I plan to create a full-time business and primary source of income" },
    { value: "expand-business", label: "I plan to expand my existing online or physical business to our platform" },
    { value: "brand-distribution", label: "I'm a brand looking for another distribution channel" },
    { value: "secondary-income", label: "I have a full-time job and plan to use this as a secondary income stream" },
    { value: "community", label: "I want to meet other passionate buyers and sellers" },
    { value: "not-sure", label: "I'm not sure what my goal is yet" },
  ];

  const isInitialStep = step === "initial";
  const isPlatformStep = step === "platform";
  const isRevenueStep = step === "revenue";
  const isGoalStep = step === "goal";
  const isSocialStep = step === "social";
  const isAdditionalStep = step === "additional";

  const getPlatformLabel = (value: string) => {
    const platform = PLATFORMS.find(p => p.value === value);
    return platform ? platform.label : "";
  };

  const shouldShowPlatformLink = platform && platform !== "na";

  return (
    <div className="flex flex-col">
      <div className="bg-gradient-to-br from-red-400/30 to-blue-500/20 px-8 py-10">
        <div className="flex-none mb-6">
          <div className="w-full h-2 bg-primary/10 rounded-full overflow-hidden">
            <div className={`h-full ${isAdditionalStep ? 'w-full' : 'w-[60%]'} bg-gradient-to-r from-red-400 to-blue-500 rounded-full`}></div>
          </div>
          <div className="flex justify-between text-sm mt-3">
            <span className="text-zinc-500">Guidelines</span>
            <span className="text-zinc-500">Categories</span>
            <span className={`${isAdditionalStep ? 'text-zinc-500' : 'font-medium'}`}>Experience</span>
            <span className={`${isAdditionalStep ? 'font-medium' : 'text-zinc-500'}`}>Additional Details</span>
          </div>
        </div>

        <div className="flex-none">
          {isInitialStep && (
            <>
              <h1 className="text-3xl font-bold mb-2">
                Are you currently selling somewhere else?
              </h1>
              <p className="text-zinc-600 dark:text-zinc-400">
                We&apos;ll tailor your experience based on what you pick.
              </p>
            </>
          )}
          {isPlatformStep && (
            <>
              <h1 className="text-3xl font-bold mb-2">
                Where have you been selling?
              </h1>
              <p className="text-zinc-600 dark:text-zinc-400">
                This info helps us tailor our seller tools for your specific needs.
              </p>
            </>
          )}
          {isRevenueStep && (
            <>
              <h1 className="text-3xl font-bold mb-2">
                What&apos;s your monthly revenue?
              </h1>
              <p className="text-zinc-600 dark:text-zinc-400">
                This helps us understand your business scale and customize features accordingly.
              </p>
            </>
          )}
          {isGoalStep && (
            <>
              <h1 className="text-3xl font-bold mb-2">
                What is your primary goal on our platform?
              </h1>
              <p className="text-zinc-600 dark:text-zinc-400">
                This info helps us tailor our seller tools for businesses big and small.
              </p>
            </>
          )}
          {isSocialStep && (
            <>
              <h1 className="text-3xl font-bold mb-2">
                Do you use social media to sell or promote your shop?
              </h1>
              <p className="text-zinc-600 dark:text-zinc-400">
                This info helps us tailor our seller tools for businesses big and small.
              </p>
            </>
          )}
          {isAdditionalStep && (
            <>
              <h1 className="text-3xl font-bold mb-2">
                Any additional information?
              </h1>
              <p className="text-zinc-600 dark:text-zinc-400">
                Tell us anything else that might help us understand your business better.
              </p>
            </>
          )}
        </div>
      </div>

      <div className="px-8 py-6 overflow-y-auto sm:!h-[calc(100vh-18.75rem)] h-full">
        {successMessage && (
          <div className="w-full max-w-xl mx-auto mb-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-md p-4 text-green-700 dark:text-green-300">
            {successMessage}
          </div>
        )}
        
        {errorMessage && (
          <div className="w-full max-w-xl mx-auto mb-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md p-4 text-red-700 dark:text-red-300">
            {errorMessage}
          </div>
        )}
        
        {isInitialStep && (
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-6 max-w-xl mx-auto w-full">
            <div 
              className="relative rounded-xl overflow-hidden border group cursor-pointer transition-all duration-300 transform hover:-translate-y-1"
              onClick={() => handleOptionSelect(true)}
            > 
              {hasExperience === true && (
                <div className="absolute -inset-[3px] rounded-[14px] bg-gradient-to-br from-red-400/70 to-blue-500/70 animate-pulse z-10"></div>
              )}
              
              <Card
                className={`relative overflow-hidden border-0 shadow-lg hover:shadow-xl p-6 h-48 flex flex-col items-center justify-center ${
                  hasExperience === true ? "shadow-xl" : ""
                }`}
              >
                <h3 className="font-bold text-xl text-center">Yes</h3>
                <p className="text-sm text-zinc-500 text-center mt-2">I&apos;m already selling on other platforms</p>
                
                {hasExperience === true && (
                  <div className="absolute top-3 right-3 bg-white dark:bg-zinc-800 rounded-full p-1 shadow-md z-20">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="w-5 h-5 text-primary">
                      <path fillRule="evenodd" d="M19.916 4.626a.75.75 0 01.208 1.04l-9 13.5a.75.75 0 01-1.154.114l-6-6a.75.75 0 011.06-1.06l5.353 5.353 8.493-12.739a.75.75 0 011.04-.208z" clipRule="evenodd" />
                    </svg>
                  </div>
                )}
              </Card>
            </div>
            
            <div 
              className="relative rounded-xl overflow-hidden border group cursor-pointer transition-all duration-300 transform hover:-translate-y-1"
              onClick={() => handleOptionSelect(false)}
            >
              {hasExperience === false && (
                <div className="absolute -inset-[3px] rounded-[14px] bg-gradient-to-br from-red-400/70 to-blue-500/70 animate-pulse z-10"></div>
              )}
              
              <Card
                className={`relative overflow-hidden border-0 shadow-lg hover:shadow-xl p-6 h-48 flex flex-col items-center justify-center ${
                  hasExperience === false ? "shadow-xl" : ""
                }`}
              >
                <h3 className="font-bold text-xl text-center">No</h3>
                <p className="text-sm text-zinc-500 text-center mt-2">I&apos;m new to selling online</p>
                
                {hasExperience === false && (
                  <div className="absolute top-3 right-3 bg-white dark:bg-zinc-800 rounded-full p-1 shadow-md z-20">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="w-5 h-5 text-primary">
                      <path fillRule="evenodd" d="M19.916 4.626a.75.75 0 01.208 1.04l-9 13.5a.75.75 0 01-1.154.114l-6-6a.75.75 0 011.06-1.06l5.353 5.353 8.493-12.739a.75.75 0 011.04-.208z" clipRule="evenodd" />
                    </svg>
                  </div>
                )}
              </Card>
            </div>
          </div>
        )}

        {isPlatformStep && (
          <div className="w-full max-w-xl mx-auto bg-white dark:bg-zinc-900 rounded-xl shadow-md p-6 border border-zinc-200 dark:border-zinc-800">
            <h3 className="text-lg font-semibold mb-2">Where else have you sold?</h3>
            <p className="text-sm text-zinc-600 dark:text-zinc-400 mb-4">
              This info helps us tailor our seller tools for businesses big and small.
            </p>
            
            <Select value={platform} onValueChange={handlePlatformSelect}>
              <SelectTrigger className="w-full">
                <SelectValue placeholder="Select One" />
              </SelectTrigger>
              <SelectContent>
                {PLATFORMS.map((item) => (
                  <SelectItem key={item.value} value={item.value}>
                    {item.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            {shouldShowPlatformLink && (
              <div className="mt-4">
                <Label htmlFor="platform-link" className="text-sm mb-1 block">
                  {platform === "own-website" ? "Your website URL" : `Your ${getPlatformLabel(platform)} username or store link`}
                </Label>
                <Input 
                  id="platform-link"
                  placeholder={platform === "own-website" ? "https://yourwebsite.com" : `Enter your ${getPlatformLabel(platform)} username or link`}
                  value={platformLink}
                  onChange={(e) => setPlatformLink(e.target.value)}
                  className="w-full"
                />
              </div>
            )}
          </div>
        )}

        {isRevenueStep && (
          <div className="w-full max-w-xl mx-auto bg-white dark:bg-zinc-900 rounded-xl shadow-md p-6 border border-zinc-200 dark:border-zinc-800">
            <h3 className="text-lg font-semibold mb-2">On average, how much do you make per month from selling?</h3>
            <p className="text-sm text-zinc-600 dark:text-zinc-400 mb-4">
              This info helps us tailor our seller tools for businesses big and small.
            </p>
            
            <Select value={monthlyRevenue} onValueChange={setMonthlyRevenue}>
              <SelectTrigger className="w-full">
                <SelectValue placeholder="Select One" />
              </SelectTrigger>
              <SelectContent>
                {revenueOptions.map((item) => (
                  <SelectItem key={item.value} value={item.value}>
                    {item.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        )}

        {isGoalStep && (
          <div className="w-full max-w-xl mx-auto bg-white dark:bg-zinc-900 rounded-xl shadow-md p-6 border border-zinc-200 dark:border-zinc-800">
            <h3 className="text-lg font-semibold mb-2">What is your primary goal on our platform?</h3>
            <p className="text-sm text-zinc-600 dark:text-zinc-400 mb-4">
              This info helps us tailor our seller tools for businesses big and small.
            </p>
            
            <RadioGroup value={goal} onValueChange={setGoal} className="flex flex-col space-y-3">
              {goalOptions.map((item) => (
                <div key={item.value} className="flex items-center space-x-2 rounded-lg border p-3 cursor-pointer hover:bg-zinc-50 dark:hover:bg-zinc-800/50 transition-colors">
                  <RadioGroupItem value={item.value} id={item.value} />
                  <Label htmlFor={item.value} className="flex-1 cursor-pointer font-medium">
                    {item.label}
                  </Label>
                </div>
              ))}
            </RadioGroup>
          </div>
        )}

        {isSocialStep && (
          <div className="w-full max-w-xl mx-auto bg-white dark:bg-zinc-900 rounded-xl shadow-md p-6 border border-zinc-200 dark:border-zinc-800">
            <h3 className="text-lg font-semibold mb-2">Do you use social media to sell or promote your shop?</h3>
            <p className="text-sm text-zinc-600 dark:text-zinc-400 mb-4">
              This info helps us tailor our seller tools for businesses big and small.
            </p>
            
            <div className="flex flex-col space-y-4">
              <p className="text-sm font-medium">Select all that apply</p>
              
              <div className="space-y-3">
                {socialMedia.map((platform) => (
                  <div key={platform.id} className="space-y-2">
                    <div className="flex items-center space-x-2">
                      <Checkbox 
                        id={platform.id} 
                        checked={platform.selected}
                        onCheckedChange={(checked) => handleSocialMediaChange(platform.id, checked === true)}
                      />
                      <Label htmlFor={platform.id} className="font-medium cursor-pointer">
                        {platform.name}
                      </Label>
                    </div>
                    
                    {platform.selected && platform.id !== "na" && (
                      <div className="pl-6">
                        <Label htmlFor={`${platform.id}-username`} className="text-sm mb-1 block">
                          {platform.name} username
                        </Label>
                        <Input 
                          id={`${platform.id}-username`}
                          placeholder={`Enter your ${platform.name} username`}
                          value={platform.username}
                          onChange={(e) => handleSocialUsernameChange(platform.id, e.target.value)}
                          className="max-w-sm"
                        />
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}
        
        {isAdditionalStep && (
          <div className="w-full max-w-xl mx-auto bg-white dark:bg-zinc-900 rounded-xl shadow-md p-6 border border-zinc-200 dark:border-zinc-800">
            <h3 className="text-lg font-semibold mb-2">Is there anything else you&apos;d like to share with us?</h3>
            <p className="text-sm text-zinc-600 dark:text-zinc-400 mb-4">
              Tell us about what you&apos;d like to sell, any special requests, or questions you might have.
            </p>
            
            <Textarea
              placeholder="Share more about your business, products, or any questions you have..."
              value={additionalInfo}
              onChange={(e) => setAdditionalInfo(e.target.value)}
              className="min-h-[150px]"
            />
          </div>
        )}
      </div>

      <div className="flex justify-between items-center px-8 py-6 border-t dark:border-zinc-800 bg-zinc-50 dark:bg-zinc-900/50">
        <Button variant="outline" size="lg" onClick={handleBack} disabled={isSubmitting}>
          Back
        </Button>
        <Button
          size="lg"
          disabled={
            (isInitialStep && hasExperience === null) ||
            (isPlatformStep && !platform) ||
            (isRevenueStep && !monthlyRevenue) ||
            (isGoalStep && !goal) ||
            (isSocialStep && !isSocialMediaValid) ||
            isSubmitting
          }
          onClick={handleContinue}
          className="font-medium"
        >
          {isSubmitting ? (
            <>
              <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Submitting...
            </>
          ) : isAdditionalStep ? "Submit Application" : (isRevenueStep || isGoalStep || isSocialStep) ? "Next" : "Continue"}
        </Button>
      </div>
    </div>
  );
} 