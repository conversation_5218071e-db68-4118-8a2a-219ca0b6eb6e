"use client";

import { Button } from "@workspace/ui/components/button";
import React, { useEffect, useState } from "react";

const VERSION_URL = "/site.webmanifest";

function getVersionFromManifest(manifest: any) {
  return manifest?.version || JSON.stringify(manifest);
}

export function UpdateNotificationPopup() {
  const [show, setShow] = useState(false);
  const [checking, setChecking] = useState(false);
  const [currentVersion, setCurrentVersion] = useState<string | null>(null);

  useEffect(() => {
    let interval: NodeJS.Timeout;
    let isMounted = true;

    async function checkForUpdate() {
      setChecking(true);
      try {
        const res = await fetch(VERSION_URL + `?t=${Date.now()}`);
        const manifest = await res.json();
        const newVersion = getVersionFromManifest(manifest);
        if (currentVersion && newVersion !== currentVersion) {
          setShow(true);
        }
        if (!currentVersion) {
          setCurrentVersion(newVersion);
        }
      } catch (e) {
        // ignore
      } finally {
        setChecking(false);
      }
    }

    checkForUpdate();
    interval = setInterval(checkForUpdate, 60000);
    return () => {
      isMounted = false;
      clearInterval(interval);
    };
  }, [currentVersion]);

  const handleReload = async () => {
    if ("caches" in window) {
      const cacheNames = await caches.keys();
      await Promise.all(cacheNames.map((name) => caches.delete(name)));
    }
    window.location.reload();
  };

  if (!show) return null;

  return (
    <div className="fixed bottom-6 right-6 z-[1000] bg-sky-800/50 text-white px-6 py-4 shadow-lg flex items-center gap-4 animate-fade-in rounded-md">
      <div className="absolute top-0 left-0 right-0 h-1.5 bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 rounded-t-2xl border-b border-sky-900" />
      <span className="text-sm">New update available! Please refresh to get the latest version.</span>
      <Button
        size="sm"
        onClick={handleReload}
        className="bg-white text-sky-800 font-semibold hover:bg-gray-100 transition border border-sky-900"
      >
        Refresh
      </Button>
    </div>
  );
} 