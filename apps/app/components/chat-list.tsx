import { useQuery } from "convex/react";
import { api } from "@workspace/backend/convex/_generated/api";
import { MessageCard } from "./message-card";
import { Id } from "@workspace/backend/convex/_generated/dataModel";
import { formatDistanceToNow } from "date-fns";
import { getAvatarImageUrl } from "@/lib/utils";
import { useEffect, useState, useCallback } from "react";

const ONLINE_THRESHOLD = 12000;

interface ChatListProps {
  onSelectChat: (chat: {
    id: Id<"chats">;
    name: string;
    avatar?: string;
    initials: string;
  }) => void;
  selectedChatId?: Id<"chats">;
}

export const ChatList = ({ onSelectChat, selectedChatId }: ChatListProps) => {
  const chats = useQuery(api.chat.listChats, {});

  useEffect(() => {
    if (selectedChatId && chats) {
      const chat = chats.find((c: any) => c._id === selectedChatId);
      if (chat && chat.otherUser) {
        const otherUser = chat.otherUser;
        const chatInitials = otherUser.name 
          ? otherUser.name.split(" ")
              .map((n: string) => n[0])
              .join("")
              .toUpperCase()
          : "?";
        
        const avatarImage = otherUser.image || otherUser.imageUrl || undefined;
        
        onSelectChat({
          id: chat._id,
          name: otherUser.name || "Unknown",
          avatar: getAvatarImageUrl(avatarImage) || undefined,
          initials: chatInitials,
        });
      }
    }
  }, [selectedChatId, chats, onSelectChat]);

  if (chats === undefined) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="animate-pulse h-6 w-6 rounded-full bg-primary/30" />
      </div>
    );
  }

  if (chats.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center py-12 text-center">
        <p className="text-muted-foreground">No messages yet</p>
        <p className="text-sm text-muted-foreground mt-2">
          Start a conversation with someone
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-2">
      {chats.map((chat: any) => {
        const otherUser = chat.otherUser;
        const lastMessage = chat.lastMessage;
        
        if (!otherUser || !lastMessage) return null;
        
        const chatInitials = otherUser.name 
          ? otherUser.name.split(" ")
              .map((n: string) => n[0])
              .join("")
              .toUpperCase()
          : "?";
          
        const timestamp = formatDistanceToNow(new Date(lastMessage._creationTime));
        
        const avatarImage = otherUser.image || otherUser.imageUrl || undefined;
        
        return (
          <div 
            key={chat._id}
            onClick={() => onSelectChat({
              id: chat._id,
              name: otherUser.name || "Unknown",
              avatar: getAvatarImageUrl(avatarImage) || undefined,
              initials: chatInitials,
            })}
            className={selectedChatId === chat._id ? "opacity-50 pointer-events-none" : ""}
          >
            <MessageCard
              senderName={otherUser.name || "Unknown"}
              message={lastMessage.text}
              timestamp={timestamp}
              avatarUrl={getAvatarImageUrl(avatarImage) || undefined}
              initials={chatInitials}
              isNew={false}
              userId={otherUser._id}
              user={otherUser}
              unreadCount={chat.unreadCount || 0}
            />
          </div>
        );
      })}
    </div>
  );
}; 