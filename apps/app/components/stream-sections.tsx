"use client";

import { StreamCard } from "./stream-card";

export const PremierShopsSection = ({ streams }: any) => {
  return (
    <section className="bg-card p-6 rounded-lg border">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-2xl font-bold text-foreground">Premier Shops</h2>
        <button className="text-muted-foreground hover:text-foreground transition-colors">
          Show All
        </button>
      </div>
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-3 xl:grid-cols-6 gap-4">
        {streams.map((stream: any) => (
          <StreamCard key={stream._id} stream={stream} onClick={stream.onClick} />
        ))}
      </div>
    </section>
  );
};

export const PopularShowsSection = ({ streams }: any) => {
  return (
    <section className="bg-card p-4 rounded-lg border">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-2xl font-bold text-foreground">Popular Shows</h2>
        <button className="text-muted-foreground hover:text-foreground transition-colors">
          Show All
        </button>
      </div>
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4">
        {streams.map((stream: any) => (
          <StreamCard key={stream._id} stream={stream} onClick={stream.onClick} />
        ))}
      </div>
    </section>
  );
};

export const RegularStreamList = ({ streams }: any) => {
  return (
    <section className="p-2">
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-3 xl:grid-cols-6 gap-4">
        {streams.map((stream: any) => (
          <StreamCard key={stream._id} stream={stream} onClick={stream.onClick} />
        ))}
      </div>
    </section>
  );
};
