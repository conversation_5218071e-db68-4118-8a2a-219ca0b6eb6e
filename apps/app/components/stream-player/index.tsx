"use client";

import { LiveKitRoom } from "@livekit/components-react";
import { cn } from "@workspace/ui/lib/utils";
import { useChatSidebar } from "@/hooks/use-chat-sidebar";
import { useViewerToken } from "@/hooks/use-viewer-token";
import { Chat, ChatSkeleton } from "./chat";
import { Video, VideoSkeleton } from "./video";
import { Header, HeaderSkeleton } from "./header";
import { DirectStreamView } from "./direct-stream-view";
import { StandaloneChat } from "./standalone-chat";
import { StreamHostView } from "./stream-host-view";
import { User as UserType } from "@/lib/types";
import { Id } from "@workspace/backend/convex/_generated/dataModel";
import { useAction } from "convex/react";
import { api } from "@workspace/backend/convex/_generated/api";
import { useEffect, useState, useMemo } from "react";

type CustomStream = {
  id: string;
  isChatEnabled: boolean;
  isChatDelayed: boolean;
  isChatFollowersOnly: boolean;
  isLive: boolean;
  thumbnail: string | null;
  title: string;
  streamer?: {
    _id: string;
    username: string;
  } | null;
  moderators: Id<"users">[];
};

interface StreamPlayerProps {
  user: UserType;
  stream: CustomStream & { isHost: boolean };
  isFollowing: boolean;
  isHost?: boolean;
}

export const StreamPlayer = ({
  user,
  stream,
  isFollowing,
}: StreamPlayerProps) => {
  const { token, name } = useViewerToken(stream.id as Id<"streams">);
  const { collapsed } = useChatSidebar();
  const [viewerCount, setViewerCount] = useState<number | null>(null);
  const getParticipantCount = useAction(api.integration.livekit.getRoomParticipantsCount);
  const [useDirectImplementation, setUseDirectImplementation] = useState(true);

  // Improved host detection logic
  const isUserHost = useMemo(() => {
    if (!user?._id || !stream) return false;
    
    // Check multiple possible ways to determine if user is host
    const hostChecks = [
      stream.isHost, // Explicit host flag
      stream.streamer?._id === user._id, // Stream owner check
      (stream as any).userId === user._id, // Alternative host ID check
    ];
    
    const isHost = hostChecks.some(check => check === true);
    console.log("[StreamPlayer] Host detection:", {
      userId: user._id,
      streamerId: stream.streamer?._id,
      streamUserId: (stream as any).userId,
      streamIsHost: stream.isHost,
      finalIsHost: isHost
    });
    
    return isHost;
  }, [user?._id, stream?.streamer?._id, stream?.isHost, (stream as any)?.userId]);

  useEffect(() => {
    if (stream.isLive && token && !useDirectImplementation && !stream.isHost) {
      const roomName = `stream-${stream.id}`;

      getParticipantCount({ roomName })
        .then(setViewerCount)
        .catch(err => console.error("Failed to fetch viewer count:", err));

      const intervalId = setInterval(() => {
        getParticipantCount({ roomName })
          .then(setViewerCount)
          .catch(err => console.error("Failed to refresh viewer count:", err));
      }, 30000);

      return () => clearInterval(intervalId);
    } else {
      setViewerCount(null);
    }
  }, [stream.isLive, stream.id, token, getParticipantCount, useDirectImplementation, stream.isHost]);

  if (!user?._id) {
    return <div className="p-4 text-red-500">Error: Missing host identity.</div>;
  }

  if (!token || !name) {
    console.log("Rendering skeleton", { token, name });
    return <StreamPlayerSkeleton />;
  }

  console.log("[LiveKit Debug] Frontend expects room name:", stream.id);
  console.log("[LiveKit Debug] Stream object:", stream);
  if (token) {
    try {
      const parts = token.split('.');
      if (parts.length > 1 && parts[1]) {
        const decoded = JSON.parse(atob(parts[1]));
        console.log("[LiveKit Debug] Token (decoded):", decoded);
      } else {
        console.log("[LiveKit Debug] Token does not have a payload part to decode.");
      }
    } catch (e) {
      console.log("[LiveKit Debug] Token decode error:", e);
    }
  }

  console.log("Stream from index.tsx", stream);
  console.log("Rendering host controls?", isUserHost);
  console.log("User ID:", user._id);
  console.log("Stream isHost flag:", stream.isHost);
  console.log("Stream streamer ID:", stream.streamer?._id);
  console.log("Stream userId:", (stream as any).userId);
  console.log("stream.moderators in index.tsx", stream.moderators);

  if (useDirectImplementation) {
    return (
      <div className={cn(
        "grid grid-cols-1 lg:gap-y-0 lg:grid-cols-3 xl:grid-cols-3 2xl:grid-cols-6 h-screen w-full",
        collapsed && "lg:grid-cols-2 xl:grid-cols-2 2xl:grid-cols-2"
      )}>
        <div className="space-y-4 col-span-1 lg:col-span-2 xl:col-span-2 2xl:col-span-5 lg:overflow-y-auto hidden-scrollbar pb-10 h-full w-full">
          <Header
            user={user}
            stream={stream}
            isLive={stream.isLive}
            isFollowing={isFollowing}
            name={stream.title}
            collapsed={collapsed}
          />

          {isUserHost ? (
            <StreamHostView
              streamId={stream.id as Id<"streams">}
              hostIdentity={user._id as Id<"users">}
              hostName={user.username}
            />
          ) : (
            <DirectStreamView
              streamId={stream.id as Id<"streams">}
              hostIdentity={stream.streamer?._id || user._id}
              hostName={stream.streamer?.username || user.username}
              viewerName={name}
              isFollowing={isFollowing}
              isChatEnabled={stream.isChatEnabled}
              isChatDelayed={stream.isChatDelayed}
              isChatFollowersOnly={stream.isChatFollowersOnly}
              showChat={false}
            />
          )}
        </div>
        <div className={cn("col-span-1 h-full", collapsed && "hidden")}>
          <LiveKitRoom
            token={token}
            serverUrl={process.env.NEXT_PUBLIC_LIVEKIT_WS_URL!}
            audio={false}
            video={false}
            connect={!isUserHost}
            className="h-full"
          >
            <StandaloneChat
              streamId={stream.id as Id<"streams">}
              viewerName={name}
              hostName={stream.streamer?.username || user.username}
              isFollowing={isFollowing}
              isChatEnabled={stream.isChatEnabled}
              isChatDelayed={stream.isChatDelayed}
              isChatFollowersOnly={stream.isChatFollowersOnly}
              isStreamLive={stream.isLive}
              moderators={stream.moderators}
              hostIdentity={(stream as any).userId || stream.streamer?._id || user._id}
            />
          </LiveKitRoom>
        </div>
      </div>
    );
  }

  return (
    <>
      <LiveKitRoom
        token={token}
        serverUrl={process.env.NEXT_PUBLIC_LIVEKIT_WS_URL!}
        className={cn(
          "grid grid-cols-1 lg:gap-y-0 lg:grid-cols-3 xl:grid-cols-3 2xl:grid-cols-6 h-screen w-full",
          collapsed && "lg:grid-cols-2 xl:grid-cols-2 2xl:grid-cols-2"
        )}
        audio={true}
        video={true}
        connect={true}

      >
        <div className="space-y-4 col-span-1 lg:col-span-2 xl:col-span-2 2xl:col-span-5 lg:overflow-y-auto hidden-scrollbar pb-10 h-full w-full">
          <Header
            user={user}
            stream={stream}
            isLive={stream.isLive}
            isFollowing={isFollowing}
            name={stream.title}
            collapsed={collapsed}
          />

          {/* Toggle button for testing */}
          <div className="flex items-center gap-2 p-2 bg-gray-100 rounded">
            <button
              onClick={() => setUseDirectImplementation(!useDirectImplementation)}
              className="px-3 py-1 bg-blue-500 text-white rounded text-sm"
            >
              Switch to {useDirectImplementation ? 'LiveKit Components' : 'Direct Implementation'}
            </button>
            <span className="text-sm text-gray-600">
              Current: {useDirectImplementation ? 'Direct Implementation' : 'LiveKit Components'}
            </span>
          </div>

          <Video hostName={user.username} hostIdentity={user._id} viewerCount={viewerCount} streamId={stream.id} />
        </div>
        <div className={cn("col-span-1 h-full", collapsed && "hidden")}>
          <Chat
            streamId={stream.id as Id<"streams">}
            viewerName={name}
            hostName={user.username}
            hostIdentity={user._id}
            isFollowing={isFollowing}
            isChatEnabled={stream.isChatEnabled}
            isChatDelayed={stream.isChatDelayed}
            isChatFollowersOnly={stream.isChatFollowersOnly}
            moderators={stream.moderators}
          />
        </div>
      </LiveKitRoom>
    </>
  );
};

export const StreamPlayerSkeleton = () => {
  return (
    <div className="grid grid-cols-1 lg:gap-y-0 lg:grid-cols-3 xl:grid-cols-3 2xl:grid-cols-6 h-full">
      <div className="space-y-4 col-span-1 lg:col-span-2 xl:col-span-2 2xl:col-span-5 lg:overflow-y-auto hidden-scrollbar pb-10">
        <VideoSkeleton />
        <HeaderSkeleton />
      </div>
      <div className="col-span-1 bg-background">
        <ChatSkeleton />
      </div>
    </div>
  );
};
