"use client";

import { useEffect, useMemo, useState } from "react";
import { ConnectionState } from "livekit-client";
import { useMediaQuery } from "@/hooks/use-media-query";

import {
  useConnectionState,
  useRemoteParticipant,
} from "@livekit/components-react";

import { ChatVariant, useChatSidebar } from "@/hooks/use-chat-sidebar";

import { ChatForm, ChatFormSkeleton } from "./chat-form";
import { ChatList, ChatListSkeleton } from "./chat-list";
import { ChatHeader, ChatHeaderSkeleton } from "./chat-header";
import { ChatCommunity } from "./chat-community";
import { useQuery } from "convex/react";
import { api } from "@workspace/backend/convex/_generated/api";
import { Id } from "@workspace/backend/convex/_generated/dataModel";
import { ReceivedChatMessage } from "@livekit/components-react";

interface ChatProps {
  streamId: Id<"streams">;
  hostName: string;
  hostIdentity: string;
  viewerName: string;
  isFollowing: boolean;
  isChatEnabled: boolean;
  isChatDelayed: boolean;
  isChatFollowersOnly: boolean;
  isHostOrModerator?: boolean;
  moderators: Id<"users">[];
}

export const Chat = ({
  streamId,
  hostName,
  hostIdentity,
  viewerName,
  isFollowing,
  isChatEnabled,
  isChatDelayed,
  isChatFollowersOnly,
  isHostOrModerator = false,
  moderators,
}: ChatProps) => {
  console.log('moderators in Chat', moderators);
  const matches = useMediaQuery("(max-width: 10240px)");
  const { variant, onExpand } = useChatSidebar();
  const connectionState = useConnectionState();
  const participant = useRemoteParticipant(hostIdentity);

  const isOnline = participant && connectionState === ConnectionState.Connected;
  // const isHidden = !isChatEnabled || !isOnline;

  const [value, setValue] = useState("");
  const paginated = useQuery(api.streams.getStreamMessages, { streamId });
  const messages = paginated?.page ?? [];

  useEffect(() => {
    if (matches) {
      onExpand();
    }
  }, [matches, onExpand]);

  const reversedMessages = useMemo(() => {
    if (!Array.isArray(messages) || messages.length === 0) {
      return [];
    }
    return messages
      .slice()
      .sort((a: ReceivedChatMessage, b: ReceivedChatMessage) => a.timestamp - b.timestamp)
      .slice(-25)
      .reverse();
  }, [messages]);

  const onChange = (value: string) => {
    setValue(value);
  };

  const [tab, setTab] = useState<"chat" | "moderation">("chat");

  return (
    <div className="flex flex-col bg-background border-l border-b pt-0 h-full">
      <ChatHeader />
      {isHostOrModerator && (
        <div className="flex gap-2 px-3 pt-2">
          <button onClick={() => setTab("chat")} className={tab === "chat" ? "font-bold" : ""}>Chat</button>
          <button onClick={() => setTab("moderation")} className={tab === "moderation" ? "font-bold" : ""}>Moderation</button>
        </div>
      )}
      {tab === "chat" && variant === ChatVariant.CHAT && (
        <>
          <ChatList messages={reversedMessages} moderators={moderators} hostIdentity={hostIdentity as Id<"users">} />
          <ChatForm
            streamId={streamId}
            value={value}
            onChange={onChange}
            isFollowersOnly={isChatFollowersOnly}
            isDelayed={isChatDelayed}
            isFollowing={isFollowing}
          />
        </>
      )}
      {tab === "moderation" && (
        <div className="p-4 text-sm text-muted-foreground">Moderation tools coming soon...</div>
      )}
      {variant === ChatVariant.COMMUNITY && (
        <ChatCommunity
          viewerName={viewerName}
          hostName={hostName}
        />
      )}
    </div>
  );
};

export const ChatSkeleton = () => {
  return (
    <div className="flex flex-col border-l border-b pt-0 h-[calc(100vh-80px)] border-2">
      <ChatHeaderSkeleton />
      <ChatListSkeleton />
      <ChatFormSkeleton />
    </div>
  );
};
