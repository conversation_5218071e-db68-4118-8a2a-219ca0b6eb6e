"use client";

import { ReceivedChatMessage } from "@livekit/components-react";
import { Id } from "@workspace/backend/convex/_generated/dataModel";

import { Skeleton } from "@workspace/ui/components/skeleton";

import { ChatMessage } from "./chat-message";

interface ChatListProps {
  messages: ReceivedChatMessage[];
  moderators: Id<"users">[];
  hostIdentity?: Id<"users">;
}

export const ChatList = ({ messages, moderators, hostIdentity }: ChatListProps) => {
  console.log('moderators in ChatList', moderators);
  if (!messages || messages.length === 0) {
    return (
      <div className="flex flex-1 items-center justify-center">
        <p className="text-sm text-muted-foreground">
          Welcome to the chat!
        </p>
      </div>
    );
  }

  return (
    <div className="flex flex-1 flex-col-reverse overflow-y-auto p-3 h-full">
      {messages.map((message) => (
        <ChatMessage key={message.timestamp} data={message} moderators={moderators} hostIdentity={hostIdentity} />
      ))}
    </div>
  );
};

export const ChatListSkeleton = () => {
  return (
    <div className="flex h-full items-center justify-center">
      <Skeleton className="w-1/2 h-6" />
    </div>
  );
};
