"use client";

import { useRef, useState, useEffect } from "react";
import { Participant, Track } from "livekit-client";
import { useTracks, useRoomContext, useConnectionState, useParticipants } from "@livekit/components-react";
import { useEventListener } from "@/hooks/use-event-listener";
import { useMutation } from "convex/react";
import { api } from "@workspace/backend/convex/_generated/api";
import { RoomEvent } from "livekit-client";
import { Id } from "@workspace/backend/convex/_generated/dataModel";

import { VolumeControl } from "./volume-controle";
import { FullscreenControl } from "./fullscreen-control";

interface LiveVideoProps {
  participant: Participant;
  isMuted?: boolean;
  volume?: number;
  streamId?: Id<"streams">;
}

export const LiveVideo = ({ participant, isMuted = false, volume = 0.5, streamId }: LiveVideoProps) => {
  const videoRef = useRef<HTMLVideoElement>(null);
  const wrapperRef = useRef<HTMLDivElement>(null);

  const [isFullscreen, setIsFullscreen] = useState(false);
  const [localVolume, setLocalVolume] = useState(volume * 100);

  const onVolumeChange = (value: number) => {
    setLocalVolume(+value);
    if (videoRef?.current) {
      videoRef.current.muted = value === 0;
      videoRef.current.volume = +value * 0.01;
    }
  };

  const toggleMute = () => {
    const isMuted = localVolume === 0;

    setLocalVolume(isMuted ? 50 : 0);

    if (videoRef?.current) {
      videoRef.current.muted = !isMuted;
      videoRef.current.volume = isMuted ? 0.5 : 0;
    }
  };

  useEffect(() => {
    if (videoRef?.current) {
      videoRef.current.muted = isMuted;
      videoRef.current.volume = volume;
    }
  }, [isMuted, volume]);

  useEffect(() => {
    onVolumeChange(localVolume);
  }, [localVolume]);

  const toggleFullscreen = () => {
    if (isFullscreen) {
      document.exitFullscreen();
    } else if (wrapperRef?.current) {
      wrapperRef.current.requestFullscreen();
    }
  };

  const handleFullscreenChange = () => {
    const isCurrentlyFullscreen = document.fullscreenElement !== null;
    setIsFullscreen(isCurrentlyFullscreen);
  };

  useEventListener("fullscreenchange", handleFullscreenChange, wrapperRef);

  useTracks([Track.Source.Camera, Track.Source.Microphone])
    .filter((track) => track.participant.identity === participant.identity)
    .forEach((track) => {
      if (videoRef.current) {
        console.log("Attaching track", track, "to video element", videoRef.current);
        track.publication.track?.attach(videoRef.current);
      }
    });

  const room = useRoomContext();
  const increment = useMutation(api.streams.incrementViewerCount);
  const decrement = useMutation(api.streams.decrementViewerCount);

  const connectionState = useConnectionState();
  const participants = useParticipants();
  const tracks = useTracks([Track.Source.Camera, Track.Source.Microphone]);

  useEffect(() => {
    if (!room || !streamId) return;

    const isHost = participant.identity === streamId.toString() || 
                   participant.metadata?.includes('host') ||
                   participant.permissions?.canPublish;

    if (isHost) {
      console.log("Skipping viewer tracking for host participant");
      return;
    }

    const handleParticipantConnected = () => {
      console.log("Viewer connected, incrementing count");
      increment({ streamId });
    };
    
    const handleParticipantDisconnected = () => {
      console.log("Viewer disconnected, decrementing count");
      decrement({ streamId });
    };

    room.on(RoomEvent.ParticipantConnected, handleParticipantConnected);
    room.on(RoomEvent.ParticipantDisconnected, handleParticipantDisconnected);

    increment({ streamId });
    
    return () => {
      decrement({ streamId });
      room.off(RoomEvent.ParticipantConnected, handleParticipantConnected);
      room.off(RoomEvent.ParticipantDisconnected, handleParticipantDisconnected);
    };
  }, [room, streamId, increment, decrement]);

  return (
    <div ref={wrapperRef} className="relative h-full flex">
      <video ref={videoRef} width="100%" />
      <div className="absolute top-0 h-full w-full opacity-0 hover:opacity-100 hover:transition-all">
        <div className="absolute bottom-0 flex h-14 w-full items-center justify-between bg-gradient-to-r from-neutral-900 px-4">
          <VolumeControl
            onChange={onVolumeChange}
            value={volume}
            onToggle={toggleMute}
          />
          <FullscreenControl
            isFullscreen={isFullscreen}
            onToggle={toggleFullscreen}
          />
        </div>
      </div>
      <div className="aspect-video border-b group relative">
        <div className="absolute bottom-0 left-0 bg-black bg-opacity-70 text-xs text-green-400 p-2 z-50">
          <div>Connection: {connectionState}</div>
          <div>Participants: {participants.length}</div>
          <div>Video Track: {tracks.find(track => track.publication.kind === 'video') ? "Yes" : "No"}</div>
          <div>Participant Identity: {participant?.identity}</div>
        </div>
      </div>
    </div>
  );
};
