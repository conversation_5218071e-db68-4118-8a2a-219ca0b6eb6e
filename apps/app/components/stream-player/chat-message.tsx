"use client";

import { stringToColor } from "@/lib/utils";
import { ReceivedChatMessage } from "@livekit/components-react";
import { format } from "date-fns";
import { Id } from "@workspace/backend/convex/_generated/dataModel";
import { Badge, CustomBadge } from "@workspace/ui/components/badge";
import { cn } from "@workspace/ui/lib/utils";
import { UserAvatar } from "@/components/user-avatar";

interface ChatMessageProps {
  data: ReceivedChatMessage;
  moderators: Id<"users">[];
  hostIdentity?: Id<"users">;
}

export const ChatMessage = ({ data, moderators, hostIdentity }: ChatMessageProps) => {
  console.log('moderators in ChatMessage', moderators);
  const color = stringToColor(data.from?.username || "");
  const isMod = data.from?._id && moderators.includes(data.from._id);
  const isHost = data.from?._id && data.from._id === hostIdentity;

  console.log("data.from", data.from);
  console.log("hostIdentity", hostIdentity);
  console.log("isHost", isHost);

  return (
    <div className={cn(
      "flex items-center gap-2 p-2 rounded-md hover:bg-white/5 my-0.5",
      isHost && "border border-dashed border-input",
      isMod && "bg-blue-400/3"
    )}>
      {/* <p className="text-sm text-white/40">{format(data.timestamp, "HH:mm")}</p> */}
      <div className="flex flex-wrap items-center gap-1 grow">
        <div className="flex items-center gap-2">
          <UserAvatar 
            user={data.from}
            size="sm"
            showOnlineIndicator={false}
          />
          <div className="text-sm font-semibold whitespace-nowrap flex items-center">
            <span className="truncate" style={{ color: color }}>
              {data.from?.username}
            </span>
            {isHost && (
              <CustomBadge variant="host" circle="host" className="ml-1 text-[8px]">Host</CustomBadge>
            )}
            {isMod && !isHost && (
              <CustomBadge className="ml-1 text-[8px]">Mod</CustomBadge>
            )}
            :
          </div>
        </div>
        <p className="text-sm break-all">{data.message}</p>
      </div>
    </div>
  );
};
