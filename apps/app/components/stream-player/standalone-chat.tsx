"use client";

import { useEffect, useMemo, useState } from "react";
import { useMediaQuery } from "@/hooks/use-media-query";
import { ChatVariant, useChatSidebar } from "@/hooks/use-chat-sidebar";
import { Cha<PERSON><PERSON><PERSON>, ChatFormSkeleton } from "./chat-form";
import { <PERSON><PERSON><PERSON><PERSON>, ChatListSkeleton } from "./chat-list";
import { Chat<PERSON>eader, ChatHeaderSkeleton } from "./chat-header";
import { ChatCommunity } from "./chat-community";
import { useQuery } from "convex/react";
import { api } from "@workspace/backend/convex/_generated/api";
import { Id } from "@workspace/backend/convex/_generated/dataModel";
import { ReceivedChatMessage } from "@livekit/components-react";

interface StandaloneChatProps {
  streamId: Id<"streams">;
  hostName: string;
  viewerName: string;
  isFollowing: boolean;
  isChatEnabled: boolean;
  isChatDelayed: boolean;
  isChatFollowersOnly: boolean;
  isHostOrModerator?: boolean;
  isStreamLive?: boolean;
  moderators?: Id<"users">[];
  hostIdentity?: Id<"users">;
}

export const StandaloneChat = ({
  streamId,
  hostName,
  viewerName,
  isFollowing,
  isChatEnabled,
  isChatDelayed,
  isChatFollowersOnly,
  isHostOrModerator = false,
  isStreamLive = false,
  moderators = [],
  hostIdentity,
}: StandaloneChatProps) => {
  const matches = useMediaQuery("(max-width: 10240px)");
  const { variant, onExpand } = useChatSidebar();

  const isOnline = isStreamLive;
  const isHidden = !isChatEnabled || !isOnline;

  const [value, setValue] = useState("");
  const paginated = useQuery(api.streams.getStreamMessages, { streamId });
  const messages = paginated?.page ?? [];

  useEffect(() => {
    if (matches) {
      onExpand();
    }
  }, [matches, onExpand]);

  const reversedMessages = useMemo(() => {
    if (!Array.isArray(messages) || messages.length === 0) {
      return [];
    }
    return messages
      .sort((a: ReceivedChatMessage, b: ReceivedChatMessage) => a.timestamp - b.timestamp)
      .slice(-25)
      .reverse();
  }, [messages]);

  const onChange = (value: string) => {
    setValue(value);
  };

  const [tab, setTab] = useState<"chat" | "moderation">("chat");

  return (
    <div className="flex flex-col bg-background border-l border-b pt-0 h-full">
      <ChatHeader />
      {isHostOrModerator && (
        <div className="flex gap-2 px-3 pt-2">
          <button onClick={() => setTab("chat")} className={tab === "chat" ? "font-bold" : ""}>
            Chat
          </button>
          <button onClick={() => setTab("moderation")} className={tab === "moderation" ? "font-bold" : ""}>
            Moderation
          </button>
        </div>
      )}
      {tab === "chat" && variant === ChatVariant.CHAT && (
        <>
          <ChatList messages={reversedMessages} moderators={moderators} hostIdentity={hostIdentity} />
          <ChatForm
            streamId={streamId}
            value={value}
            onChange={onChange}
            isFollowersOnly={isChatFollowersOnly}
            isDelayed={isChatDelayed}
            isFollowing={isFollowing}
          />
        </>
      )}
      {tab === "moderation" && (
        <div className="p-4 text-sm text-muted-foreground">Moderation tools coming soon...</div>
      )}
      {variant === ChatVariant.COMMUNITY && (
        <ChatCommunity
          viewerName={viewerName}
          hostName={hostName}
        />
      )}
    </div>
  );
};

export const StandaloneChatSkeleton = () => {
  return (
    <div className="flex flex-col border-l border-b pt-0 h-[calc(100vh-80px)] border-2">
      <ChatHeaderSkeleton />
      <ChatListSkeleton />
      <ChatFormSkeleton />
    </div>
  );
};
