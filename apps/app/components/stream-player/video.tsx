"use client";

import { ConnectionState, Track } from "livekit-client";
import {
  useConnectionState,
  useRemoteParticipant,
  useTracks,
  useParticipants,
  useRoomContext,
} from "@livekit/components-react";
import { useEffect, useState, useRef } from "react";
import { Volume2, VolumeX, Maximize, Minimize, Users } from "lucide-react";

import { Skeleton } from "@workspace/ui/components/skeleton";

import { OfflineVideo } from "./offline-video";
import { LoadingVideo } from "./loading-video";
import { LiveVideo } from "./live-video";

interface VideoProps {
  hostName: string;
  hostIdentity: string;
  viewerCount?: number | null;
  streamId?: string;
}

export const Video = ({ hostName, hostIdentity, viewerCount, streamId }: VideoProps) => {
  const connectionState = useConnectionState();
  const tracks = useTracks();
  const participants = useParticipants();
  const room = useRoomContext();

  const videoContainerRef = useRef<HTMLDivElement>(null);
  const [isMuted, setIsMuted] = useState(false);
  const [volume, setVolume] = useState(0.5);
  const [isFullscreen, setIsFullscreen] = useState(false);

  const videoTracks = tracks.filter(track => track.publication.kind === 'video');

  console.log("LiveKit connectionState:", connectionState);
  console.log("All participants:", participants);
  console.log("All tracks:", tracks);
  if (videoTracks.length > 0) {
    videoTracks.forEach((track, idx) => {
      console.log(`Video track #${idx}:`, track);
      console.log("Video participant identity:", track.participant?.identity);
    });
  }

  useEffect(() => {
    if (!room) return;
    console.log("=== LiveKit Room Remote Participants ===");
    Array.from(room.remoteParticipants.values()).forEach((p) => {
      console.log(
        "[LiveKit] Remote Participant:",
        p.identity,
        p.name,
        p.isLocal ? "(local)" : "(remote)",
        p
      );
      Array.from(p.trackPublications.values()).forEach((pub: any) => {
        if (pub && pub.track) {
          console.log(
            "[LiveKit] Remote Track:",
            pub.kind,
            pub.track.name,
            pub.track.kind,
            pub.track
          );
        }
      });
    });
    const local = room.localParticipant;
    if (local) {
      console.log(
        "[LiveKit] LocalParticipant:",
        local.identity,
        local.name
      );
      Array.from(local.trackPublications.values()).forEach((pub: any) => {
        if (pub && pub.track) {
          console.log(
            "[LiveKit] Local Track:",
            pub.kind,
            pub.track.name,
            pub.track.kind,
            pub.track
          );
        }
      });
    }
  }, [room, participants.length]);

  const toggleMute = () => {
    setIsMuted(!isMuted);
  };

  const handleVolumeChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const newVolume = parseFloat(event.target.value);
    setVolume(newVolume);
    if (newVolume > 0 && isMuted) {
      setIsMuted(false);
    } else if (newVolume === 0 && !isMuted) {
      setIsMuted(true);
    }
  };

  const toggleFullscreen = () => {
    if (!videoContainerRef.current) return;
    if (!document.fullscreenElement) {
      videoContainerRef.current.requestFullscreen().catch(err => {
        console.error(`Error attempting to enable full-screen mode: ${err.message} (${err.name})`);
      });
    } else {
      document.exitFullscreen();
    }
  };

  useEffect(() => {
    const handleFullscreenChange = () => {
      setIsFullscreen(!!document.fullscreenElement);
    };
    document.addEventListener('fullscreenchange', handleFullscreenChange);
    return () => document.removeEventListener('fullscreenchange', handleFullscreenChange);
  }, []);

  let content;

  if (videoTracks.length === 0 && connectionState === ConnectionState.Connected) {
    content = <OfflineVideo username={hostName} />;
  } else if (videoTracks.length === 0) {
    content = <LoadingVideo label={connectionState} />;
  } else {
    content = (
      <>
        {videoTracks.map((track, idx) => (
          <LiveVideo
            key={track.publication.trackSid || idx}
            participant={track.participant}
            isMuted={isMuted}
            volume={volume}
            streamId={streamId as any}
          />
        ))}
      </>
    );
  }

  return (
    <div
      ref={videoContainerRef}
      className="aspect-video border-b group relative overflow-hidden bg-black"
    >
      {content}

      {/* Video Controls Overlay */}
      {connectionState === ConnectionState.Connected && videoTracks.length > 0 && (
        <div className="absolute bottom-0 left-0 right-0 p-2 bg-black/30 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <button
              onClick={toggleMute}
              className="text-white p-1.5 hover:bg-white/20 rounded-full"
            >
              {isMuted ? <VolumeX size={20} /> : <Volume2 size={20} />}
            </button>
            <input
              type="range"
              min="0"
              max="1"
              step="0.05"
              value={isMuted ? 0 : volume}
              onChange={handleVolumeChange}
              className="w-20 h-1.5 accent-primary cursor-pointer"
            />
          </div>

          <div className="flex items-center space-x-2">
            {viewerCount !== null && viewerCount !== undefined && (
              <div className="flex items-center text-white text-sm">
                <Users size={16} className="mr-1"/> {viewerCount}
              </div>
            )}
            <button
              onClick={toggleFullscreen}
              className="text-white p-1.5 hover:bg-white/20 rounded-full"
            >
              {isFullscreen ? <Minimize size={20} /> : <Maximize size={20} />}
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export const VideoSkeleton = () => {
  return (
    <div className="aspect-video border-x border-background">
      <Skeleton className="h-full w-full rounded-none" />
    </div>
  );
};
