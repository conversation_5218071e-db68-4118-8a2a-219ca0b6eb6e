"use client";

import { cn } from "@workspace/ui/lib/utils";
import { But<PERSON> } from "@workspace/ui/components/button";
import { Input } from "@workspace/ui/components/input";
import { useState, useEffect } from "react";
import { Badge } from "@workspace/ui/components/badge";
import { ArrowLeft, Eye, EyeOff } from "lucide-react";
import { useAuthActions } from "@convex-dev/auth/react";
import { useMutation, useQuery } from "convex/react";
import { api } from "@workspace/backend/convex/_generated/api";
import { toast } from "sonner";
import Image from "next/image";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useDebouncedCallback } from "@/hooks/use-debounce-callback";
import { ConvexError } from "convex/values";
import { z } from "zod";
import { STORAGE_PREFIX } from "@/lib/constants";
import {
  IconBrandGoogleFilled,
} from "@tabler/icons-react";
import logo from "@workspace/assets/images/icon.png";
import { validateUsername } from "@workspace/lib/profanity";

const emailSchema = z.string().email("Invalid email address");
const passwordSchema = z
  .string()
  .min(8, "Password must be at least 8 characters long");

const signInSchema = z.object({
  email: emailSchema,
  password: passwordSchema,
  flow: z.literal("signIn"),
  loginType: z.literal("password"),
});

const signUpSchema = z
  .object({
    email: emailSchema,
    password: passwordSchema,
    confirmPassword: passwordSchema,
    firstName: z.string().min(1, "First name is required"),
    lastName: z.string().min(1, "Last name is required"),
    flow: z.literal("signUp"),
    loginType: z.literal("password"),
    username: z.string().min(1, "Username is required"),
    role: z.literal("user"),
    phone: z.string().min(1, "Phone number is required"),
  })
  .refine((data) => data.password === data.confirmPassword, {
    message: "Passwords do not match",
    path: ["confirmPassword"],
  });

export function LoginForm({
  className,
  ...props
}: React.ComponentProps<"div">) {
  const router = useRouter();
  const { signIn } = useAuthActions();
  const [email, setEmail] = useState("");
  const [debouncedEmail, setDebouncedEmail] = useState(email);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [emailSubmitted, setEmailSubmitted] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [username, setUsername] = useState("");
  const [debouncedUsername, setDebouncedUsername] = useState(username);
  const [flow, setFlow] = useState<"signIn" | "signUp">("signIn");
  const update = useMutation(api.users.updateUserLoginType);
  const [errors, setErrors] = useState<{ [key: string]: string }>({});
  const [forceSignup, setForceSignup] = useState(false);
  const [checkingUsername, setCheckingUsername] = useState(false);
  const [resetStep, setResetStep] = useState<"forgot" | { email: string } | null>(null);
  const [resetEmail, setResetEmail] = useState("");
  const [resetCode, setResetCode] = useState("");
  const [resetNewPassword, setResetNewPassword] = useState("");
  const [showResetPassword, setShowResetPassword] = useState(false);
  const [resetErrors, setResetErrors] = useState<{ [key: string]: string }>({});
  const [firstName, setFirstName] = useState("");
  const [lastName, setLastName] = useState("");
  const [phone, setPhone] = useState("");

  const lastLoginType =
    useQuery(api.users.getUserLoginType, { email: debouncedEmail }) || null;
  const isNewUser = emailSubmitted && !lastLoginType;
  
  const isUsernameAvailable = useQuery(
    api.users.checkUsernameAvailable,
    debouncedUsername ? { username: debouncedUsername } : { username: "" }
  );

  const debouncedSetEmail = useDebouncedCallback((value: string) => {
    setDebouncedEmail(value);
  }, 300);

  const debouncedSetUsername = useDebouncedCallback((value: string) => {
    setDebouncedUsername(value);
    setCheckingUsername(true);
    
    const validation = validateUsername(value);
    if (!validation.isValid) {
      setErrors(prev => ({
        ...prev,
        username: validation.error || "Invalid username"
      }));
    } else if (errors.username && errors.username !== "Username is already taken") {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors.username;
        return newErrors;
      });
    }
  }, 300);

  useEffect(() => {
    const savedEmail = localStorage.getItem(`${STORAGE_PREFIX}user_email`);
    if (savedEmail) {
      setEmail(savedEmail);
      setDebouncedEmail(savedEmail);
    }
  }, []);

  useEffect(() => {
    if (isNewUser) {
      setFlow("signUp");
    } else if (emailSubmitted) {
      setFlow("signIn");
    }
  }, [isNewUser, emailSubmitted]);

  useEffect(() => {
    if (typeof window !== "undefined") {
      const params = new URLSearchParams(window.location.search);
      setForceSignup(params.get("signup") === "true");
    }
  }, []);

  useEffect(() => {
    if (checkingUsername && debouncedUsername && isUsernameAvailable === false) {
      setErrors(prev => ({
        ...prev,
        username: "Username is already taken"
      }));
    } else if (checkingUsername && debouncedUsername && isUsernameAvailable === true) {
      if (errors.username === "Username is already taken") {
        setErrors(prev => {
          const newErrors = { ...prev };
          delete newErrors.username;
          return newErrors;
        });
      }
      setCheckingUsername(false);
    }
  }, [isUsernameAvailable, debouncedUsername, checkingUsername, errors.username]);

  const validateForm = () => {
    setErrors({});
    try {
      if (flow === "signUp") {
        signUpSchema.parse({
          email: email.toLowerCase().trim(),
          password,
          confirmPassword,
          firstName,
          lastName,
          phone,
          flow,
          loginType: "password",
          username,
          role: "user",
        });
        
        const usernameValidation = validateUsername(username);
        if (!usernameValidation.isValid) {
          setErrors({ username: usernameValidation.error || "Invalid username" });
          return false;
        }
        
        if (isUsernameAvailable === false) {
          setErrors({ username: "Username is already taken" });
          return false;
        }
      } else {
        signInSchema.parse({
          email: email.toLowerCase().trim(),
          password,
          flow,
          loginType: "password",
        });
      }
      return true;
    } catch (error) {
      if (error instanceof z.ZodError) {
        const newErrors: { [key: string]: string } = {};
        error.errors.forEach((err) => {
          const path = err.path.join(".");
          newErrors[path] = err.message;
        });
        setErrors(newErrors);
      }
      return false;
    }
  };

  const handleEmailSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    try {
      emailSchema.parse(email);
      localStorage.setItem(`${STORAGE_PREFIX}user_email`, email);
      setIsLoading(true);
      setErrors({});
      setTimeout(() => {
        setEmailSubmitted(true);
        setIsLoading(false);
      }, 400);
    } catch (error) {
      if (error instanceof z.ZodError) {
        setErrors({
          email: error.errors[0]?.message || "Invalid email address",
        });
      }
    }
  };

  const handleUseAnotherEmail = () => {
    setEmailSubmitted(false);
    setEmail("");
    setDebouncedEmail("");
    setPassword("");
    setConfirmPassword("");
    setUsername("");
    localStorage.removeItem(`${STORAGE_PREFIX}user_email`);
  };

  const handlePasswordSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsLoading(true);

    try {
      const credentials = {
        email: email.toLowerCase().trim(),
        password,
        flow,
        loginType: "password" as const,
        ...(flow === "signUp"
          ? { username, firstName, lastName, phone }
          : {}),
      };

      await signIn("password", credentials);
      if (!isNewUser) {
        await update({
          email: email.toLowerCase().trim(),
          loginType: "password",
        });
      }
      toast.success(
        flow === "signIn"
          ? "Signed in successfully!"
          : "Account created successfully!",
      );

      setTimeout(() => {
        if (flow === "signUp") {
          router.push("/onboarding");
        } else {
          router.push("/");
        }
      }, 500);
    } catch (error) {
      if (
        error instanceof Error &&
        error.message.includes(
          "Cannot read properties of null (reading 'redirect')",
        )
      ) {
        setErrors({ password: "Invalid password" });
      } else if (error instanceof ConvexError) {
        toast.error(
          flow === "signIn"
            ? "Could not sign in, did you mean to sign up?"
            : "Could not sign up, did you mean to sign in?",
        );
      } else if (error instanceof Error && error.message.includes("Invalid username")) {
        setErrors({ username: error.message });
      } else {
        toast.error("An error occurred. Please try again.");
      }
    } finally {
      setIsLoading(false);
    }
  };

  const handleGoogleSignIn = async () => {
    setIsLoading(true);
    try {
      await signIn("google");
      setTimeout(() => {
        router.push("/");
      }, 500);
    } catch (error) {
      toast.error("An error occurred. Please try again.", {
        description:
          error instanceof Error ? error.message : "An unknown error occurred",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleResetRequest = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setResetErrors({});
    try {
      emailSchema.parse(resetEmail);
      setIsLoading(true);
      await signIn("password", { email: resetEmail, flow: "reset" });
      setResetStep({ email: resetEmail });
      toast.success("If an account exists, you'll get an email with a reset code.");
    } catch (error) {
      if (error instanceof z.ZodError) {
        setResetErrors({ email: error.errors[0]?.message || "Invalid email address" });
      } else {
        setResetErrors({ email: "If an account exists, you'll get an email with a reset code." });
      }
    } finally {
      setIsLoading(false);
    }
  };

  const handleResetVerification = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setResetErrors({});
    try {
      if (!resetCode) {
        setResetErrors({ code: "Code is required" });
        return;
      }
      passwordSchema.parse(resetNewPassword);
      setIsLoading(true);
      if (!resetStep || typeof resetStep !== "object") {
        setResetErrors({ code: "Unexpected error. Please try again." });
        setIsLoading(false);
        return;
      }
      await signIn("password", {
        email: resetStep.email,
        code: resetCode,
        newPassword: resetNewPassword,
        flow: "reset-verification",
      });
      toast.success("Password reset successfully! You can now sign in.");
      setResetStep(null);
      setResetEmail("");
      setResetCode("");
      setResetNewPassword("");
      setEmailSubmitted(false);
      setFlow("signIn");
    } catch (error) {
      if (error instanceof z.ZodError) {
        setResetErrors({ newPassword: error.errors[0]?.message || "Invalid password" });
      } else {
        setResetErrors({ code: "Invalid code or password. Please try again." });
      }
    } finally {
      setIsLoading(false);
    }
  };

  const handlePhoneFormat = (phone: string) => {
    return phone.replace(/(\d{3})(\d{3})(\d{4})/, "($1) $2-$3");
  }

  return (
    <div className={cn("flex flex-col gap-6", className)} {...props}>
      {resetStep === "forgot" && (
        <form onSubmit={handleResetRequest} className="flex flex-col gap-4">
          <h2 className="text-lg font-semibold text-center">Reset your password</h2>
          <Input
            id="resetEmail"
            name="resetEmail"
            type="email"
            value={resetEmail}
            onChange={e => setResetEmail(e.target.value)}
            placeholder="Your email"
            required
            autoComplete="email"
            aria-invalid={!!resetErrors.email}
            aria-errormessage={resetErrors.email}
          />
          {resetErrors.email && <p className="text-sm text-destructive">{resetErrors.email}</p>}
          <Button type="submit" className="w-full" disabled={isLoading}>
            {isLoading ? "Sending..." : "Send code"}
          </Button>
          <Button type="button" variant="link" onClick={() => setResetStep(null)} className="text-xs">
            Back to sign in
          </Button>
        </form>
      )}
      {resetStep && typeof resetStep === "object" && (
        <form onSubmit={handleResetVerification} className="flex flex-col gap-4">
          <h2 className="text-lg font-semibold text-center">Enter code & new password</h2>
          <Input
            id="resetCode"
            name="resetCode"
            type="text"
            value={resetCode}
            onChange={e => setResetCode(e.target.value)}
            placeholder="Code from email"
            required
            aria-invalid={!!resetErrors.code}
            aria-errormessage={resetErrors.code}
          />
          {resetErrors.code && <p className="text-sm text-destructive">{resetErrors.code}</p>}
          <div className="relative">
            <Input
              id="resetNewPassword"
              name="resetNewPassword"
              type={showResetPassword ? "text" : "password"}
              value={resetNewPassword}
              onChange={e => setResetNewPassword(e.target.value)}
              placeholder="New password"
              required
              aria-invalid={!!resetErrors.newPassword}
              aria-errormessage={resetErrors.newPassword}
              autoComplete="new-password"
              className="pr-10"
            />
            <button
              type="button"
              onClick={() => setShowResetPassword(!showResetPassword)}
              className="absolute right-3 top-1/2 -translate-y-1/2"
            >
              {showResetPassword ? (
                <EyeOff className="h-4 w-4 text-gray-500" />
              ) : (
                <Eye className="h-4 w-4 text-gray-500" />
              )}
            </button>
          </div>
          {resetErrors.newPassword && <p className="text-sm text-destructive">{resetErrors.newPassword}</p>}
          <Button type="submit" className="w-full" disabled={isLoading}>
            {isLoading ? "Resetting..." : "Reset password"}
          </Button>
          <Button type="button" variant="link" onClick={() => setResetStep(null)} className="text-xs">
            Back to sign in
          </Button>
        </form>
      )}
      {resetStep === null && (
        <form
          onSubmit={emailSubmitted ? handlePasswordSubmit : handleEmailSubmit}
        >
          <div className="flex flex-col gap-6">
            <div className="flex flex-col items-center gap-2">
              <Link
                href="/"
                aria-label="home"
                className="flex items-center space-x-2"
              >
                <Image
                  className="rounded-md z-1 relative border dark:hidden"
                  src={logo}
                  alt="liveciety logo"
                  width={48}
                  height={48}
                />
                <Image
                  className="rounded-md z-1 relative hidden border dark:block"
                  src={logo}
                  alt="liveciety logo"
                  width={48}
                  height={48}
                />
              </Link>
              <h1 className="text-xl font-bold">
                {forceSignup
                  ? "Signup for Liveciety"
                  : isNewUser
                    ? "Sign up for an account"
                    : "Login to Liveciety"}
              </h1>
            </div>
            <div className="flex flex-col gap-2">
              <div
                className={`${emailSubmitted ? "!cursor-not-allowed" : ""} grid gap-2`}
              >
                <div className="relative">
                  <Input
                    id="email"
                    name="email"
                    type="email"
                    value={email}
                    onChange={(e) => {
                      const value = e.target.value;
                      setEmail(value);
                      debouncedSetEmail(value);
                    }}
                    placeholder="Your email (required)"
                    required
                    disabled={emailSubmitted}
                    autoComplete="email"
                    aria-invalid={!!errors.email}
                    aria-errormessage={errors.email}
                  />
                </div>
                {errors.email && (
                  <p className="text-sm text-destructive">{errors.email}</p>
                )}
              </div>

              {emailSubmitted && (
                <>
                  {isNewUser ? (
                    <div className="space-y-2">
                      <div className="relative">
                        <Input
                          id="username"
                          name="username"
                          type="text"
                          value={username}
                          onChange={(e) => {
                            const value = e.target.value;
                            setUsername(value);
                            debouncedSetUsername(value);
                          }}
                          placeholder="Your username (required)"
                          required
                          autoComplete="username"
                          aria-invalid={!!errors.username}
                          aria-errormessage={errors.username}
                        />
                      </div>
                      {errors.username && (
                        <p className="text-sm text-destructive">
                          {errors.username}
                        </p>
                      )}
                      <div className="flex flex-row items-center gap-2">
                        <Input
                          id="firstName"
                          name="firstName"
                          type="text"
                          value={firstName}
                          onChange={(e) => {
                            const value = e.target.value;
                            setFirstName(value);
                          }}
                          placeholder="First name (required)"
                          required
                          autoComplete="firstName"
                          aria-invalid={!!errors.firstName}
                          aria-errormessage={errors.firstName}
                        />
                        <Input 
                          id="lastName"
                          name="lastName"
                          type="text"
                          value={lastName}
                          onChange={(e) => {
                            const value = e.target.value;
                            setLastName(value);
                          }}
                          placeholder="Last name (required)"
                          required
                          autoComplete="lastName"
                          aria-invalid={!!errors.lastName}
                          aria-errormessage={errors.lastName}
                        />
                      </div>
                      {errors.firstName && (
                        <p className="text-sm text-destructive">
                          {errors.firstName}
                        </p>
                      )}
                      {errors.lastName && (
                        <p className="text-sm text-destructive">
                          {errors.lastName}
                        </p>
                      )}
                      <div className="relative">
                        <Input
                          id="phone"
                          name="phone"
                          type="text"
                          value={phone}
                          onChange={(e) => {
                            const value = e.target.value;
                            setPhone(value);
                          }}
                          placeholder="Phone number (required)"
                          required
                          pattern="^\d{10}$"
                          inputMode="numeric"
                          autoComplete="phone"
                          aria-invalid={!!errors.phone}
                          aria-errormessage={errors.phone}
                        />
                      </div>
                      {errors.phone && (
                        <p className="text-sm text-destructive">
                          {errors.phone}
                        </p>
                      )}
                      <div className="relative">
                        <Input
                          id="password"
                          name="password"
                          type={showPassword ? "text" : "password"}
                          value={password}
                          onChange={(e) => setPassword(e.target.value)}
                          placeholder="Create a password"
                          required
                          className="pr-10"
                          autoComplete="new-password"
                          aria-invalid={!!errors.password}
                          aria-errormessage={errors.password}
                        />
                        <button
                          type="button"
                          onClick={() => setShowPassword(!showPassword)}
                          className="absolute right-3 top-1/2 -translate-y-1/2"
                        >
                          {showPassword ? (
                            <EyeOff className="h-4 w-4 text-gray-500" />
                          ) : (
                            <Eye className="h-4 w-4 text-gray-500" />
                          )}
                        </button>
                      </div>
                      <div className="relative">
                        <Input
                          id="confirmPassword"
                          name="confirmPassword"
                          type={showConfirmPassword ? "text" : "password"}
                          value={confirmPassword}
                          onChange={(e) => setConfirmPassword(e.target.value)}
                          placeholder="Confirm password"
                          required
                          className="pr-10"
                          autoComplete="new-password"
                          aria-invalid={!!errors.confirmPassword}
                          aria-errormessage={errors.confirmPassword}
                        />
                        <button
                          type="button"
                          onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                          className="absolute right-3 top-1/2 -translate-y-1/2"
                        >
                          {showConfirmPassword ? (
                            <EyeOff className="h-4 w-4 text-gray-500" />
                          ) : (
                            <Eye className="h-4 w-4 text-gray-500" />
                          )}
                        </button>
                      </div>
                      <p className="text-xs text-muted-foreground">
                        Password must be at least 8 characters long
                      </p>
                    </div>
                  ) : (
                    <div className="relative">
                      <Input
                        id="password"
                        name="password"
                        type={showPassword ? "text" : "password"}
                        value={password}
                        onChange={(e) => setPassword(e.target.value)}
                        placeholder="Enter your password"
                        required
                        className="pr-10"
                        autoComplete="off"
                        aria-invalid={!!errors.password}
                        aria-errormessage={errors.password}
                      />
                      <button
                        type="button"
                        onClick={() => setShowPassword(!showPassword)}
                        className="absolute right-3 top-1/2 -translate-y-1/2"
                      >
                        {showPassword ? (
                          <EyeOff className="h-4 w-4 text-gray-500" />
                        ) : (
                          <Eye className="h-4 w-4 text-gray-500" />
                        )}
                      </button>
                    </div>
                  )}
                </>
              )}

              {!emailSubmitted ? (
                <Button type="submit" className="w-full" disabled={isLoading}>
                  {isLoading ? (
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                      strokeWidth="1.5"
                      stroke="currentColor"
                      className="h-4 w-4 animate-spin"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        d="M16.023 9.348h4.992v-.001M2.985 19.644v-4.992m0 0h4.992m-4.993 0 3.181 3.183a8.25 8.25 0 0 0 13.803-3.7M4.031 9.865a8.25 8.25 0 0 1 13.803-3.7l3.181 3.182m0-4.991v4.99"
                      />
                    </svg>
                  ) : (
                    "Continue"
                  )}
                </Button>
              ) : (
                <div className="flex flex-col gap-4">
                  <div className="relative">
                    <Button type="submit" className="w-full" disabled={isLoading}>
                      {isLoading ? (
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          fill="none"
                          viewBox="0 0 24 24"
                          strokeWidth="1.5"
                          stroke="currentColor"
                          className="h-4 w-4 animate-spin"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            d="M16.023 9.348h4.992v-.001M2.985 19.644v-4.992m0 0h4.992m-4.993 0 3.181 3.183a8.25 8.25 0 0 0 13.803-3.7M4.031 9.865a8.25 8.25 0 0 1 13.803-3.7l3.181 3.182m0-4.991v4.99"
                          />
                        </svg>
                      ) : isNewUser ? (
                        "Create Account"
                      ) : (
                        "Sign In"
                      )}
                    </Button>
                    {lastLoginType === "password" && !isNewUser && (
                      <Badge className="absolute rounded-sm -right-2 -top-2 bg-card text-muted-foreground border-input">
                        Last used
                      </Badge>
                    )}
                  </div>
                  <div className="flex items-center justify-between gap-4">
                    <Button
                      type="button"
                      variant="link"
                      onClick={handleUseAnotherEmail}
                      className="flex items-center gap-2 h-6 bg-transparent hover:bg-transparent text-muted-foreground hover:text-primary cursor-pointer"
                    >
                      <ArrowLeft className="h-4 w-4" />
                      Use another email
                    </Button>
                    {!isNewUser && (
                      <Button
                        type="button"
                        onClick={() => setResetStep("forgot")}
                        className="h-6 bg-transparent hover:bg-transparent text-muted-foreground hover:text-primary cursor-pointer"
                      >
                        Reset password
                      </Button>
                    )}
                  </div>
                </div>
              )}
            </div>

            {emailSubmitted && (
              <>
                <div className="after:border-border relative text-center text-sm after:absolute after:inset-0 after:top-1/2 after:z-0 after:flex after:items-center after:border-t">
                  <span className="bg-background text-muted-foreground relative z-10 px-2">
                    or
                  </span>
                </div>

                <div className="relative grid grid-cols-1 gap-2">
                  <Button
                    variant="outline"
                    type="button"
                    className="w-full gap-2"
                    onClick={handleGoogleSignIn}
                    disabled={isLoading}
                  >
                    {isLoading ? (
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 24 24"
                        strokeWidth="1.5"
                        stroke="currentColor"
                        className="h-4 w-4 animate-spin"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          d="M16.023 9.348h4.992v-.001M2.985 19.644v-4.992m0 0h4.992m-4.993 0 3.181 3.183a8.25 8.25 0 0 0 13.803-3.7M4.031 9.865a8.25 8.25 0 0 1 13.803-3.7l3.181 3.182m0-4.991v4.99"
                        />
                      </svg>
                    ) : (
                      <>
                        <IconBrandGoogleFilled className="h-4 w-4" />
                        {isNewUser
                          ? "Register with Google"
                          : "Continue with Google"}
                      </>
                    )}
                  </Button>
                  {/* <Button 
                    variant="outline" 
                    type="button" 
                    className="w-full gap-2" 
                    onClick={handleGoogleSignIn} 
                    disabled={isLoading}
                  >
                    {isLoading ? (
                      <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="h-4 w-4 animate-spin">
                        <path strokeLinecap="round" strokeLinejoin="round" d="M16.023 9.348h4.992v-.001M2.985 19.644v-4.992m0 0h4.992m-4.993 0 3.181 3.183a8.25 8.25 0 0 0 13.803-3.7M4.031 9.865a8.25 8.25 0 0 1 13.803-3.7l3.181 3.182m0-4.991v4.99" />
                      </svg>
                    ) : (
                      <>
                        <IconBrandAppleFilled className="h-4 w-4" />
                        {isNewUser ? "Register with Apple" : "Continue with Apple"}
                      </>
                    )}
                  </Button> */}
                  {lastLoginType === "oauth" && !isNewUser && (
                    <Badge className="absolute rounded-sm -right-2 -top-2">
                      Last used
                    </Badge>
                  )}
                </div>
              </>
            )}
          </div>

          {/* @MSY - Add hidden input for flow */}
          <input type="hidden" name="flow" value={flow} />
        </form>
      )}
      <div className="text-muted-foreground *:[a]:hover:text-primary text-center text-xs text-balance *:[a]:underline *:[a]:underline-offset-4">
        By clicking continue, you agree to our{" "}
        <a href="/legal/terms-of-service">Terms of Service</a> and <a href="/legal/privacy-policy">Privacy Policy</a>
        .
      </div>
    </div>
  );
}
