"use client";

import { useState, useEffect, useRef, useCallback } from "react";
import { Search as SearchIcon, X, Star, Clock, User, ArrowLeft } from "lucide-react";
import { cn } from "@workspace/ui/lib/utils";
import { Input } from "@workspace/ui/components/input";
import { But<PERSON> } from "@workspace/ui/components/button";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>rigger,
  TabsContent,
} from "@workspace/ui/components/tabs";
import { useQuery, useMutation, useConvex } from "convex/react";
import { api } from "@workspace/backend/convex/_generated/api";
import { Badge } from "@workspace/ui/components/badge";
import { Id } from "@workspace/backend/convex/_generated/dataModel";
import { useRouter } from "next/navigation";
import { getAvatarImageUrl } from "@/lib/utils";
import { UserAvatar } from "./user-avatar";
import { categories, subcategories } from "@workspace/lib/constants/categories";
import Image from "next/image";
import { User as UserType } from "@workspace/backend/convex/lib/types";
import { useDebounce } from "@/hooks/use-debounce";

type MinimalUser = {
  _id: Id<"users">;
  username?: string;
  name?: string;
  followers?: number;
  image?: string;
};

interface UserSearchResult {
  _id: string;
  username: string;
  name?: string;
  avatarUrl?: string;
  followers: number;
}

interface SavedSearch {
  _id: Id<"savedSearches">;
  searchQuery: string;
  type: "general" | "user" | "category" | "product";
  targetId?: string;
  savedAt: number;
  isPinned?: boolean;
  targetUser?: MinimalUser | null;
}

function useSavedSearchesPaginated({ type, pageSize = 20 }: { type?: "general" | "user" | "category" | "product"; pageSize?: number }) {
  const [searches, setSearches] = useState<SavedSearch[]>([]);
  const [cursor, setCursor] = useState<string | undefined>(undefined);
  const [isDone, setIsDone] = useState(false);
  const [loading, setLoading] = useState(false);
  const convex = useConvex();

  const fetchPage = useCallback(async () => {
    if (isDone || loading) return;
    setLoading(true);
    const res = await convex.query(api.savedSearches.getSavedSearches, {
      type,
      limit: pageSize,
      cursor,
    });
    setSearches(prev => [
      ...prev,
      ...(res.page || []).map(s => ({
        ...s,
        targetUser: s.targetUser && s.targetUser !== null
          ? {
              _id: s.targetUser._id,
              username: s.targetUser.username,
              name: s.targetUser.name,
              followers: s.targetUser.followersCount,
              image: s.targetUser.image,
            }
          : null,
      }))
    ]);
    setCursor(res.continueCursor);
    setIsDone(res.isDone);
    setLoading(false);
  }, [type, pageSize, cursor, isDone, loading, convex]);

  useEffect(() => {
    setSearches([]);
    setCursor(undefined);
    setIsDone(false);
  }, [type]);

  useEffect(() => {
    fetchPage();
  }, [type]);

  return { searches, isDone, loading, fetchPage };
}

export function Search() {
  const router = useRouter();
  const [isExpanded, setIsExpanded] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const debouncedQuery = useDebounce(searchQuery, 300);
  const inputRef = useRef<HTMLInputElement>(null);
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const checkIsMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };
    
    checkIsMobile();
    window.addEventListener('resize', checkIsMobile);
    
    return () => {
      window.removeEventListener('resize', checkIsMobile);
    };
  }, []);

  const userSearchResults = useQuery(
    api.users.searchUsers, 
    debouncedQuery ? { searchQuery: debouncedQuery } : "skip"
  );
  
  const recentSearches = useQuery(api.savedSearches.getRecentSearches, {}) as SavedSearch[] | undefined;
  const {
    searches: pagedSavedSearches,
    isDone: savedIsDone,
    loading: savedLoading,
    fetchPage: fetchSavedPage,
  } = useSavedSearchesPaginated({ pageSize: 20 });
  
  const deleteSavedSearch = useMutation(api.savedSearches.deleteSavedSearch);
  const saveSuggestion = useMutation(api.savedSearches.saveSuggestion);

  const filteredCategories = searchQuery
    ? categories.filter((cat) =>
        searchQuery.toLowerCase().split(/\s+/).every(word => cat.title.toLowerCase().includes(word)) ||
        cat.description.toLowerCase().includes(searchQuery.toLowerCase())
      )
    : [];

  const filteredSubcategories = searchQuery
    ? Object.entries(subcategories)
        .flatMap(([catId, subs]) =>
          subs.map((sub) => ({ ...sub, categoryId: catId }))
        )
        .filter((sub) =>
          sub.title.toLowerCase().includes(searchQuery.toLowerCase())
        )
    : [];

  const handleUserSelect = async (user: UserSearchResult) => {
    await saveSuggestion({
      searchQuery: user.username,
      type: "user",
      targetId: user._id,
    });
    
    router.push(`/user/${user.username}`);
    setIsExpanded(false);
    setSearchQuery("");
  };

  const handleSearchSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!searchQuery.trim()) return;
    
    await saveSuggestion({
      searchQuery: searchQuery.trim(),
      type: "general",
    });
    
    router.push(`/search?q=${encodeURIComponent(searchQuery.trim())}`);
    setIsExpanded(false);
    setSearchQuery("");
  };

  const handleDeleteSearch = async (searchId: Id<"savedSearches">) => {
    await deleteSavedSearch({ searchId });
  };

  useEffect(() => {
    if (isExpanded && inputRef.current) {
      inputRef.current.focus();
    }
    
    if (isMobile && isExpanded) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = '';
    }
    
    return () => {
      document.body.style.overflow = '';
    };
  }, [isExpanded, isMobile]);

  useEffect(() => {
    const handleEscapeKey = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && isExpanded) {
        setIsExpanded(false);
        inputRef.current?.blur();
      }
    };

    document.addEventListener('keydown', handleEscapeKey);
    return () => {
      document.removeEventListener('keydown', handleEscapeKey);
    };
  }, [isExpanded]);

  const renderCategoryResults = () => {
    if (!filteredCategories.length) return null;
    return (
      <div className="mb-4">
        <div className="flex items-center justify-between mb-2">
          <h3 className="text-sm font-medium">Categories</h3>
          <Button
            variant="ghost"
            size="sm"
            className="text-xs text-primary"
            onClick={() => {
              router.push(`/browse`);
              setIsExpanded(false);
            }}
          >
            See all
          </Button>
        </div>
        <div className="space-y-2">
          {filteredCategories
            .sort((a, b) => a.title.localeCompare(b.title))
            .slice(0, 3)
            .map((cat) => {
            return (
            <div
              key={cat.id}
              className="flex items-center gap-3 px-2 py-2 hover:bg-accent/50 rounded-lg cursor-pointer"
              onClick={() => {
                saveSuggestion({ searchQuery: cat.title, type: "category", targetId: cat.id });
                router.push(`/browse/${cat.id}`);
                setIsExpanded(false);
                setSearchQuery("");
              }}
            >
              <Image
                src={cat?.image || "/placeholder.png"}
                alt={cat.title}
                className="h-8 w-8 rounded object-cover"
                width={32}
                height={32}
              />
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium truncate">{cat.title}</p>
                <p className="text-xs text-muted-foreground truncate">{cat.description}</p>
              </div>
            </div>
          )})}
        </div>
      </div>
    );
  };

  const renderSubcategoryResults = () => {
    if (!filteredSubcategories.length) return null;
    return (
      <div className="mb-4">
        <div className="flex items-center justify-between mb-2">
          <h3 className="text-sm font-medium">Subcategories</h3>
        </div>
        <div className="space-y-2">
          {filteredSubcategories
            .sort((a, b) => a.title.localeCompare(b.title))
            .slice(0, 3)
            .map((sub) => (
            <div
              key={sub.id + "-" + sub.categoryId}
              className="flex items-center gap-3 px-2 py-2 hover:bg-accent/50 rounded-lg cursor-pointer"
              onClick={() => {
                saveSuggestion({ searchQuery: sub.title, type: "category", targetId: sub.id });
                router.push(`/browse/${sub.categoryId}?subcategory=${sub.id}`);
                setIsExpanded(false);
                setSearchQuery("");
              }}
            >
              {sub.image ? (
              <Image
                src={sub.image || ""}
                alt={sub.title}
                className="h-8 w-8 rounded object-cover"
                width={32}
                height={32}
              />
              ) : (
                <div className="h-8 w-8 rounded object-cover bg-accent/50 flex items-center justify-center">
                  <p className="text-xs text-muted-foreground">{sub.title.charAt(0)}</p>
                </div>
              )}
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium truncate">{sub.title}</p>
                <p className="text-xs text-muted-foreground truncate">Subcategory</p>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  };

  const renderUserResults = () => {
    if (!userSearchResults?.users?.length) return null;
    
    return (
      <div className="mb-4">
        <div className="flex items-center justify-between mb-2">
          <h3 className="text-sm font-medium">People</h3>
          <Button
            variant="ghost"
            size="sm"
            className="text-xs text-primary"
            onClick={() => {
              router.push(`/search/users?q=${encodeURIComponent(searchQuery)}`);
              setIsExpanded(false);
            }}
          >
            See all
          </Button>
        </div>
        <div className="space-y-2">
          {userSearchResults.users.slice(0, 3).map((user: any) => (
            <div
              key={user._id}
              className="flex items-center gap-3 px-2 py-2 hover:bg-accent/50 rounded-lg cursor-pointer"
              onClick={() => handleUserSelect({
                _id: user._id,
                username: user.username || '',
                name: user.name,
                avatarUrl: getAvatarImageUrl(user.image),
                followers: user.followers || 0
              })}
            >
              <UserAvatar user={user as UserType} height={30} width={30} />
              <div className="flex-1 min-w-0">
                <div className="flex items-baseline gap-1">
                  <p className="text-sm font-medium truncate">{user.username || ''}</p>
                  <p className="text-xs text-muted-foreground truncate">{user.name}</p>
                </div>
                <div className="flex items-center text-xs text-muted-foreground">
                  <User className="h-3 w-3 mr-1" />
                  <span>{user.followersCount ?? 0} follower{(user.followersCount ?? 0) !== 1 ? 's' : ''}</span>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  };

  if (isMobile && isExpanded) {
    return (
      <div className="fixed inset-0 bg-background z-50 flex flex-col">
        <div className="px-4 py-3 border-b flex items-center gap-2">
          <Button 
            variant="ghost" 
            size="icon" 
            className="h-8 w-8" 
            onClick={() => setIsExpanded(false)}
          >
            <ArrowLeft className="h-5 w-5" />
          </Button>
          
          <form onSubmit={handleSearchSubmit} className="flex-1">
            <Input
              ref={inputRef}
              autoFocus
              placeholder="Search Liveciety"
              className="h-10 bg-accent/50"
              value={searchQuery}
              onChange={(e) => {
                setSearchQuery(e.target.value);
                setIsExpanded(false);
              }}
            />
          </form>
        </div>
        
        <div className="flex-1 overflow-y-auto">
          <Tabs defaultValue="results" className="w-full">
            <div className="sticky top-0 bg-background flex px-4 pt-3 border-b z-10">
              <TabsList className="bg-transparent p-0 h-auto justify-start items-center gap-4">
                <TabsTrigger
                  value="results"
                  className={cn(
                    "pb-3 -mb-px rounded-none h-auto px-0 data-[state=active]:shadow-none data-[state=active]:bg-transparent",
                    "data-[state=active]:text-foreground",
                    "data-[state=inactive]:border-transparent data-[state=inactive]:text-muted-foreground",
                  )}
                >
                  Results
                </TabsTrigger>
                <TabsTrigger
                  value="recent"
                  className={cn(
                    "pb-3 -mb-px rounded-none h-auto px-0 data-[state=active]:shadow-none data-[state=active]:bg-transparent",
                    "data-[state=active]:text-foreground",
                    "data-[state=inactive]:border-transparent data-[state=inactive]:text-muted-foreground",
                  )}
                >
                  Recent
                </TabsTrigger>
              </TabsList>
            </div>

            <TabsContent value="results" className="p-4">
              {searchQuery ? (
                <>
                  {renderUserResults()}
                  {renderCategoryResults()}
                  {renderSubcategoryResults()}
                  {(!userSearchResults || !userSearchResults.users?.length) && (
                    <div className="py-6 text-center">
                      <p className="text-sm text-muted-foreground">
                        No results found for "{searchQuery}"
                      </p>
                    </div>
                  )}
                </>
              ) : (
                <div className="py-6 text-center">
                  <p className="text-sm text-muted-foreground">
                    Start typing to search for users, products, or categories
                  </p>
                </div>
              )}
            </TabsContent>

            <TabsContent value="recent" className="p-4">
              <h3 className="text-sm text-muted-foreground mb-2">
                Your Recent Searches
              </h3>
              {recentSearches?.length ? (
                <div className="space-y-1">
                  {recentSearches.map((search: SavedSearch) => (
                    <div
                      key={search._id}
                      className="flex items-center gap-2 px-2 py-1.5 hover:bg-accent/50 rounded-lg group cursor-pointer"
                      onClick={() => {
                        if (search.type === 'user' && search.targetId) {
                          router.push(`/user/${search.searchQuery}`);
                        } else {
                          router.push(`/search?q=${encodeURIComponent(search.searchQuery)}`);
                        }
                        setIsExpanded(false);
                      }}
                    >
                      {search.type === 'user' ? (
                        <>
                          <UserAvatar user={search.targetUser as MinimalUser} height={20} width={20} />
                          <div className="flex flex-col flex-1 min-w-0">
                            <span className="text-sm truncate">{search.searchQuery}</span>
                            <span className="text-xs text-muted-foreground">
                              {search.targetUser?.followers ?? 0} follower{(search.targetUser?.followers ?? 0) !== 1 ? 's' : ''}
                            </span>
                          </div>
                        </>
                      ) : (
                        <>
                          <Clock className="h-4 w-4 text-muted-foreground" />
                          <span className="text-sm flex-1">{search.searchQuery}</span>
                        </>
                      )}
                      <Badge variant="outline" className="text-xs px-1">
                        {search.type}
                      </Badge>
                      <Button
                        variant="ghost"
                        size="icon"
                        className="opacity-0 group-hover:opacity-100 size-6 p-0"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleDeleteSearch(search._id);
                        }}
                      >
                        <X className="h-3 w-3" />
                      </Button>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-sm text-muted-foreground">
                  No recent searches
                </div>
              )}
            </TabsContent>

            <TabsContent value="saved" className="p-4">
              {pagedSavedSearches?.filter(s => s.isPinned)?.length ? (
                <div className="space-y-1">
                  {pagedSavedSearches.filter(s => s.isPinned).map((search: SavedSearch) => (
                    <div
                      key={search._id}
                      className="flex items-center gap-2 px-2 py-1.5 hover:bg-accent/50 rounded-lg group cursor-pointer"
                      onClick={() => {
                        if (search.type === 'user' && search.targetId) {
                          router.push(`/user/${search.searchQuery}`);
                        } else {
                          router.push(`/search?q=${encodeURIComponent(search.searchQuery)}`);
                        }
                        setIsExpanded(false);
                      }}
                    >
                      {search.type === 'user' ? (
                        <>
                          <UserAvatar user={search.targetUser as UserType} height={20} width={20} />
                          <div className="flex flex-col flex-1 min-w-0">
                            <span className="text-sm truncate">{search.searchQuery}</span>
                            <span className="text-xs text-muted-foreground">
                              {search.targetUser?.followers ?? 0} follower{(search.targetUser?.followers ?? 0) !== 1 ? 's' : ''}
                            </span>
                          </div>
                        </>
                      ) : (
                        <>
                          <Clock className="h-4 w-4 text-muted-foreground" />
                          <span className="text-sm flex-1">{search.searchQuery}</span>
                        </>
                      )}
                      <Badge variant="outline" className="text-xs px-1">
                        {search.type}
                      </Badge>
                      <Button
                        variant="ghost"
                        size="icon"
                        className="opacity-0 group-hover:opacity-100 size-6 p-0"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleDeleteSearch(search._id);
                        }}
                      >
                        <X className="h-3 w-3" />
                      </Button>
                    </div>
                  ))}
                  {!savedIsDone && (
                    <div className="flex justify-center mt-2">
                      <Button onClick={fetchSavedPage} disabled={savedLoading} size="sm">
                        {savedLoading ? "Loading..." : "Load More"}
                      </Button>
                    </div>
                  )}
                </div>
              ) : (
                <div className="text-sm text-muted-foreground">
                  No saved searches yet. Star a search to save it here.
                </div>
              )}
            </TabsContent>
          </Tabs>
        </div>
      </div>
    );
  }

  return (
    <div className="relative flex-1">
      <div className="w-full">
        <form onSubmit={handleSearchSubmit}>
          <div
            className={cn(
              "rounded-2xl overflow-hidden transition-all",
              isExpanded && "rounded-b-none",
            )}
          >
            <Input
              ref={inputRef}
              placeholder="Search Liveciety"
              className={cn(
                "border-border rounded-2xl h-10 bg-accent/50 dark:bg-accent/50",
                isExpanded && "rounded-b-none border-b-0 border-input",
              )}
              leftIcon={<SearchIcon className="text-muted-foreground" />}
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              onFocus={() => setIsExpanded(true)}
              onBlur={(e) => {
                const dropdown = document.getElementById("search-dropdown");
                if (!dropdown?.contains(e.relatedTarget as Node)) {
                  setIsExpanded(false);
                }
              }}
            />
          </div>
        </form>

        {isExpanded && (
          <div
            id="search-dropdown"
            className="absolute top-full left-0 right-0 bg-background dark:bg-background/95 rounded-b-2xl border shadow-lg z-50"
            tabIndex={-1}
          >
            <Tabs defaultValue="results" className="w-full">
              <div className="flex px-4 pt-3 border-b">
                <TabsList className="bg-transparent p-0 h-auto justify-start items-center gap-4">
                  <TabsTrigger
                    value="results"
                    className={cn(
                      "pb-3 -mb-px rounded-none h-auto px-0 data-[state=active]:shadow-none data-[state=active]:bg-transparent",
                      "data-[state=active]:text-foreground",
                      "data-[state=inactive]:border-transparent data-[state=inactive]:text-muted-foreground",
                    )}
                  >
                    Results
                  </TabsTrigger>
                  <TabsTrigger
                    value="recent"
                    className={cn(
                      "pb-3 -mb-px rounded-none h-auto px-0 data-[state=active]:shadow-none data-[state=active]:bg-transparent",
                      "data-[state=active]:text-foreground",
                      "data-[state=inactive]:border-transparent data-[state=inactive]:text-muted-foreground",
                    )}
                  >
                    Recent
                  </TabsTrigger>
                </TabsList>
              </div>

              <TabsContent value="results" className="p-4">
                {searchQuery ? (
                  <>
                    {renderUserResults()}
                    {renderCategoryResults()}
                    {renderSubcategoryResults()}
                    {(!userSearchResults || !userSearchResults.users?.length) && (
                      <div className="py-6 text-center">
                        <p className="text-sm text-muted-foreground">
                          No results found for "{searchQuery}"
                        </p>
                      </div>
                    )}
                  </>
                ) : (
                  <div className="py-6 text-center">
                    <p className="text-sm text-muted-foreground">
                      Start typing to search for users, products, or categories
                    </p>
                  </div>
                )}
              </TabsContent>

              <TabsContent value="recent" className="p-4">
                <h3 className="text-sm text-muted-foreground mb-2">
                  Your Recent Searches
                </h3>
                {recentSearches?.length ? (
                  <div className="space-y-1">
                    {recentSearches.map((search: SavedSearch) => (
                      <div
                        key={search._id}
                        className="flex items-center gap-2 px-2 py-1.5 hover:bg-accent/50 rounded-lg group cursor-pointer"
                        onClick={() => {
                          if (search.type === 'user' && search.targetId) {
                            router.push(`/user/${search.searchQuery}`);
                          } else {
                            router.push(`/search?q=${encodeURIComponent(search.searchQuery)}`);
                          }
                          setIsExpanded(false);
                        }}
                      >
                        {search.type === 'user' ? (
                          <>
                            <UserAvatar user={search.targetUser as UserType} height={20} width={20} />
                            <div className="flex flex-col flex-1 min-w-0">
                              <span className="text-sm truncate">{search.searchQuery}</span>
                              <span className="text-xs text-muted-foreground">
                                {search.targetUser?.followers ?? 0} follower{(search.targetUser?.followers ?? 0) !== 1 ? 's' : ''}
                              </span>
                            </div>
                          </>
                        ) : (
                          <>
                            <Clock className="h-4 w-4 text-muted-foreground" />
                            <span className="text-sm flex-1">{search.searchQuery}</span>
                          </>
                        )}
                        <Badge variant="outline" className="text-xs px-1">
                          {search.type}
                        </Badge>
                        <Button
                          variant="ghost"
                          size="icon"
                          className="opacity-0 group-hover:opacity-100 size-6 p-0"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleDeleteSearch(search._id);
                          }}
                        >
                          <X className="h-3 w-3" />
                        </Button>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-sm text-muted-foreground">
                    No recent searches
                  </div>
                )}
              </TabsContent>
            </Tabs>
          </div>
        )}
      </div>
    </div>
  );
}
