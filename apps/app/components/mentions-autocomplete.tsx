import React, { useState, useEffect, useRef } from "react";
import { useQuery } from "convex/react";
import { api } from "@workspace/backend/convex/_generated/api";
import { UserAvatar } from "./user-avatar";
import { ScrollArea } from "@workspace/ui/components/scroll-area";
import { cn } from "@workspace/ui/lib/utils";

interface MentionUser {
  _id: string;
  username: string;
  name?: string;
  image?: string;
  color?: string;
}

interface MentionsAutocompleteProps {
  value: string;
  caretPosition: number;
  onMentionSelect: (username: string, user: MentionUser) => void;
  onClose: () => void;
  className?: string;
}

export const MentionsAutocomplete: React.FC<MentionsAutocompleteProps> = ({
  value,
  caretPosition,
  onMentionSelect,
  onClose,
  className,
}) => {
  const [query, setQuery] = useState("");
  const [open, setOpen] = useState(false);
  const [selectedIndex, setSelectedIndex] = useState(0);
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const textUpToCaret = value.slice(0, caretPosition);
    const match = /(?:^|\s)@([\w]*)$/.exec(textUpToCaret);
    if (match) {
      setQuery(match[1] || "");
      setOpen(true);
    } else {
      setQuery("");
      setOpen(false);
    }
  }, [value, caretPosition]);

  const searchResults = useQuery(
    api.users.searchUsers,
    open && query.length > 0 ? { searchQuery: query } : "skip"
  );
  const users: MentionUser[] = searchResults?.users || [];

  useEffect(() => {
    if (!open) return;
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === "ArrowDown") {
        setSelectedIndex((prev) => Math.min(prev + 1, users.length - 1));
        e.preventDefault();
      } else if (e.key === "ArrowUp") {
        setSelectedIndex((prev) => Math.max(prev - 1, 0));
        e.preventDefault();
      } else if (e.key === "Enter") {
        if (users[selectedIndex]) {
          onMentionSelect(users[selectedIndex].username, users[selectedIndex]);
          setOpen(false);
        }
        e.preventDefault();
      } else if (e.key === "Escape") {
        setOpen(false);
        onClose();
      }
    };
    window.addEventListener("keydown", handleKeyDown);
    return () => window.removeEventListener("keydown", handleKeyDown);
  }, [open, users, selectedIndex, onMentionSelect, onClose]);

  useEffect(() => {
    if (!open) return;
    const handleClick = (e: MouseEvent) => {
      if (
        containerRef.current &&
        !containerRef.current.contains(e.target as Node)
      ) {
        setOpen(false);
        onClose();
      }
    };
    document.addEventListener("mousedown", handleClick);
    return () => document.removeEventListener("mousedown", handleClick);
  }, [open, onClose]);

  if (!open || users.length === 0) return null;

  return (
    <div
      ref={containerRef}
      className={cn(
        "absolute z-50 left-0 right-0 w-full bg-background shadow-lg overflow-hidden bottom-full mb-2 p-1",
        className
      )}
      style={{ left: 0 }}
    >
      <ScrollArea className="max-h-60">
        <ul>
          {users.map((user, idx) => (
            <li
              key={user._id}
              className={cn(
                "flex items-center gap-2 px-3 py-2 cursor-pointer hover:bg-accent rounded-md",
                idx === selectedIndex && "bg-accent"
              )}
              onMouseDown={(e) => {
                e.preventDefault();
                onMentionSelect(user.username, user);
                setOpen(false);
              }}
              onMouseEnter={() => setSelectedIndex(idx)}
            >
              <UserAvatar user={user} size="xs" />
              <span className="font-medium text-sm" style={{ color: user.color }}>{user.username}</span>
              {user.name && (
                <span className="ml-2 text-xs text-muted-foreground">{user.name}</span>
              )}
            </li>
          ))}
        </ul>
      </ScrollArea>
    </div>
  );
}; 