"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@workspace/ui/components/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  Di<PERSON>Title,
  DialogFooter,
} from "@workspace/ui/components/dialog";
import { Label } from "@workspace/ui/components/label";
import { Textarea } from "@workspace/ui/components/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@workspace/ui/components/select";
import { useMutation, useQuery } from "convex/react";
import { api } from "@workspace/backend/convex/_generated/api";
import { toast } from "sonner";
import { cn } from "@workspace/ui/lib/utils";
import { useSidebar } from "@workspace/ui/components/sidebar";
import { Tooltip, TooltipContent, TooltipTrigger } from "@workspace/ui/components/tooltip";
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@workspace/ui/components/tabs";
import { format } from "date-fns";
import { Badge } from "@workspace/ui/components/badge";
import { 
  MessageSquare, 
  Bug, 
  Lightbulb, 
  Clock, 
  Send, 
  Plus, 
  Loader2,
  CheckCircle2,
  AlertCircle,
  CornerDownRight,
  Calendar
} from "lucide-react";
import { Card, CardContent } from "@workspace/ui/components/card";
import { Separator } from "@workspace/ui/components/separator";

export function FeedbackButton() {
  const { state, isMobile } = useSidebar();
  const [open, setOpen] = useState(false);
  const [feedbackType, setFeedbackType] = useState<"bug" | "feature_request">("bug");
  const [message, setMessage] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [activeTab, setActiveTab] = useState("submit");
  
  const isCollapsed = state === "collapsed" && !isMobile;
  
  const submitFeedback = useMutation(api.feedback.submitFeedback);
  const userFeedback = useQuery(api.feedback.getUserFeedback);

  const handleSubmit = async () => {
    if (!message.trim()) {
      toast.error("Please enter a message");
      return;
    }

    setIsSubmitting(true);

    try {
      await submitFeedback({
        type: feedbackType,
        message,
        url: window.location.href,
        metadata: {
          browser: navigator.userAgent,
          device: navigator.platform,
          os: navigator.platform,
        },
      });

      toast.success("Feedback submitted successfully!");
      setMessage("");
      setFeedbackType("bug");
      setActiveTab("history");
    } catch (error) {
      console.error("Error submitting feedback:", error);
      toast.error("Failed to submit feedback. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "new":
        return <Badge variant="secondary" className="bg-blue-100 text-blue-700 dark:bg-blue-900/30 dark:text-blue-400">New</Badge>;
      case "in_progress":
        return <Badge variant="secondary" className="bg-yellow-100 text-yellow-700 dark:bg-yellow-900/30 dark:text-yellow-400">In Progress</Badge>;
      case "done":
        return <Badge variant="secondary" className="bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-400">Completed</Badge>;
      case "rejected":
        return <Badge variant="secondary" className="bg-red-100 text-red-700 dark:bg-red-900/30 dark:text-red-400">Rejected</Badge>;
      default:
        return <Badge variant="outline">Unknown</Badge>;
    }
  };

  const getFeedbackTypeIcon = (type: string) => {
    return type === "bug" ? (
      <Bug className="h-4 w-4 text-red-500" />
    ) : (
      <Lightbulb className="h-4 w-4 text-amber-500" />
    );
  };

  const buttonContent = (
    <>
      <MessageSquare className="h-4 w-4" />
      <span className={cn({ "hidden": isCollapsed })}>Feedback</span>
    </>
  );

  return (
    <>
      {isCollapsed ? (
        <Tooltip>
          <TooltipTrigger asChild>
            <Button 
              variant="ghost" 
              size="icon"
              className="w-full flex justify-center"
              onClick={() => setOpen(true)}
            >
              {buttonContent}
            </Button>
          </TooltipTrigger>
          <TooltipContent side="right">
            Send Feedback
          </TooltipContent>
        </Tooltip>
      ) : (
        <Button 
          variant="ghost" 
          size="sm" 
          className="w-full justify-start px-2 gap-2"
          onClick={() => setOpen(true)}
        >
          {buttonContent}
        </Button>
      )}

      <Dialog open={open} onOpenChange={setOpen}>
        <DialogContent
          className="fixed md:!left-65 md:!bottom-1 sm:bottom-0 sm:left-0 top-0 sm:right-4 sm:bottom-4 sm:left-auto sm:top-auto w-full sm:w-[550px] max-w-[100vw] p-0 overflow-hidden rounded-none sm:rounded-xl border border-border/80 sm:border shadow-none sm:shadow-xl bg-background z-50"
          style={{ margin: 0 }}
          overlay={false}
          placement="custom"
        >
          <div className="absolute top-0 left-0 right-0 h-1.5 bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500"></div>
          <DialogHeader className="px-6 pt-6 pb-2">
            <DialogTitle className="text-xl flex items-center gap-2 font-semibold">
              <MessageSquare className="h-5 w-5 text-primary" />
              Feedback
            </DialogTitle>
            <DialogDescription className="text-muted-foreground">
              Help us improve by sharing your thoughts or reporting issues.
            </DialogDescription>
          </DialogHeader>
          
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <div className="px-6 pt-2">
              <TabsList className="grid w-full grid-cols-2 mb-4 p-1 bg-muted/50 rounded-lg">
                <TabsTrigger 
                  value="submit" 
                  className="flex items-center gap-2 rounded-md data-[state=active]:bg-background data-[state=active]:shadow-sm"
                >
                  <Send className="h-4 w-4" />
                  Submit Feedback
                </TabsTrigger>
                <TabsTrigger 
                  value="history" 
                  className="flex items-center gap-2 rounded-md data-[state=active]:bg-background data-[state=active]:shadow-sm"
                >
                  <Clock className="h-4 w-4" />
                  Your History
                </TabsTrigger>
              </TabsList>
            </div>
            
            <TabsContent value="submit" className="max-h-[450px] h-[450px] min-h-[450px] overflow-y-auto px-6 py-4 space-y-6 border-t">
              <div className="space-y-5">
                <div className="space-y-2">
                  <Label htmlFor="feedback-type" className="text-sm font-medium">
                    Feedback Type
                  </Label>
                  <Select
                    value={feedbackType}
                    onValueChange={(value) => setFeedbackType(value as "bug" | "feature_request")}
                  >
                    <SelectTrigger 
                      className="w-full bg-background border border-input" 
                      id="feedback-type"
                    >
                      <SelectValue placeholder="Select type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="bug" className="flex items-center gap-2">
                        <div className="flex items-center gap-2">
                          <Bug className="h-4 w-4 text-red-500" />
                          Bug Report
                        </div>
                      </SelectItem>
                      <SelectItem value="feature_request" className="flex items-center gap-2">
                        <div className="flex items-center gap-2">
                          <Lightbulb className="h-4 w-4 text-amber-500" />
                          Feature Request
                        </div>
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="message" className="text-sm font-medium">
                    Your Message
                  </Label>
                  <Textarea
                    id="message"
                    value={message}
                    onChange={(e) => setMessage(e.target.value)}
                    placeholder="Please describe your feedback in detail..."
                    className="min-h-[150px] resize-none bg-background border border-input"
                  />
                  <p className="text-xs text-muted-foreground">
                    {feedbackType === "bug" 
                      ? "Please include steps to reproduce the issue if possible." 
                      : "Describe how this feature would help improve your experience."}
                  </p>
                </div>
              </div>

              <div className="flex justify-end pt-2">
                <Button 
                  type="submit" 
                  disabled={isSubmitting || !message.trim()} 
                  onClick={handleSubmit}
                  className="gap-2 relative overflow-hidden group"
                >
                  {isSubmitting ? (
                    <>
                      <Loader2 className="h-4 w-4 animate-spin" />
                      Submitting...
                    </>
                  ) : (
                    <>
                      <Send className="h-4 w-4" />
                      Submit Feedback
                      <span className="absolute inset-0 w-0 bg-white/20 transition-all duration-300 group-hover:w-full"></span>
                    </>
                  )}
                </Button>
              </div>
            </TabsContent>
            
            <TabsContent value="history" className="border-t max-h-[450px] h-[450px] min-h-[450px] overflow-y-auto space-y-4">
              {userFeedback?.length === 0 ? (
                <div className="text-center py-12 space-y-4">
                  <div className="flex justify-center">
                    <div className="rounded-full bg-muted/50 p-4">
                      <MessageSquare className="h-10 w-10 text-muted-foreground/50" />
                    </div>
                  </div>
                  <div className="space-y-2">
                    <h3 className="text-lg font-medium">No feedback yet</h3>
                    <p className="text-muted-foreground max-w-md mx-auto">
                      You haven't submitted any feedback yet. We'd love to hear your thoughts!
                    </p>
                  </div>
                  <Button 
                    variant="outline" 
                    onClick={() => setActiveTab("submit")}
                    className="mt-2 gap-2"
                  >
                    <Plus className="h-4 w-4" />
                    New Feedback
                  </Button>
                </div>
              ) : userFeedback === undefined ? (
                <div className="text-center py-12">
                  <div className="animate-pulse flex flex-col items-center gap-4">
                    <div className="h-12 w-12 rounded-full bg-muted"></div>
                    <div className="h-4 w-32 bg-muted rounded"></div>
                    <div className="h-3 w-48 bg-muted rounded"></div>
                  </div>
                </div>
              ) : (
                <div>
                  {userFeedback.map((item) => (
                    <Card 
                      key={item._id} 
                      className="group overflow-hidden border-0 shadow-md hover:shadow-lg transition-all duration-200 bg-gradient-to-br from-background to-muted/30 p-0 rounded-none border-b border-border/40 hover:bg-muted/50"
                    >
                      <div className="absolute -left-px -top-px block size-2 border-l-2 border-t-2 border-primary/60"></div>
                      <div className="absolute -right-px -top-px block size-2 border-r-2 border-t-2 border-primary/60"></div>
                      <div className="absolute -left-px -bottom-px block size-2 border-l-2 border-b-2 border-primary/60"></div>
                      <div className="absolute -right-px -bottom-px block size-2 border-r-2 border-b-2 border-primary/60"></div>
                      
                      <CardContent className="p-0">
                        <div className="flex justify-between items-center p-2 px-4 border-b border-border/40 bg-muted/20">
                          <div className="flex items-center gap-2">
                            <div className="flex items-center justify-center size-7 rounded-full bg-background border border-border/50 shadow-sm">
                              {getFeedbackTypeIcon(item.type)}
                            </div>
                            <span className="font-medium capitalize text-foreground">
                              {item.type.replace('_', ' ')}
                            </span>
                          </div>
                          <div className="flex items-center gap-3">
                            {getStatusBadge(item.status)}
                            <div className="flex items-center gap-1.5 text-xs text-muted-foreground">
                              <Calendar className="h-3 w-3" />
                              {format(new Date(item.createdAt), 'MMM d, yyyy')}
                            </div>
                          </div>
                        </div>
                        
                        <div className="p-4">
                          <p className="text-sm whitespace-pre-wrap leading-relaxed text-foreground/90">{item.message}</p>
                          
                          {item.notes && (
                            <div className="mt-4 pt-4 relative">
                              <div className="absolute top-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-border to-transparent"></div>
                              <div className="flex gap-2 items-start mt-1">
                                <div className="flex-shrink-0 mt-1">
                                  <CornerDownRight className="h-3.5 w-3.5 text-primary/70" />
                                </div>
                                <div className="flex-1 rounded-lg bg-primary/5 border border-primary/10 p-3 shadow-sm">
                                  <div className="flex items-center gap-1.5 mb-2">
                                    <CheckCircle2 className="h-3.5 w-3.5 text-primary" />
                                    <span className="text-xs font-medium uppercase tracking-wide text-primary/80">
                                      Response
                                    </span>
                                  </div>
                                  <p className="text-sm leading-relaxed text-foreground/90">{item.notes}</p>
                                </div>
                              </div>
                            </div>
                          )}
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}
            </TabsContent>
          </Tabs>

          <DialogFooter className="px-6 py-4 border-t bg-muted/10 flex items-center justify-between">
            <div className="text-xs text-muted-foreground flex items-center gap-1.5">
              <AlertCircle className="h-3.5 w-3.5" />
              Your feedback helps us improve our product
            </div>
            {activeTab === "submit" && (
              <div className="text-xs text-muted-foreground">
                {message.length} / 500 characters
              </div>
            )}
              {activeTab === "history" && (
                <div className="sticky bottom-0 flex justify-end bg-gradient-to-t from-background to-transparent">
                  <Button 
                    size="sm"
                    onClick={() => { 
                      setActiveTab("submit");
                      setMessage("");
                      setFeedbackType("bug");
                    }}
                    className="gap-2 bg-primary/90 hover:bg-primary transition-colors shadow-sm"
                  >
                    <Plus className="h-4 w-4" />
                    New Feedback
                  </Button>
                </div>
              )}
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}

export default FeedbackButton; 