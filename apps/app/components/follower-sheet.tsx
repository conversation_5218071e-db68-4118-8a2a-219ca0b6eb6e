import React, { useEffect, useState } from "react";
import Link from "next/link";
import { useMutation, useQuery } from "convex/react";
import { api } from "@workspace/backend/convex/_generated/api";
import { But<PERSON> } from "@workspace/ui/components/button";
import {
  She<PERSON>,
  <PERSON><PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON>eader,
  Sheet<PERSON>itle,
  SheetDescription,
} from "@workspace/ui/components/sheet";
import { UserAvatar } from "./user-avatar";
import { Id } from "@workspace/backend/convex/_generated/dataModel";
import { Badge } from "@workspace/ui/components/badge";
import { UsersIcon, Loader2 } from "lucide-react";
import { useInView } from "react-intersection-observer";
import { useToggleFollow } from "../hooks/use-toggle-follow";

type UserResult = any[] | { page?: any[], users?: any[] } | null | undefined;

interface FollowerSheetProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  title: string;
  description: string;
  initialUsers: UserResult;
  currentUser: any | null | undefined;
  emptyMessage?: string;
  context?: "followers" | "following";
  hasMore: boolean;
  loadMore: () => Promise<void>;
}

export function FollowerSheet({
  open,
  onOpenChange,
  title,
  description,
  initialUsers,
  currentUser,
  emptyMessage = "No users found",
  context = "followers",
  hasMore,
  loadMore,
}: FollowerSheetProps) {
  const followUser = useMutation(api.users.followUser);
  const unfollowUser = useMutation(api.users.unfollowUser);
  
  const [displayedUsers, setDisplayedUsers] = useState<any[]>([]);
  const [isClient, setIsClient] = useState(false);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  
  const { ref, inView } = useInView({
    threshold: 0,
    triggerOnce: false,
    rootMargin: '100px',
  });
  
  useEffect(() => {
    setIsClient(true);
    
    let users: any[] = [];
    if (Array.isArray(initialUsers)) {
      users = initialUsers;
    } else if (initialUsers && typeof initialUsers === 'object') {
      if ('page' in initialUsers && Array.isArray(initialUsers.page)) {
        users = initialUsers.page;
      } else if ('users' in initialUsers && Array.isArray(initialUsers.users)) {
        users = initialUsers.users;
      } else if ('results' in initialUsers && Array.isArray(initialUsers.results)) {
        users = initialUsers.results;
      }
    }
    
    setDisplayedUsers(users);
  }, [initialUsers]);

  useEffect(() => {
    if (inView && hasMore && !isLoadingMore && isClient) {
      const fetchMore = async () => {
        setIsLoadingMore(true);
        try {
          await loadMore();
        } catch (error) {
          console.error("Failed to load more users:", error);
        } finally {
          setIsLoadingMore(false);
        }
      };
      fetchMore();
    }
  }, [inView, hasMore, isLoadingMore, loadMore, isClient]);

  const UserRow = ({ user, isFollower }: { user: any, isFollower: boolean }) => {
    const { isFollowing, toggleFollow, loading } = useToggleFollow({
      userId: user._id,
      username: user.username,
      skip: !isClient || !currentUser || user._id === currentUser._id,
    });

    const areFriends = useQuery(
      api.users.areMutualFollowers,
      isClient && currentUser && user._id !== currentUser._id
        ? { targetUserId: user._id }
        : "skip"
    );

    const isFollowingCurrentUser = useQuery(
      api.users.isUserFollowing,
      isClient && currentUser && user._id !== currentUser._id
        ? { userId: user._id, targetUserId: currentUser._id }
        : "skip"
    );

    return (
      <Link 
        href={`/user/${user.username}`} 
        key={user._id}
        onClick={() => onOpenChange(false)}
        className="flex items-center gap-3 p-2 rounded-md transition-colors hover:bg-primary/10"
      >
        <UserAvatar user={user} />
        <div>
          <div className="flex items-center gap-2">
            <p className="font-medium">{user.username}</p>
            {areFriends && (
              <Badge variant="outline" className="bg-blue-50 text-blue-700 text-xs border-blue-200">
                <UsersIcon className="w-3 h-3 mr-1" />
                Friend
              </Badge>
            )}
          </div>
          {user.name && <p className="text-sm text-gray-500">{user.name}</p>}
        </div>
        {currentUser && currentUser._id !== user._id && (
          <Button 
            variant={isFollowing ? "outline" : "default"}
            size="sm" 
            className="ml-auto rounded-full"
            onClick={e => {
              e.preventDefault();
              e.stopPropagation();
              toggleFollow();
            }}
            disabled={loading}
          >
            {isFollowing 
              ? "Unfollow" 
              : (context === "followers" && isFollowingCurrentUser ? "Follow Back" : "Follow")}
          </Button>
        )}
      </Link>
    );
  };

  return (
    <Sheet open={open} onOpenChange={onOpenChange}>
      <SheetContent overlay={false} className="flex flex-col h-full">
        <SheetHeader className="border-b pb-4 mb-4 flex-shrink-0">
          <SheetTitle>{title}</SheetTitle>
          <SheetDescription>
            {description}
          </SheetDescription>
        </SheetHeader>
        
        <div className="flex-grow space-y-2 overflow-y-auto px-4" id="follower-sheet-scrollable-content">
          {displayedUsers && displayedUsers.length > 0 ? (
            displayedUsers.map((user) => {
              if (!user || !user._id) return null;
              return (
                <UserRow 
                  key={user._id} 
                  user={user} 
                  isFollower={context === "followers"} 
                />
              );
            })
          ) : (
            <div className="text-center py-8">
              <p className="text-gray-500">{emptyMessage}</p>
            </div>
          )}
          {hasMore && (
            <div 
              ref={ref} 
              className="flex justify-center items-center py-4"
              style={{ minHeight: '50px' }}
            >
              {isLoadingMore ? (
                <Loader2 className="h-6 w-6 animate-spin text-gray-500" />
              ) : (
                <span className="text-sm text-gray-400">Scroll to load more</span>
              )}
            </div>
          )}
        </div>
      </SheetContent>
    </Sheet>
  );
} 