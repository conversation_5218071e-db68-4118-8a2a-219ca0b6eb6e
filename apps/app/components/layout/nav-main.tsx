"use client";

import {
  SidebarGroup,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "@workspace/ui/components/sidebar";
import { usePathname } from "next/navigation";
import { Icon } from "@tabler/icons-react";
import { cn } from "@workspace/ui/lib/utils";
import Link from "next/link";

export function NavMain({
  items,
}: {
  items: {
    title: string;
    url: string;
    icon?: Icon;
  }[];
}) {
  const pathname = usePathname();

  const isActive = (url: string) => {
    return pathname === url;
  };

  return (
    <SidebarGroup>
      <SidebarGroupLabel>Platform</SidebarGroupLabel>
      <SidebarMenu>
        {items.map((item) => (
          <SidebarMenuItem
            key={item.title}
            className="flex items-center justify-center"
          >
            <Link href={item.url} className="w-full">
              <SidebarMenuButton
                tooltip={item.title}
                className={cn(
                  "group/item w-full transition-colors duration-200",
                  isActive(item.url) && "bg-sky-800 text-white",
                )}
              >
                {item.icon && (
                  <item.icon
                    size={20}
                    stroke={1.5}
                    className={cn(
                      "flex-shrink-0",
                      isActive(item.url)
                        ? "text-white"
                        : "text-sidebar-foreground",
                      "group-hover/item:text-accent-foreground",
                    )}
                  />
                )}
                <span
                  className={cn(
                    isActive(item.url)
                      ? "text-white"
                      : "text-sidebar-foreground",
                    "group-hover/item:text-accent-foreground",
                  )}
                >
                  {item.title}
                </span>
              </SidebarMenuButton>
            </Link>
          </SidebarMenuItem>
        ))}
      </SidebarMenu>
    </SidebarGroup>
  );
}
