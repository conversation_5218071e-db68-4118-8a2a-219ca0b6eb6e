"use client";

import { usePreloadedQuery } from "convex/react";
import {
  SidebarGroup,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuItem,
  SidebarMenuButton,
} from "@workspace/ui/components/sidebar";
import { Plus } from "lucide-react";
import { categories as allCategories, subcategories as allSubcategories, Subcategory } from "@workspace/lib/constants/categories";
import Image from "next/image";
import Link from "next/link";
import { usePathname, useSearchParams } from "next/navigation";
import { usePreloadedData } from "@/hooks/use-preloaded-data";
import { IconBookmark } from "@tabler/icons-react";

export function UserPreferences() {
  const { preloadedUser } = usePreloadedData();
  const user = usePreloadedQuery(preloadedUser);

  const pathname = usePathname();
  const searchParams = useSearchParams();
  
  if (!user) {
    return null;
  }

  const preferences = user.preferences || {};
  const userCategories = preferences.categories || [];
  const userSubcategories = preferences.subcategories || [];

  const hasPreferences = userCategories.length > 0 || userSubcategories.length > 0;

  const categoryObjects = allCategories.filter(category => 
    userCategories.includes(category.id) || userCategories.includes(category.title)
  );
  
  const isCategorySelected = (categoryId: string) => {
    return pathname?.includes(`/browse/${categoryId}`) && !searchParams?.has('subcategory');
  };
  
  const isSubcategorySelected = (subcategoryId: string) => {
    return searchParams?.get('subcategory') === subcategoryId;
  };

  return (
    <SidebarGroup className="group-data-[collapsible=icon]:hidden">
      <SidebarGroupLabel>Your Preferences</SidebarGroupLabel>
      
      {!hasPreferences && (
        <div className="px-3 py-2">
          <p className="text-xs text-sidebar-foreground/70 mb-2">
            Customize your experience by setting your preferences.
          </p>
          <SidebarMenu>
            <SidebarMenuItem>
              <SidebarMenuButton asChild>
                <Link href="/settings/preferences" className="bg-sidebar-accent/50 hover:bg-sidebar-accent transition-colors">
                  <Plus className="h-4 w-4" />
                  <span>Set Preferences</span>
                </Link>
              </SidebarMenuButton>
            </SidebarMenuItem>
          </SidebarMenu>
        </div>
      )}
      
      {categoryObjects.length > 0 && (
        <>
          <div className="px-2 pt-2 pb-1">
            <h4 className="text-xs font-medium text-sidebar-foreground/60">Categories</h4>
          </div>
          <SidebarMenu>
            {categoryObjects.map((category) => (
              <SidebarMenuItem key={`category-${category.id}`}>
                <SidebarMenuButton asChild>
                  <Link 
                    href={`/browse/${category.id}`} 
                    className={`flex items-center gap-2 ${isCategorySelected(category.id) ? "bg-sidebar-accent" : ""}`}
                  >
                    <div className="flex-shrink-0 relative h-5 w-5 overflow-hidden rounded-sm">
                      <Image 
                        src={category.image} 
                        alt={category.title}
                        fill
                        className="object-cover"
                      />
                    </div>
                    <span>{category.title}</span>
                  </Link>
                </SidebarMenuButton>
              </SidebarMenuItem>
            ))}
          </SidebarMenu>
        </>
      )}
      
      {userSubcategories.length > 0 && (
        <>
          <div className="px-2 pt-2 pb-1">
            <h4 className="text-xs font-medium text-sidebar-foreground/60">Subcategories</h4>
          </div>
          <SidebarMenu>
            {userSubcategories.map((subcategoryId: string) => {
              let subcategory: Subcategory | undefined;
              let parentCategoryId = "";
              
              for (const [categoryId, subcategories] of Object.entries(allSubcategories)) {
                const found = subcategories.find(sub => sub.id === subcategoryId || sub.title === subcategoryId);
                if (found) {
                  subcategory = found;
                  parentCategoryId = categoryId;
                  break;
                }
              }

              if (!subcategory || !parentCategoryId) {
                return null;
              }

              return (
                <SidebarMenuItem key={`subcategory-${subcategoryId}`}>
                  <SidebarMenuButton asChild>
                    <Link 
                      href={`/browse/${parentCategoryId}/?subcategory=${encodeURIComponent(subcategory.id)}`}
                      className={isSubcategorySelected(subcategory.id) ? "bg-sidebar-accent" : ""}
                    >
                      {subcategory.image ? (
                        <div className="flex-shrink-0 relative h-5 w-5 overflow-hidden rounded-sm">
                          <Image 
                            src={subcategory.image} 
                            alt={subcategory.title}
                            fill
                            className="object-cover"
                          />
                        </div>
                      ): (
                        <IconBookmark className="h-4 w-4" />
                      )}  
                      <span>{subcategory.title}</span>
                    </Link>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              );
            })}
          </SidebarMenu>
        </>
      )}
    </SidebarGroup>
  );
} 