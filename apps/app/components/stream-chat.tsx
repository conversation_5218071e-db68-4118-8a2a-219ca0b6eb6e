import { FormEvent, useState, useEffect, useRef, useMemo } from "react";
import { useMutation, useQuery } from "convex/react";
import { api } from "@workspace/backend/convex/_generated/api";
import { Id } from "@workspace/backend/convex/_generated/dataModel";
import { toast } from "sonner";
import { Ban } from "lucide-react";
import { ScrollArea } from "@workspace/ui/components/scroll-area";
import { Avatar, AvatarFallback, AvatarImage } from "@workspace/ui/components/avatar";
import { Input } from "@workspace/ui/components/input";
import { Button } from "@workspace/ui/components/button";
import { Send } from "lucide-react";
import { UserAvatar } from "./user-avatar";
import { MentionsAutocomplete } from "./mentions-autocomplete";
import { Badge, CustomBadge } from "@workspace/ui/components/badge";
import { cn } from "@workspace/ui/lib/utils";

export function StreamChat({ streamId, isHost, isMod, streamDetails }: { streamId: Id<"streams">, isHost: boolean, isMod: boolean, streamDetails: any }) {
  const messagesQuery = useQuery(api.streams.listMessages, { streamId });
  const messages = useMemo(() => messagesQuery || [], [messagesQuery]);
  const sendMessageMutation = useMutation(api.streams.sendMessage);
  const [newMessageText, setNewMessageText] = useState("");
  const loggedInUser = useQuery(api.users.viewer);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const [caretPosition, setCaretPosition] = useState(0);
  const inputRef = useRef<HTMLInputElement>(null);

  const moderationData = useQuery(api.moderation.getStreamModerationData, streamId ? { streamId } : "skip");
  const blockUserMutation = useMutation(api.moderation.blockUserFromChat);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(scrollToBottom, [messages]);

  const handleSendMessage = async (event: FormEvent) => {
    event.preventDefault();
    if (!newMessageText.trim() || !loggedInUser) {
      setNewMessageText("");
      return;
    }
    try {
      await sendMessageMutation({ streamId, text: newMessageText.trim() });
      setNewMessageText("");
    } catch (error: any) {
      console.error("Failed to send message:", error);
      toast.error(`Failed to send message: ${error.data?.value || error.message}`);
    }
  };

  const handleBlockUser = async (userIdToBlock: Id<"users">) => {
    if (!moderationData || (!moderationData.isCurrentUserHost && !moderationData.isCurrentUserMod)) {
      toast.error("You do not have permission to block users.");
      return;
    }
    if (userIdToBlock === moderationData.hostId) {
        toast.error("Cannot block the host.");
        return;
    }
    if (moderationData.moderatorIds?.includes(userIdToBlock)) {
        toast.error("Cannot block a fellow moderator. Remove moderator status first if needed.");
        return;
    }

    try {
      await blockUserMutation({ streamId, userIdToBlock });
      toast.success("User blocked from chat.");
    } catch (error: any) {
      console.error("Failed to block user:", error);
      toast.error(`Failed to block user: ${error.data?.value || error.message}`);
    }
  };

  const insertMention = (username: string) => {
    if (!inputRef.current) return;
    const input = inputRef.current;
    const value = newMessageText;
    const start = input.selectionStart || 0;
    const end = input.selectionEnd || 0;

    const textUpToCaret = value.slice(0, start);
    const match = /(?:^|\s)@([\w]*)$/.exec(textUpToCaret);
    if (match) {
      const atIndex = textUpToCaret.lastIndexOf("@" + match[1]);
      const before = value.slice(0, atIndex);
      const after = value.slice(end);
      const mentionText = `@${username} `;
      const newValue = before + mentionText + after;
      setNewMessageText(newValue);

      setTimeout(() => {
        const pos = before.length + mentionText.length;
        input.setSelectionRange(pos, pos);
        input.focus();
        setCaretPosition(pos);
      }, 0);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setNewMessageText(e.target.value);
    setCaretPosition(e.target.selectionStart || 0);
  };

  const handleInputSelect = (e: React.SyntheticEvent<HTMLInputElement>) => {
    const input = e.target as HTMLInputElement;
    setCaretPosition(input.selectionStart || 0);
  };

  return (
    <div className="flex flex-col h-full bg-sidebar text-foreground rounded-lg shadow-md overflow-hidden">
      <ScrollArea className="flex-1 px-2">
        <div className="space-y-2 py-2">
          {messages.map((message) => {
            if (message.type === "system") {
              return (
                <div
                  key={message._id}
                  className="w-full flex justify-center my-2"
                >
                  <div className="bg-muted-foreground/10 text-muted-foreground text-xs px-3 py-1 rounded-full shadow-sm">
                    {message.text}
                  </div>
                </div>
              );
            }
            const isHostMessage = streamDetails.hostId === message.userId;
            const isModMessage = streamDetails.moderatorIds?.includes(message.userId);
            
            const canBlock = moderationData && 
              (moderationData.isCurrentUserHost || moderationData.isCurrentUserMod) &&
              message.userId !== moderationData.hostId && 
              !moderationData.moderatorIds?.includes(message.userId) &&
              message.userId !== loggedInUser?._id;
            return (
              <div key={message._id} className={cn(
                "flex items-start gap-1 group",
                isHostMessage && "border border-blue-600 bg-blue-600/15 p-2 rounded-lg",
                isModMessage && "border border-yellow-600 bg-yellow-600/15 p-2 rounded-lg"
              )}>
                <UserAvatar user={message.user} size="sm" />
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2">
                    <span className="text-sm font-medium" style={{ color: message.user?.color }}>{message.user?.username}</span>
                    {isHostMessage && (
                      <CustomBadge variant="host" circle="host" className="text-xs">
                        Host
                      </CustomBadge>
                    )}
                    {isModMessage && (
                      <CustomBadge variant="mod" className="text-xs">
                        Mod
                      </CustomBadge>
                    )}
                    <span className="text-xs text-muted-foreground">{new Date(message._creationTime).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}</span>
                    {canBlock && (
                      <Button 
                        onClick={() => handleBlockUser(message.userId)}
                        className="p-1 text-red-500 hover:text-red-400 opacity-0 group-hover:opacity-100 transition-opacity"
                        title="Block user from chat"
                        size="icon"
                        variant="outline"
                      >
                        <Ban size={14} />
                      </Button>
                    )}
                  </div>
                  <p className={cn(
                    "text-sm break-words py-1.5 rounded-lg max-w-[80%]",
                    message.userId === loggedInUser?._id ? "self-end text-white" : "self-start"
                  )}>{message.text}</p>
                </div>
              </div>
            );
          })}
          <div ref={messagesEndRef} />
        </div>
      </ScrollArea>
      {loggedInUser && (
        <form onSubmit={handleSendMessage} className="p-4 border-t border-gray-700 relative">
          {/* Mentions dropdown, full width, above input */}
          <MentionsAutocomplete
            value={newMessageText}
            caretPosition={caretPosition}
            onMentionSelect={(username) => insertMention(username)}
            onClose={() => {}}
            className="left-0 right-0 w-full bottom-16"
          />
          <div className="flex gap-2 items-end">
            <Input
              ref={inputRef}
              type="text"
              value={newMessageText}
              onChange={handleInputChange}
              onSelect={handleInputSelect}
              onClick={handleInputSelect}
              placeholder="Say something..."
              className="flex-1"
              disabled={!loggedInUser || moderationData?.blockedUserIds?.includes(loggedInUser._id)}
              autoComplete="off"
            />
            <Button
              type="submit"
              size="icon"
              disabled={!newMessageText.trim() || !loggedInUser || moderationData?.blockedUserIds?.includes(loggedInUser._id)}
            >
              <Send className="h-4 w-4" />
            </Button>
          </div>
          {moderationData?.blockedUserIds?.includes(loggedInUser!._id) && (
            <p className="text-xs text-red-400 mt-1 text-center">You are blocked from sending messages in this stream.</p>
          )}
        </form>
      )}
      {!loggedInUser && (
        <div className="p-4 text-center text-sm text-gray-400 border-t border-gray-700">
          Sign in to chat.
        </div>
      )}
    </div>
  );
}
