import React from "react";
import { usePreloadedQuery, useQuery } from "convex/react";
import { api } from "@workspace/backend/convex/_generated/api";
import { Badge } from "@workspace/ui/components/badge";
import { Id } from "@workspace/backend/convex/_generated/dataModel";
import { UserIcon, UsersIcon } from "lucide-react";
import { usePreloadedData } from "@/hooks/use-preloaded-data";

interface FriendStatusProps {
  userId: Id<"users">;
  showIcon?: boolean;
  className?: string;
}

export function FriendStatus({ userId, showIcon = true, className = "" }: FriendStatusProps) {
  const { preloadedUser } = usePreloadedData();
  const currentUser = usePreloadedQuery(preloadedUser);
  
  const isOwnProfile = currentUser?._id === userId;
  
  const areFriends = useQuery(
    api.users.areMutualFollowers,
    currentUser && !isOwnProfile ? { targetUserId: userId } : "skip"
  );
  
  if (isOwnProfile || !currentUser) {
    return null;
  }
  
  if (areFriends) {
    return (
      <Badge 
        variant="outline" 
        className={`bg-blue-50 text-blue-700 border-blue-200 ${className}`}
      >
        {showIcon && <UsersIcon className="w-3 h-3 mr-1" />}
        Friend
      </Badge>
    );
  }
  
  return null;
} 