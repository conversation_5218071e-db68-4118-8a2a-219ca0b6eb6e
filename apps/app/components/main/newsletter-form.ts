export function initNewsletterForm() {
  function submitHandler(event: SubmitEvent) {
    event.preventDefault();
    const container = (event.target as HTMLElement).parentNode as HTMLElement;
    const form = container.querySelector(".newsletter-form") as HTMLFormElement;
    const formInput = container.querySelector(
      ".newsletter-form-input",
    ) as HTMLInputElement;
    const success = container.querySelector(
      ".newsletter-success",
    ) as HTMLElement;
    const errorContainer = container.querySelector(
      ".newsletter-error",
    ) as HTMLElement;
    const errorMessage = container.querySelector(
      ".newsletter-error-message",
    ) as HTMLElement;
    const backButton = container.querySelector(
      ".newsletter-back-button",
    ) as HTMLButtonElement;
    const submitButton = container.querySelector(
      ".newsletter-form-button",
    ) as HTMLButtonElement;
    const loadingButton = container.querySelector(
      ".newsletter-loading-button",
    ) as HTMLButtonElement;

    const rateLimit = () => {
      errorContainer.style.display = "flex";
      errorMessage.innerText =
        "Too many signups, please try again in a little while";
      submitButton.style.display = "none";
      formInput.style.display = "none";
      backButton.style.display = "block";
    };

    const time = new Date();
    const timestamp = time.valueOf();
    const previousTimestamp = localStorage.getItem("loops-form-timestamp");

    if (previousTimestamp && Number(previousTimestamp) + 60000 > timestamp) {
      rateLimit();
      return;
    }
    localStorage.setItem("loops-form-timestamp", timestamp.toString());

    submitButton.style.display = "none";
    loadingButton.style.display = "flex";

    const formBody =
      "userGroup=&mailingLists=&email=" + encodeURIComponent(formInput.value);

    fetch(form.action, {
      method: "POST",
      body: formBody,
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
    })
      .then((res) => [res.ok, res.json(), res] as const)
      .then(([ok, dataPromise, res]) => {
        if (ok) {
          success.style.display = "flex";
          form.reset();
        } else {
          dataPromise.then((data) => {
            errorContainer.style.display = "flex";
            errorMessage.innerText = data.message
              ? data.message
              : res.statusText;
          });
        }
      })
      .catch((error) => {
        if (error.message === "Failed to fetch") {
          rateLimit();
          return;
        }
        errorContainer.style.display = "flex";
        if (error.message) errorMessage.innerText = error.message;
        localStorage.setItem("loops-form-timestamp", "");
      })
      .finally(() => {
        formInput.style.display = "none";
        loadingButton.style.display = "none";
        backButton.style.display = "block";
      });
  }

  function resetFormHandler(event: MouseEvent) {
    const container = (event.target as HTMLElement).parentNode as HTMLElement;
    const formInput = container.querySelector(
      ".newsletter-form-input",
    ) as HTMLInputElement;
    const success = container.querySelector(
      ".newsletter-success",
    ) as HTMLElement;
    const errorContainer = container.querySelector(
      ".newsletter-error",
    ) as HTMLElement;
    const errorMessage = container.querySelector(
      ".newsletter-error-message",
    ) as HTMLElement;
    const backButton = container.querySelector(
      ".newsletter-back-button",
    ) as HTMLButtonElement;
    const submitButton = container.querySelector(
      ".newsletter-form-button",
    ) as HTMLButtonElement;

    success.style.display = "none";
    errorContainer.style.display = "none";
    errorMessage.innerText = "Oops! Something went wrong, please try again";
    backButton.style.display = "none";
    formInput.style.display = "flex";
    submitButton.style.display = "flex";
  }

  const formContainers = document.getElementsByClassName(
    "newsletter-form-container",
  );

  for (let i = 0; i < formContainers.length; i++) {
    const formContainer = formContainers[i] as HTMLElement;
    if (!formContainer) continue;

    const handlersAdded = formContainer.classList.contains(
      "newsletter-handlers-added",
    );
    if (handlersAdded) continue;

    const form = formContainer.querySelector(".newsletter-form");
    const backButton = formContainer.querySelector(".newsletter-back-button");

    if (form) form.addEventListener("submit", submitHandler as EventListener);
    if (backButton)
      backButton.addEventListener("click", resetFormHandler as EventListener);

    formContainer.classList.add("newsletter-handlers-added");
  }
}
