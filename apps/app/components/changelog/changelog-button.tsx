"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@workspace/ui/components/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@workspace/ui/components/dialog";
import { IconVersions } from "@tabler/icons-react";
import { useQuery } from "convex/react";
import { api } from "@workspace/backend/convex/_generated/api";
import { cn } from "@workspace/ui/lib/utils";
import { useSidebar } from "@workspace/ui/components/sidebar";
import { Tooltip, TooltipContent, TooltipTrigger } from "@workspace/ui/components/tooltip";
import { Badge } from "@workspace/ui/components/badge";
import { ScrollArea } from "@workspace/ui/components/scroll-area";
import { Star, ArrowUpRight, Sparkles, Wrench, Bug } from "lucide-react";
import { Separator } from "@workspace/ui/components/separator";

export function ChangelogButton() {
  const { state, isMobile } = useSidebar();
  const [open, setOpen] = useState(false);
  const isCollapsed = state === "collapsed" && !isMobile;
  const [hasUnreadChanges, setHasUnreadChanges] = useState(false);
  const [, setLastViewedVersion] = useState<string | null>(null);
  
  const changelog = useQuery(api.changelog.getChangelog, { limit: 10 }) || [];
  const latestEntry = useQuery(api.changelog.getLatestChangelog);
  
  const latestVersion = latestEntry?.version || "";

  useEffect(() => {
    if (!latestVersion) return;
    
    const storedLastViewedVersion = localStorage.getItem('lastViewedChangelogVersion');
    setLastViewedVersion(storedLastViewedVersion);
    
    if (!storedLastViewedVersion || storedLastViewedVersion !== latestVersion) {
      setHasUnreadChanges(true);
    }
  }, [latestVersion]);

  const handleOpenChangelog = () => {
    setOpen(true);
    
    if (hasUnreadChanges && latestVersion) {
      setHasUnreadChanges(false);
      localStorage.setItem('lastViewedChangelogVersion', latestVersion);
    }
  };

  const NewChangesBadge = () => (
    hasUnreadChanges ? (
      <Badge variant="destructive" className="absolute -top-1 -right-1 h-2 w-2 p-0" />
    ) : null
  );

  const getChangeTypeIcon = (type: "feature" | "improvement" | "bugfix") => {
    switch (type) {
      case "feature":
        return <Sparkles className="h-4 w-4" />;
      case "improvement":
        return <Wrench className="h-4 w-4" />;
      case "bugfix":
        return <Bug className="h-4 w-4" />;
      default:
        return <Star className="h-4 w-4" />;
    }
  };

  const getChangeTypeColor = (type: "feature" | "improvement" | "bugfix") => {
    switch (type) {
      case "feature":
        return "bg-emerald-100 text-emerald-800 dark:bg-emerald-950/60 dark:text-emerald-300 border-emerald-200 dark:border-emerald-800";
      case "improvement":
        return "bg-blue-100 text-blue-800 dark:bg-blue-950/60 dark:text-blue-300 border-blue-200 dark:border-blue-800";
      case "bugfix":
        return "bg-amber-100 text-amber-800 dark:bg-amber-950/60 dark:text-amber-300 border-amber-200 dark:border-amber-800";
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300";
    }
  };

  const buttonContent = (
    <>
      <div className="relative">
        <IconVersions className="h-4 w-4" />
        <NewChangesBadge />
      </div>
      <span className={cn({ "hidden": isCollapsed })}>Changelog</span>
    </>
  );

  const isLoading = changelog === undefined;

  return (
    <>
      {isCollapsed ? (
        <Tooltip>
          <TooltipTrigger asChild>
            <Button 
              variant="ghost" 
              size="icon"
              className="w-full flex justify-center"
              onClick={handleOpenChangelog}
            >
              {buttonContent}
            </Button>
          </TooltipTrigger>
          <TooltipContent side="right">
            View Changelog
            {hasUnreadChanges && <span className="ml-1 text-xs text-red-500">(New!)</span>}
          </TooltipContent>
        </Tooltip>
      ) : (
        <Button 
          variant="ghost" 
          size="sm" 
          className="w-full justify-start px-2 gap-2"
          onClick={handleOpenChangelog}
        >
          {buttonContent}
        </Button>
      )}

      <Dialog open={open} onOpenChange={setOpen}>
        <DialogContent
          className="fixed md:!left-65 md:!bottom-1 sm:bottom-0 sm:left-0 top-0 sm:right-4 sm:bottom-4 sm:left-auto sm:top-auto w-full sm:w-[550px] max-w-[100vw] p-0 overflow-hidden rounded-none sm:rounded-xl border border-border/80 sm:border shadow-none sm:shadow-xl bg-background z-50"
          style={{ margin: 0 }}
          overlay={false}
          placement="custom"
        >
          <div className="absolute top-0 left-0 right-0 h-1.5 bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500"></div>
          <DialogHeader className="px-6 pt-6 pb-2">
            <DialogTitle className="text-xl flex items-center gap-2 font-semibold">
              <IconVersions className="h-5 w-5 text-primary" />
              Changelog
            </DialogTitle>
            <DialogDescription className="text-muted-foreground">
              Latest updates and improvements to Liveciety
            </DialogDescription>
          </DialogHeader>
          
          <ScrollArea className="mt-4 max-h-[70vh] px-6">
            {isLoading ? (
              <div className="flex justify-center items-center py-8">
                <div className="animate-pulse text-muted-foreground">Loading changelog...</div>
              </div>
            ) : changelog.length === 0 ? (
              <div className="py-8 text-center text-muted-foreground">
                No changelog entries available yet.
              </div>
            ) : (
              <div className="space-y-10 pr-4 pb-4">
                {changelog.map((entry, entryIndex) => (
                  <div key={entry._id} className="relative">
                    {/* Timeline connector */}
                    {entryIndex < changelog.length - 1 && (
                      <div className="absolute left-[19px] top-[40px] bottom-[-40px] w-0.5 bg-gradient-to-b from-primary/50 to-transparent"></div>
                    )}
                    
                    {/* Version header */}
                    <div className="flex items-center gap-3 mb-4">
                      <div className="flex-shrink-0 h-10 w-10 rounded-full bg-gradient-to-br dark:from-zinc-800 dark:to-zinc-900 from-zinc-200 to-zinc-300 flex items-center justify-center shadow-md">
                        <IconVersions className="h-5 w-5 dark:text-zinc-200 text-zinc-800" />
                      </div>
                      <div>
                        <h3 className="text-lg font-semibold tracking-tight">Version {entry.version}</h3>
                        <p className="text-xs text-muted-foreground">
                          {new Date(entry.date).toLocaleDateString('en-US', {
                            month: 'short',
                            day: 'numeric',
                            year: 'numeric'
                          })}
                        </p>
                      </div>
                    </div>
                    
                    {/* Summary */}
                    {entry.summary && (
                      <div className="ml-[52px] mb-4">
                        <p className="text-sm text-foreground/80 italic border-l-2 border-primary/20 pl-3 py-1">
                          {entry.summary}
                        </p>
                      </div>
                    )}
                    
                    {/* Changes */}
                    <div className="ml-[52px] space-y-3">
                      {entry.changes.map((change, index) => (
                        <div 
                          key={index} 
                          className="group p-3 rounded-lg border border-border/50 bg-muted/30 hover:bg-muted/50 transition-colors"
                        >
                          <div className="flex items-center gap-2 mb-1.5">
                            <Badge 
                              variant="outline" 
                              className={cn(
                                "font-medium text-xs px-2 py-0.5 flex items-center gap-1.5 transition-colors", 
                                getChangeTypeColor(change.type)
                              )}
                            >
                              {getChangeTypeIcon(change.type)}
                              {change.type === "feature" ? "New" : 
                               change.type === "improvement" ? "Improved" : "Fixed"}
                            </Badge>
                          </div>
                          <p className="text-sm text-foreground/90 leading-relaxed">{change.description}</p>
                        </div>
                      ))}
                    </div>
                    
                    {entryIndex < changelog.length - 1 && (
                      <div className="mt-8 mb-2 ml-[52px]">
                        <Separator className="opacity-30" />
                      </div>
                    )}
                  </div>
                ))}
              </div>
            )}
          </ScrollArea>

          <DialogFooter className="px-6 py-4 border-t bg-muted/10 flex items-center justify-between mt-0">
            <div className="flex items-center gap-2">
              <span className="text-xs text-muted-foreground">
                Last updated: {new Date(latestEntry?.date || "").toLocaleDateString('en-US', {
                  month: 'short',
                  day: 'numeric',
                  year: 'numeric'
                })}
              </span>
              <Badge variant="secondary" className="text-xs">v{latestEntry?.version}</Badge>
            </div>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
} 