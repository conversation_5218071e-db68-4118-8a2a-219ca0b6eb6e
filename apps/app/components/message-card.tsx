import { User } from "@/lib/types";
import { UserAvatar } from "./user-avatar";
import { Id } from "@workspace/backend/convex/_generated/dataModel";

interface MessageCardProps {
  avatarUrl?: string;
  senderName: string;
  message: string;
  timestamp: string;
  isNew?: boolean;
  initials: string;
  userId?: Id<"users">;
  user: User;
  unreadCount?: number;
}

export const MessageCard = ({
  avatarUrl,
  senderName,
  message,
  timestamp,
  isNew = false,
  initials,
  userId,
  user,
  unreadCount = 0,
}: MessageCardProps) => {

  return (
    <div className="flex gap-3 p-3 rounded-lg border border-input hover:bg-accent/30 cursor-pointer">
      <UserAvatar 
        user={user} 
        size="default"
        showOnlineIndicator={true}
      />
      <div className="flex w-full flex-col gap-1">
        <div className="flex justify-between items-start">
          <div>
            <p className="font-semibold line-clamp-1 flex items-center gap-2">
              {senderName}
              {unreadCount > 0 && (
                <span className="inline-flex items-center px-2 py-0.5 rounded-md text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400 ml-2">
                  {unreadCount > 9 ? '9+' : unreadCount}
                </span>
              )}
            </p>
            <p className="text-sm text-muted-foreground line-clamp-2">
              {message}
            </p>
          </div>
          <span className="text-xs text-muted-foreground whitespace-nowrap">
            {timestamp}
          </span>
        </div>
        {isNew && (
          <div className="flex items-center gap-2">
            <div className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400">
              New
            </div>
          </div>
        )}
      </div>
    </div>
  );
};
