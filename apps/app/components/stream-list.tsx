"use client";

import {
  RegularStreamList,
} from "./stream-sections";
import { useQuery } from "convex/react";
import { api } from "@workspace/backend/convex/_generated/api";
import { useRouter } from "next/navigation";

export const StreamList = ({ showMockup = false }: { showMockup?: boolean }) => {
  const router = useRouter();
  const streams = useQuery(api.streams.listActiveStreams, {}); 

  const handleStreamClick = (streamId: string) => {
    router.push(`/stream/${streamId}`);
  };

  return (
    <div className="px-4">
      <RegularStreamList streams={streams?.map(stream => ({
          ...stream,
          onClick: () => handleStreamClick(stream._id),
        })) ?? []} />
    </div>
  );
};
