import { Card, CardContent } from "@workspace/ui/components/card";
import { StreamWithDetails } from "@workspace/backend/convex/streams";
import { Clock, Radio } from "lucide-react";
import { Badge } from "@workspace/ui/components/badge";
import Image from "next/image";
import { getAvatarImageUrl, getCategoryTitle, formatScheduledTime } from "@/lib/utils";
import { StreamCardViewerCount } from "@/components/viewer-count";
import Link from "next/link";
import { UserAvatar } from "./user-avatar";

interface StreamCardProps {
  stream: StreamWithDetails;
  onClick?: () => void;
}

export function StreamCard({ stream, onClick }: StreamCardProps) {
  return (
    <Card className="bg-transparent border-none cursor-pointer group w-64">
      <CardContent className="p-0">
        <div onClick={onClick} className="relative mb-2 border-2 border-gray-800 rounded-lg overflow-hidden">
          <Image
            src={getAvatarImageUrl(stream.thumbnail) || "/placeholder.svg"}
            alt={stream.title}
            className="w-full h-80 object-contain rounded-lg"
            width={320}
            height={192}
          />
          <div className="absolute top-2 left-2">
            {stream.status === "live" ? (
            <Badge variant="destructive" className="!bg-red-600 hover:!bg-red-700 text-white font-bold px-2 py-1 border-1 border-red-400">
              <Radio className="w-3 h-3 mr-1 animate-pulse" />
              LIVE
            </Badge>
            ) : (
              <Badge variant="secondary" className="!bg-black/80 text-white text-xs px-2 py-1">
                <Clock className="w-3 h-3 mr-1" />
                {formatScheduledTime(stream.scheduledTime as unknown as string)}
              </Badge>
            )}
          </div>
          <div className="absolute bottom-2 left-2">
            <StreamCardViewerCount
              streamId={stream._id}
              isLive={stream.status === "live"}
            />
          </div>
          <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors rounded-lg" />
        </div>

        <div className="flex gap-2">
          <div className="flex-1 min-w-0">
            <Link href={`/stream/${stream._id}`}>
              <h3 className="font-semibold text-white text-sm line-clamp-2 mb-1 group-hover:text-blue-400 transition-colors">
                {stream.title}
              </h3>
            </Link>
            <Link href={`/user/${stream.streamer?.username}`} className="flex items-center gap-1 mb-1 hover:text-blue-400 transition-colors w-fit gap-2">
            <UserAvatar user={stream.streamer} size="xs" />
              <span className="text-gray-400 text-sm">{stream.streamer?.username}</span>
              {stream.streamer?.sellerProfile?.verified && (
                <div className="w-4 h-4 bg-blue-600 rounded-full flex items-center justify-center">
                  <div className="w-2 h-2 bg-white rounded-full" />
                </div>
              )}
            </Link>
            <Link 
              href={`/browse/${stream.category}`}
              className="text-gray-400 text-xs mb-2 hover:text-blue-400 transition-colors"
            >
              {getCategoryTitle(stream.category || "")}
            </Link>
            <div className="text-[10px] text-gray-300">
              {stream.tags?.join(", ")}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}