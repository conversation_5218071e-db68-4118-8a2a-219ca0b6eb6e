"use client";

import { Doc } from "@workspace/backend/convex/_generated/dataModel";
import StreamView from "./stream-view";

interface StreamDetailContentProps {
  stream: Doc<"streams">;
  user: Doc<"users"> | undefined;
  onClose?: () => void;
}

const StreamDetailContent = ({ stream, user, onClose }: StreamDetailContentProps) => {
  if (!user) {
    return (
      <div className="flex flex-col md:flex-row w-full h-screen bg-black text-white">
        <div className="flex-1 flex flex-col items-center justify-center">
          <div className="text-white">Loading data...</div>
        </div>
      </div>
    );
  }

  const handleStreamEnded = () => {
    console.log("Stream ended");
    if (onClose) {
      onClose();
    }
  };

  return (
    <div className="flex flex-col md:flex-row w-full h-screen bg-black text-white">
      <div className="flex-1 flex flex-col">
        <StreamView 
          streamId={stream._id}
          loggedInUser={user}
          onStreamEnded={handleStreamEnded}
        />
      </div>
    </div>
  );
};

export default StreamDetailContent;
