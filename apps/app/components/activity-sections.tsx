import React, { useState } from "react";
import useEmblaCarousel from "embla-carousel-react";
import { Card, CardContent } from "@workspace/ui/components/card";
import { Button } from "@workspace/ui/components/button";
import {
  IconChevronLeft,
  IconChevronRight,
  IconBookmark,
  IconBookmarkFilled,
} from "@tabler/icons-react";
import Image from "next/image";
import { useQuery, useMutation } from "convex/react";
import { api } from "@workspace/backend/convex/_generated/api";
import { UserAvatar } from "./user-avatar";

interface ShowItemProps {
  image?: string;
  title: string;
  subtitle?: string;
  date?: string;
  tag?: string;
  tagColor?: string;
  user: {
    name: string;
    avatar?: string;
    initials: string;
  };
}

const ShowItem = ({
  image,
  title,
  subtitle,
  date,
  tag,
  tagColor,
  user,
}: ShowItemProps) => {
  return (
    <Card className="h-full">
      <CardContent className="p-4 space-y-3">
        {image ? (
          <div className="relative aspect-[4/3] rounded-lg overflow-hidden bg-muted">
            <Image src={image} alt={title} fill className="object-cover" />
          </div>
        ) : (
          <div className="aspect-[4/3] rounded-lg bg-muted" />
        )}
        <div className="space-y-1.5">
          <h3 className="font-semibold line-clamp-1">{title}</h3>
          {subtitle && (
            <p className="text-sm text-muted-foreground line-clamp-2 text-sky-600">
              {subtitle}
            </p>
          )}
          {date && <p className="text-sm text-muted-foreground">{date}</p>}
        </div>
        <div className="flex items-center justify-between pt-2 border-t">
          <div className="flex items-center gap-2">
            <UserAvatar
              user={user}
              size="lg"
            />
            <span className="text-sm font-medium">{user.name}</span>
          </div>
          {tag && (
            <div
              className={`inline-flex items-center px-2 py-0.5 rounded-md text-xs font-semibold ${tagColor || "bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400"}`}
            >
              {tag}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

interface ProductItemProps {
  image?: string;
  title: string;
  price?: string;
  user: {
    name: string;
    avatar?: string;
    initials: string;
  };
  streamId?: string;
  bookmarked?: boolean;
  onToggleBookmark?: () => void;
}

const ProductItem = ({ 
  image, 
  title, 
  price, 
  user, 
  streamId,
  bookmarked = false,
  onToggleBookmark 
}: ProductItemProps) => {
  return (
    <Card className="h-full">
      <CardContent className="p-4 space-y-3">
        <div className="relative">
          {image ? (
            <div className="relative aspect-[4/3] rounded-lg overflow-hidden bg-muted">
              <Image src={image} alt={title} fill className="object-cover" />
            </div>
          ) : (
            <div className="aspect-[4/3] rounded-lg bg-muted" />
          )}
          <Button
            variant="ghost"
            size="icon"
            className={`absolute top-2 right-2 h-8 w-8 rounded-full`}
            onClick={onToggleBookmark}
            disabled={!streamId}
          >
            {bookmarked ? <IconBookmarkFilled className="h-4 w-4" /> : <IconBookmark className="h-4 w-4" />}
          </Button>
        </div>
        <div className="space-y-1.5">
          <h3 className="font-semibold line-clamp-1">{title}</h3>
          {price && <p className="font-semibold">{price}</p>}
        </div>
        <div className="flex items-center gap-2 pt-2 border-t">
          <UserAvatar
            user={user}
            height={6}
            width={6}
          />
          <span className="text-sm font-medium">{user.name}</span>
        </div>
      </CardContent>
    </Card>
  );
};

interface ActivitySectionProps {
  title: string;
  items: Array<{
    id: string;
    image?: string;
    title: string;
    subtitle?: string;
    price?: string;
    date?: string;
    tag?: string;
    tagColor?: string;
    user: {
      name: string;
      avatar?: string;
      initials: string;
    };
    streamId?: string;
    bookmarked?: boolean;
    onToggleBookmark?: () => void;
  }>;
  type: "show" | "product" | "search";
}

const ActivitySection = ({ title, items, type }: ActivitySectionProps) => {
  const [emblaRef, emblaApi] = useEmblaCarousel({
    dragFree: true,
    containScroll: "trimSnaps",
  });

  const scrollPrev = React.useCallback(() => {
    if (emblaApi) emblaApi.scrollPrev();
  }, [emblaApi]);

  const scrollNext = React.useCallback(() => {
    if (emblaApi) emblaApi.scrollNext();
  }, [emblaApi]);

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-semibold">{title}</h2>
        <div className="flex gap-2">
          <Button
            variant="outline"
            size="icon"
            onClick={scrollPrev}
            className="size-8"
          >
            <IconChevronLeft className="size-4" />
          </Button>
          <Button
            variant="outline"
            size="icon"
            onClick={scrollNext}
            className="size-8"
          >
            <IconChevronRight className="size-4" />
          </Button>
        </div>
      </div>

      <div className="overflow-hidden" ref={emblaRef}>
        <div className="flex gap-4">
          {items.map((item) => (
            <div key={item.id} className="flex-[0_0_280px] min-w-0">
              {type === "show" && (
                <ShowItem
                  image={item.image}
                  title={item.title}
                  subtitle={item.subtitle}
                  date={item.date}
                  tag={item.tag}
                  tagColor={item.tagColor}
                  user={item.user}
                />
              )}
              {type === "product" && (
                <ProductItem
                  image={item.image}
                  title={item.title}
                  price={item.price}
                  user={item.user}
                  streamId={item.streamId}
                  bookmarked={item.bookmarked}
                  onToggleBookmark={item.onToggleBookmark}
                />
              )}
              {type === "search" && (
                <Card className="h-full">
                  <CardContent className="p-4 space-y-3">
                    <h3 className="font-semibold line-clamp-1">{item.title}</h3>
                    {item.subtitle && (
                      <p className="text-sm text-muted-foreground line-clamp-2">
                        {item.subtitle}
                      </p>
                    )}
                  </CardContent>
                </Card>
              )}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export const ActivitySections = () => {
  const bookmarkedStreams = useQuery(api.bookmarks.getBookmarkedStreams) || [];
  const toggleBookmark = useMutation(api.bookmarks.toggleBookmark);

  const handleToggleBookmark = async (streamId: string) => {
    await toggleBookmark({ streamId: streamId as any });
  };

  const bookmarkedItems = bookmarkedStreams.map((stream: any) => ({
    id: stream._id,
    image: stream.thumbnail || null,
    title: stream.title || "Untitled Stream",
    price: stream.price ? `$${stream.price}` : undefined,
    subtitle: stream.description,
    streamId: stream._id,
    bookmarked: true,
    onToggleBookmark: () => handleToggleBookmark(stream._id),
    user: {
      name: stream.streamer?.username || "Unknown",
      avatar: stream.streamer?.profileImage,
      initials: stream.streamer?.username?.substring(0, 2)?.toUpperCase() || "UN",
    },
  }));

  return (
    <div className="space-y-8">
      {bookmarkedItems.length > 0 && (
        <ActivitySection 
          title="Bookmarked Items" 
          items={bookmarkedItems} 
          type="product" 
        />
      )}
      
      {bookmarkedItems.length === 0 && (
        <div className="text-center py-10 text-muted-foreground">
          <p>No bookmarked items yet.</p>
          <p className="text-sm mt-2">Browse streams and click the bookmark icon to save them here.</p>
        </div>
      )}
    </div>
  );
};
