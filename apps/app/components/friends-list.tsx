import { <PERSON><PERSON> } from "@workspace/ui/components/button";
import { IconMessage } from "@tabler/icons-react";
import { UserAvatar } from "./user-avatar";
import { useQuery, useMutation, usePreloadedQuery } from "convex/react";
import { api } from "@workspace/backend/convex/_generated/api";
import { useEffect, useState, useRef, useCallback } from "react";
import { User } from "@/lib/types";
import { Id } from "@workspace/backend/convex/_generated/dataModel";
import { toast } from "sonner";
import { usePreloadedData } from "@/hooks/use-preloaded-data";
import { UserRole } from "@workspace/backend/convex/lib/types";
import { Input } from "@workspace/ui/components/input";

interface FriendsListProps {
  onBack: () => void;
  onMessageUser: (user: {
    name: string;
    avatar?: string;
    initials: string;
    id?: Id<"chats">;
  }) => void;
}

interface FriendUser {
  _id: string;
  username: string;
  name: string;
  avatarUrl?: string;
  status?: string;
  isOnline?: boolean;
  activity?: string;
  color?: string;
}

interface ChatType {
  _id: Id<"chats">;
  isGroup: boolean;
  participants: Array<{
    _id: Id<"users">;
    username?: string;
    name?: string;
    image?: string;
    imageUrl?: string;
  }>;
  otherUser?: {
    _id: Id<"users">;
    name?: string;
    username?: string;
    image?: string;
    imageUrl?: string;
  } | null;
}

export const FriendsList = ({ onBack, onMessageUser }: FriendsListProps) => {
  const [onlineFriends, setOnlineFriends] = useState<FriendUser[]>([]);
  const [offlineFriends, setOfflineFriends] = useState<FriendUser[]>([]);
  const [search, setSearch] = useState("");
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const observer = useRef<IntersectionObserver | null>(null);
  const loadMoreRef = useRef<HTMLDivElement | null>(null);
  const createChat = useMutation(api.chat.createChat);
  
  const { preloadedUser } = usePreloadedData();
  const user = usePreloadedQuery(preloadedUser);
  
  const chats = useQuery(api.chat.listChats, {});
  
  const mutualFollowers = useQuery(api.users.getMutualFollowers, 
    user?._id ? { userId: user._id } : "skip"
  );
  
  useEffect(() => {
    if (mutualFollowers && Array.isArray(mutualFollowers)) {
      const online: FriendUser[] = [];
      const offline: FriendUser[] = [];
      
      mutualFollowers.forEach(friend => {
        const friendUser: FriendUser = {
          _id: friend._id,
          username: friend.username || "",
          name: friend.name || friend.username || "",
          avatarUrl: friend.avatarUrl || undefined,
          status: friend.status,
          color: friend.color,
        };
        
        if (friend.status === "online") {
          online.push({
            ...friendUser,
            isOnline: true,
          });
        } else {
          offline.push({
            ...friendUser,
            isOnline: false,
          });
        }
      });
      
      setOnlineFriends(online);
      setOfflineFriends(offline);
    }
  }, [mutualFollowers]);

  const getInitials = (name: string) => {
    return name.charAt(0).toUpperCase();
  };

  const createUserFromFriend = (friend: FriendUser): User => {
    return {
      _id: friend._id,
      username: friend.username,
      name: friend.name,
      color: friend.color || "",
      image: friend.avatarUrl || "",
      avatarUrl: friend.avatarUrl,
      status: friend.status as "online" | "offline",
      _creationTime: 0,
      email: "",
      firstName: "",
      lastName: "",
      role: "user" as UserRole,
      hashedPassword: false,
      accounts: [],
      lastLoginType: "",
      subscription: null,
      followers: [],
      following: [],
    };
  };

  const handleMessageUser = async (friend: FriendUser) => {
    if (!user) return;
    
    let existingChat: ChatType | undefined;
    
    if (chats && Array.isArray(chats)) {
      existingChat = chats.find((chat: ChatType) => 
        !chat.isGroup && 
        chat.participants.some((p) => p._id === friend._id as Id<"users">)
      );
    }
    
    const friendInitials = getInitials(friend.name);
    
    try {
      let chatId: Id<"chats">;
      
      if (existingChat) {
        chatId = existingChat._id;
      } else {
        chatId = await createChat({
          participantIds: [friend._id as Id<"users">],
          isGroup: false
        });
      }
      
      onMessageUser({
        id: chatId,
        name: friend.name,
        avatar: friend.avatarUrl,
        initials: friendInitials,
      });
      
    } catch (error) {
      console.error("Failed to handle messaging:", error);
      toast.error("Failed to open chat");
    }
  };

  const allFriends = [
    ...onlineFriends.map(f => ({ ...f, isOnline: true })),
    ...offlineFriends.map(f => ({ ...f, isOnline: false })),
  ];
  const filteredFriends = search
    ? allFriends.filter(
        (friend) =>
          friend.username.toLowerCase().includes(search.toLowerCase()) ||
          friend.name.toLowerCase().includes(search.toLowerCase())
      )
    : allFriends;
    
  const lastFriendRef = useCallback(
    (node: HTMLDivElement | null) => {
      if (!hasMore) return;
      if (observer.current) observer.current.disconnect();
      observer.current = new IntersectionObserver((entries) => {
        if (entries[0]?.isIntersecting) {
          setPage((prev) => prev + 1);
        }
      });
      if (node) observer.current.observe(node);
    },
    [hasMore]
  );

  useEffect(() => {
    if (page > 1) setHasMore(false);
  }, [page]);

  return (
    <div className="flex flex-col h-full overflow-y-auto">
      <div className="sticky top-0 z-10 bg-background p-4 pb-2">
        <Input
          placeholder="Search friends..."
          value={search}
          onChange={(e) => setSearch(e.target.value)}
          className="mb-4"
        />
      </div>
      <div className="flex-1 px-4 pt-0 overflow-y-auto">
        {filteredFriends.length === 0 && (
          <p className="text-sm text-muted-foreground mt-8">No friends found</p>
        )}
        {filteredFriends.map((friend, idx) => {
          const isLast = idx === filteredFriends.length - 1;
          return (
            <div
              key={friend._id}
              ref={isLast ? lastFriendRef : undefined}
              className="flex items-center justify-between p-2 rounded-lg hover:bg-accent/50 mb-2"
            >
              <div className="flex items-center gap-3">
                <div className="relative">
                  <UserAvatar user={createUserFromFriend(friend)} />
                  {friend.isOnline && (
                    <span className="absolute bottom-0 right-0 w-3 h-3 bg-green-500 rounded-full border-2 border-background" />
                  )}
                </div>
                <div>
                  <p className="font-medium">{friend.username}</p>
                  {friend.activity && (
                    <p className="text-sm text-muted-foreground">{friend.activity}</p>
                  )}
                </div>
              </div>
              {friend.activity?.startsWith("watching") ? (
                <Button variant="default" size="xs">Join</Button>
              ) : (
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => handleMessageUser(friend)}
                >
                  <IconMessage className="h-5 w-5" />
                </Button>
              )}
            </div>
          );
        })}
        {hasMore && (
          <div ref={loadMoreRef} className="py-4 text-center text-muted-foreground text-xs">
            Loading more friends...
          </div>
        )}
      </div>
    </div>
  );
};
