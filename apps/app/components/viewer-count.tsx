import { Eye, Users, Radio } from "lucide-react";
import { Badge } from "@workspace/ui/components/badge";
import { useViewerCount, formatViewerCount } from "@/hooks/use-viewer-count";
import { Id } from "@workspace/backend/convex/_generated/dataModel";
import { cn } from "@workspace/ui/lib/utils";

interface ViewerCountProps {
  streamId: Id<"streams">;
  isLive: boolean;
  variant?: "badge" | "text" | "compact";
  showIcon?: boolean;
  showLabel?: boolean;
  className?: string;
  updateInterval?: number;
  size?: "sm" | "md" | "lg";
}

export function ViewerCount({
  streamId,
  isLive,
  variant = "badge",
  showIcon = true,
  showLabel = true,
  className,
  updateInterval = 30000,
  size = "md",
}: ViewerCountProps) {
  const { viewerCount, isLoading, error } = useViewerCount({
    streamId,
    isLive,
    updateInterval,
  });

  const displayCount = formatViewerCount(viewerCount);
  const isLoadingOrError = isLoading || error;

  // Icon size based on size prop
  const iconSize = {
    sm: 12,
    md: 16,
    lg: 20,
  }[size];

  // Text size classes
  const textSizeClass = {
    sm: "text-xs",
    md: "text-sm",
    lg: "text-base",
  }[size];

  if (variant === "badge") {
    return (
      <Badge
        className={cn(
          "!bg-black/80 text-white font-medium px-2 py-1",
          textSizeClass,
          className
        )}
      >
        {showIcon && <Eye className={cn("mr-1", `w-${iconSize/4} h-${iconSize/4}`)} />}
        {isLoadingOrError ? "..." : displayCount}
        {showLabel && " viewers"}
      </Badge>
    );
  }

  if (variant === "compact") {
    return (
      <div className={cn("flex items-center gap-1 text-white", textSizeClass, className)}>
        {showIcon && <Users size={iconSize} className="text-gray-400" />}
        <span className="font-medium">
          {isLoadingOrError ? "..." : displayCount}
        </span>
      </div>
    );
  }

  // Default text variant
  return (
    <div className={cn("flex items-center gap-1 text-white", textSizeClass, className)}>
      {showIcon && <Eye size={iconSize} className="text-gray-400" />}
      <span className="font-medium">
        {isLoadingOrError ? "..." : displayCount}
        {showLabel && " viewers"}
      </span>
      {isLive && (
        <Radio size={iconSize - 2} className="text-red-500 animate-pulse ml-1" />
      )}
    </div>
  );
}

// Specialized component for live stream indicators
export function LiveViewerCount({
  streamId,
  className,
  size = "md",
}: {
  streamId: Id<"streams">;
  className?: string;
  size?: "sm" | "md" | "lg";
}) {
  return (
    <ViewerCount
      streamId={streamId}
      isLive={true}
      variant="compact"
      showIcon={true}
      showLabel={false}
      className={className}
      size={size}
      updateInterval={15000} // More frequent updates for live streams
    />
  );
}

// Component for stream cards
export function StreamCardViewerCount({
  streamId,
  isLive,
  className,
}: {
  streamId: Id<"streams">;
  isLive: boolean;
  className?: string;
}) {
  return (
    <ViewerCount
      streamId={streamId}
      isLive={isLive}
      variant="badge"
      showIcon={true}
      showLabel={true}
      className={className}
      size="sm"
    />
  );
} 