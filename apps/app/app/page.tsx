"use client";

import React from "react";
import { Authenticated, Unauthenticated, AuthLoading } from "convex/react";
import UnauthPage from "@/components/unauth-page";
import { StreamList } from "@/components/stream-list";

export default function AuthPage() {
  return (
    <>
      <Authenticated>
        <StreamList />
      </Authenticated>
      <Unauthenticated>
        <UnauthPage />
      </Unauthenticated>
      <AuthLoading>
        <div className="flex min-h-screen items-center justify-center">
          <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-indigo-500"></div>
        </div>
      </AuthLoading>
    </>
  );
}
