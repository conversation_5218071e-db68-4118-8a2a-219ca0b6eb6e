"use client";

import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useMutation, usePreloadedQuery, useQuery } from "convex/react";
import { api } from "@workspace/backend/convex/_generated/api";
import { <PERSON><PERSON> } from "@workspace/ui/components/button";
import { 
  categories, 
  subcategories, 
  Category, 
  Subcategory 
} from "@workspace/lib/constants/categories";
import { ChevronLeft } from "lucide-react";
import { Skeleton } from "@workspace/ui/components/skeleton";
import { StepIndicator } from "../components/step-indicator";
import { usePreloadedData } from "@/hooks/use-preloaded-data";
import { Id } from "@workspace/backend/convex/_generated/dataModel";

export default function SubcategoriesPage() {
  const router = useRouter();
  const { preloadedUser } = usePreloadedData();
  const user = usePreloadedQuery(preloadedUser);
  const updateUser = useMutation(api.users.update);
  const [selectedSubcategories, setSelectedSubcategories] = useState<Set<string>>(
    new Set(user?.preferences?.subcategories || [])
  );
  const [isLoading, setIsLoading] = useState(false);

  const categoryIds = user?.preferences?.categories || [];

  const groupedSubcategories = categoryIds.reduce(
    (
      acc: Record<string, { category: Category; subcats: Subcategory[] }>,
      id: string,
    ) => {
      const category = categories.find((c) => c.id === id);
      if (category) {
        acc[id] = {
          category,
          subcats: subcategories[id] || [],
        };
      }
      return acc;
    },
    {},
  );

  const toggleSubcategory = (subcategoryId: string) => {
    setSelectedSubcategories((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(subcategoryId)) {
        newSet.delete(subcategoryId);
      } else {
        newSet.add(subcategoryId);
      }
      return newSet;
    });
  };

  const handleBack = () => {
    router.back();
  };

  const handleFinish = async () => {
    if (selectedSubcategories.size === 0) return;
    if (!user || !user._id) {
      console.error("User not found or missing ID");
      return;
    }

    setIsLoading(true);
    try {
      await updateUser({
        id: user._id as Id<"users">,
        preferences: {
          ...user.preferences,
          subcategories: Array.from(selectedSubcategories),
        },
        finishedSignUp: true,
      });
      router.push("/");
    } catch (err) {
      console.error(err);
    } finally {
      setIsLoading(false);
    }
  };

  if (!user) {
    return (
      <div className="space-y-6">
        <StepIndicator />
        <div className="flex items-center">
          <Button variant="ghost" size="sm" className="mr-2">
            <ChevronLeft className="h-4 w-4" />
          </Button>
          <div>
            <h2 className="text-xl font-semibold">Building your experience</h2>
            <p className="text-sm text-muted-foreground">
              Select your specific interests
            </p>
          </div>
        </div>
        <div className="space-y-6 max-h-[50vh] overflow-y-auto pr-2">
          {Array.from({ length: 2 }).map((_, categoryIndex) => (
            <div key={categoryIndex} className="space-y-3">
              <Skeleton className="h-4 w-32" />
              <div className="flex flex-wrap gap-2">
                {Array.from({ length: 6 }).map((_, i) => (
                  <Skeleton key={i} className="h-8 w-24 rounded-full" />
                ))}
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <StepIndicator />
      <div className="flex items-center">
        <Button 
          variant="ghost" 
          size="sm" 
          className="mr-2 -ml-2" 
          onClick={handleBack}
        >
          <ChevronLeft className="h-4 w-4" />
        </Button>
        <div>
          <h2 className="text-xl font-semibold">Build your ideal experience</h2>
          <p className="text-sm text-muted-foreground">
            Pick specific items you&apos;d like to see
          </p>
        </div>
      </div>

      <div className="space-y-6 max-h-[50vh] overflow-y-auto pr-2">
        {Object.entries(groupedSubcategories).map(
          ([categoryId, { category, subcats }]) => (
            <div key={categoryId} className="space-y-3">
              <h3 className="font-medium text-sm flex items-center">
                <span className="w-2 h-2 rounded-full bg-primary mr-2"></span>
                {category.title}
              </h3>
              <div className="flex flex-wrap gap-2">
                {subcats.map((item: Subcategory) => (
                  <Button
                    key={item.id}
                    variant={selectedSubcategories.has(item.id) ? "default" : "outline"}
                    size="sm"
                    onClick={() => toggleSubcategory(item.id)}
                    className={`rounded-full ${selectedSubcategories.has(item.id) ? 
                      "shadow-sm" : 
                      "text-muted-foreground hover:text-foreground"}`}
                  >
                    {item.title}
                  </Button>
                ))}
              </div>
            </div>
          ),
        )}
      </div>

      <div className="pt-4 border-t space-y-4">
        <Button
          className="w-full"
          onClick={handleFinish}
          disabled={!user || selectedSubcategories.size === 0 || isLoading}
        >
          {isLoading ? "Saving..." : "Complete Setup"}
        </Button>
        <p className="text-xs text-center text-muted-foreground">
          These selections help us personalize your feed and recommendations
        </p>
      </div>
    </div>
  );
} 