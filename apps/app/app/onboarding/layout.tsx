"use client";

import { useEffect } from "react";
import { usePreloadedQuery } from "convex/react";
import { useRouter } from "next/navigation";
import Image from "next/image";
import logo from "@workspace/assets/images/icon.png";
import Link from "next/link";
import { usePreloadedData } from "@/hooks/use-preloaded-data";

export default function OnboardingLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const router = useRouter();
  const { preloadedUser } = usePreloadedData();
  const user = usePreloadedQuery(preloadedUser);

  useEffect(() => {
    if (user && user.finishedSignUp) {
      router.push("/");
    }
  }, [user, router]);

  return (
    <div className="flex flex-col bg-background h-screen">
      <div className="fixed inset-0 z-0 opacity-5">
        <div className="absolute inset-0 bg-grid-pattern" />
      </div>
      
      <header className="relative z-10 p-4 flex justify-center border-b bg-background/80 backdrop-blur-sm ">
        <Link href="/" className="flex items-center space-x-2">
          <Image
            className="rounded-md z-1 relative border shadow-sm"
            src={logo}
            alt="liveciety logo"
            width={32}
            height={32}
          />
          <span className="font-semibold text-lg">Liveciety</span>
        </Link>
      </header>
      
      <main className="flex-1 flex items-center justify-center p-4 md:p-8 relative z-10">
        <div className="w-full space-y-8 bg-card p-6 rounded-lg shadow-sm">
          {children}
        </div>
      </main>
      
      <footer className="relative z-10 p-4 text-center text-xs text-muted-foreground border-t bg-background/80 backdrop-blur-sm">
        <p>© {new Date().getFullYear()} Liveciety. All rights reserved.</p>
      </footer>
      
      {/* Add a CSS class for the grid pattern */}
      <style jsx global>{`
        .bg-grid-pattern {
          background-image: radial-gradient(circle, currentColor 1px, transparent 1px);
          background-size: 30px 30px;
        }
      `}</style>
    </div>
  );
} 