"use client";

import Image from "next/image";
import logo from "@workspace/assets/images/icon.png";
import Link from "next/link";

export default function OnboardingLoading() {
  return (
    <div className="min-h-screen flex flex-col bg-background">
      {/* Background pattern */}
      <div className="fixed inset-0 z-0 opacity-5">
        <div className="absolute inset-0 bg-grid-pattern" />
      </div>
      
      {/* Logo and top area */}
      <header className="relative z-10 p-4 flex justify-center border-b bg-background/80 backdrop-blur-sm">
        <Link href="/" className="flex items-center space-x-2">
          <Image
            className="rounded-md z-1 relative border shadow-sm"
            src={logo}
            alt="liveciety logo"
            width={32}
            height={32}
          />
          <span className="font-semibold text-lg">Liveciety</span>
        </Link>
      </header>
      
      {/* Main content */}
      <main className="flex-1 flex items-center justify-center p-4 md:p-8 relative z-10">
        <div className="w-full max-w-md space-y-8 bg-card p-6 rounded-lg border shadow-sm">
          <div className="flex flex-col items-center space-y-4">
            <div className="relative animate-pulse">
              <div className="h-16 w-16 rounded-full bg-primary/10 flex items-center justify-center">
                <Image
                  src={logo}
                  alt="liveciety logo"
                  width={32}
                  height={32}
                  className="opacity-75"
                />
              </div>
            </div>
            <h1 className="text-2xl font-bold tracking-tight">Getting Ready...</h1>
            <p className="text-sm text-muted-foreground text-center">
              Loading your personalized experience
            </p>
            
            {/* Loading indicator */}
            <div className="w-full max-w-xs mx-auto pt-4">
              <div className="h-1 w-full bg-secondary rounded-full overflow-hidden">
                <div className="h-full bg-primary animate-pulse" style={{ width: '75%' }}></div>
              </div>
            </div>
          </div>
        </div>
      </main>
      
      {/* Footer */}
      <footer className="relative z-10 p-4 text-center text-xs text-muted-foreground border-t bg-background/80 backdrop-blur-sm">
        <p>© {new Date().getFullYear()} Liveciety. All rights reserved.</p>
      </footer>
      
      {/* Add a CSS class for the grid pattern */}
      <style jsx global>{`
        .bg-grid-pattern {
          background-image: radial-gradient(circle, currentColor 1px, transparent 1px);
          background-size: 30px 30px;
        }
      `}</style>
    </div>
  );
} 