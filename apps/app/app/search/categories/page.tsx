"use client";

import { useState, useEffect } from "react";
import { useSearchParams } from "next/navigation";
import { Input } from "@workspace/ui/components/input";
import { Button } from "@workspace/ui/components/button";
import Link from "next/link";
import Image from "next/image";
import { Search as SearchIcon, ArrowLeft } from "lucide-react";
import { categories, Category } from "@workspace/lib/constants/categories";

export default function CategoriesSearchPage() {
  const searchParams = useSearchParams();
  const query = searchParams.get("q") || "";
  const [searchQuery, setSearchQuery] = useState(query);

  useEffect(() => {
    setSearchQuery(query);
  }, [query]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!searchQuery.trim()) return;
    const url = new URL(window.location.href);
    url.searchParams.set("q", searchQuery);
    window.history.pushState({}, "", url);
  };

  const filteredCategories: Category[] = query
    ? categories.filter((cat) =>
        cat.title.toLowerCase().includes(query.toLowerCase()) ||
        cat.description.toLowerCase().includes(query.toLowerCase())
      )
    : categories;

  return (
    <div className="container max-w-screen-xl mx-auto py-6 px-4 md:px-6">
      <div className="mb-6">
        <div className="flex items-center gap-2 mb-4">
          <Link href={`/search?q=${encodeURIComponent(query)}`}>
            <Button variant="ghost" size="icon" className="rounded-full">
              <ArrowLeft className="h-4 w-4" />
            </Button>
          </Link>
          <h1 className="text-2xl font-bold">
            {query ? `Categories matching "${query}"` : "Search Categories"}
          </h1>
        </div>
        <form onSubmit={handleSubmit} className="flex w-full max-w-xl">
          <div className="relative flex-1">
            <Input
              placeholder="Search categories"
              className="pr-10 h-10"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
            <Button 
              type="submit"
              size="icon"
              variant="ghost"
              className="absolute right-0 top-0 h-full aspect-square"
            >
              <SearchIcon className="h-4 w-4" />
            </Button>
          </div>
        </form>
      </div>
      <div className="my-4 pb-4 border-b">
        <div className="text-sm text-muted-foreground">
          {filteredCategories.length} results found
        </div>
      </div>
      {filteredCategories.length > 0 ? (
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6">
          {filteredCategories.map((cat) => (
            <Link
              key={cat.id}
              href={`/browse/${cat.id}`}
              className="flex items-center gap-4 p-4 rounded-lg border hover:bg-accent/50 transition cursor-pointer"
            >
              <Image
                src={cat.image}
                alt={cat.title}
                width={48}
                height={48}
                className="h-12 w-12 rounded object-cover"
              />
              <div className="flex-1 min-w-0">
                <p className="text-base font-medium truncate">{cat.title}</p>
                <p className="text-xs text-muted-foreground truncate">{cat.description}</p>
              </div>
            </Link>
          ))}
        </div>
      ) : (
        <div className="text-center py-12">
          <p className="text-muted-foreground">No categories found matching &quot;{query}&quot;</p>
          <p className="text-sm text-muted-foreground mt-2">Try a different search term</p>
        </div>
      )}
    </div>
  );
} 