"use client";

import { useState } from "react";
import { useMutation, useQuery } from "convex/react";
import { api } from "@workspace/backend/convex/_generated/api";
import { Star, Tag } from "lucide-react";
import { Button } from "@workspace/ui/components/button";
import { Badge } from "@workspace/ui/components/badge";
import Link from "next/link";

interface ProductResultsProps {
  query: string;
  layout?: "grid" | "list";
  limit?: number;
}

const PLACEHOLDER_PRODUCTS = [
  {
    _id: "1",
    name: "Velvet Matte Liquid Lipstick",
    description: "About Last Night",
    price: 18,
    currency: "USD",
    images: ["/placeholder-product-1.jpg"],
    sellerId: "1",
    seller: {
      username: "miamioclock",
      avatarUrl: null
    },
    condition: "new",
    category: "Makeup",
  },
  {
    _id: "2",
    name: "Saffron Rouge-Inspired by Baccarat Rouge",
    description: "Long-lasting parfum",
    price: 29,
    currency: "USD",
    images: ["/placeholder-product-2.jpg"],
    sellerId: "2",
    seller: {
      username: "inspiredbyscent",
      avatarUrl: null
    },
    condition: "new",
    category: "Fragrance",
  },
  {
    _id: "3",
    name: "YSL Black Opium Le Parfum 3oz/90ml",
    description: "Sealed $195 Retail",
    price: 90,
    currency: "USD",
    images: ["/placeholder-product-3.jpg"],
    sellerId: "3",
    seller: {
      username: "mysistersclosetbeauty",
      avatarUrl: null
    },
    condition: "new",
    category: "Fragrance",
  },
  {
    _id: "4",
    name: "Juicy Couture Daydreamer Bag",
    description: "Rare (READ ME!)",
    price: 230,
    currency: "USD",
    images: ["/placeholder-product-4.jpg"],
    sellerId: "4",
    seller: {
      username: "shannonspecialties",
      avatarUrl: null
    },
    condition: "good",
    category: "Bags",
  }
];

export function ProductResults({ query, layout = "list", limit }: ProductResultsProps) {
  const [showAll, setShowAll] = useState(false);
  const filteredProducts = PLACEHOLDER_PRODUCTS.filter(
    product => product.name.toLowerCase().includes(query.toLowerCase())
  );
  const productsToShow = showAll || !limit ? filteredProducts : filteredProducts.slice(0, limit);
  return (
    <div className={layout === "grid" ? "grid-layout" : "list-layout"}>
      {filteredProducts.length === 0 ? (
        <div className="text-center py-12">
          <p className="text-muted-foreground">No products found matching "{query}"</p>
        </div>
      ) : (
        <>
          {layout === "grid" ? (
            <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4">
              {productsToShow.map((product) => (
                <ProductGridItem key={product._id} product={product} />
              ))}
            </div>
          ) : (
            <div className="space-y-4">
              {productsToShow.map((product) => (
                <ProductListItem key={product._id} product={product} />
              ))}
            </div>
          )}
          {limit && filteredProducts.length > limit && (
            <div className="flex justify-center mt-4">
              <Button size="sm" variant="ghost" onClick={() => setShowAll((v) => !v)}>
                {showAll ? "Show less" : "Show more"}
              </Button>
            </div>
          )}
        </>
      )}
    </div>
  );
}

function ProductGridItem({ product }: { product: any }) {
  const [isHovered, setIsHovered] = useState(false);
  const saveSearch = useMutation(api.savedSearches.saveSearch);

  const handleSaveSearch = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    
    saveSearch({
      searchQuery: product.name,
      type: "product",
      targetId: product._id,
      isPinned: true
    });
  };

  return (
    <Link 
      href={`/product/${product._id}`}
      className="block"
    >
      <div 
        className="border rounded-xl overflow-hidden hover:shadow-md transition-all"
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
      >
        <div className="relative aspect-square bg-accent/20">
          <div className="absolute inset-0 flex items-center justify-center text-muted-foreground">
            <Tag className="h-8 w-8" />
          </div>
          
          {isHovered && (
            <Button
              variant="secondary"
              size="icon"
              className="absolute top-2 right-2 h-6 w-6 rounded-full"
              onClick={handleSaveSearch}
            >
              <Star className="h-3 w-3" />
            </Button>
          )}
          
          <Badge 
            variant="secondary" 
            className="absolute bottom-2 left-2"
          >
            {product.condition}
          </Badge>
        </div>
        
        <div className="p-3">
          <p className="font-medium truncate">{product.name}</p>
          <p className="text-sm truncate text-muted-foreground">{product.description}</p>
          
          <div className="flex justify-between items-center mt-2">
            <span className="font-bold">${product.price}</span>
            <div className="flex items-center text-xs text-muted-foreground">
              <span>@{product.seller.username}</span>
            </div>
          </div>
        </div>
      </div>
    </Link>
  );
}

function ProductListItem({ product }: { product: any }) {
  const saveSearch = useMutation(api.savedSearches.saveSearch);

  const handleSaveSearch = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    
    saveSearch({
      searchQuery: product.name,
      type: "product",
      targetId: product._id,
      isPinned: true
    });
  };

  return (
    <Link 
      href={`/product/${product._id}`}
      className="block"
    >
      <div className="flex gap-4 border rounded-lg p-3 hover:bg-accent/20 transition-colors">
        <div className="relative h-20 w-20 bg-accent/20 rounded-md flex items-center justify-center">
          <Tag className="h-6 w-6 text-muted-foreground" />
          <Badge 
            variant="secondary" 
            className="absolute bottom-1 left-1 text-xs"
          >
            {product.condition}
          </Badge>
        </div>
        
        <div className="flex-1 min-w-0">
          <p className="font-medium truncate">{product.name}</p>
          <p className="text-sm truncate text-muted-foreground">{product.description}</p>
          
          <div className="flex items-center gap-2 mt-1 text-xs text-muted-foreground">
            <Badge variant="outline" className="text-xs">
              {product.category}
            </Badge>
            <span>@{product.seller.username}</span>
          </div>
        </div>
        
        <div className="flex flex-col items-end justify-between">
          <Button
            variant="ghost"
            size="icon"
            className="h-7 w-7"
            onClick={handleSaveSearch}
          >
            <Star className="h-4 w-4" />
          </Button>
          
          <span className="font-bold">${product.price}</span>
        </div>
      </div>
    </Link>
  );
} 