"use client";

import { useEffect, useState, useRef, useCallback } from "react";
import { useConvex } from "convex/react";
import { api } from "@workspace/backend/convex/_generated/api";
import { useSearchParams } from "next/navigation";
import { Search as SearchIcon, ArrowLeft } from "lucide-react";
import { Input } from "@workspace/ui/components/input";
import { Button } from "@workspace/ui/components/button";
import { UserResults } from "../_components/user-results";
import Link from "next/link";
import {
  Select,
  SelectTrigger,
  SelectValue,
  SelectContent,
  SelectItem,
} from "@workspace/ui/components/select";
import { User as UserType } from "@/lib/types";

const PAGE_SIZE = 50;

export default function UserSearchPage() {
  const searchParams = useSearchParams();
  const query = searchParams.get("q") || "";
  const [searchQuery, setSearchQuery] = useState(query);

  const [users, setUsers] = useState<UserType[]>([]);
  const [cursor, setCursor] = useState<string | null>(null);
  const [isDone, setIsDone] = useState(false);
  const [loading, setLoading] = useState(false);

  const loaderRef = useRef<HTMLDivElement | null>(null);

  const convex = useConvex();

  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("asc");

  useEffect(() => {
    setUsers([]);
    setCursor(null);
    setIsDone(false);
  }, [query, sortOrder]);

  const fetchUsers = useCallback(async () => {
    if (loading || isDone) return;
    setLoading(true);

    const res = await convex.query(
      api.users.searchUsers,
      {
        searchQuery: query,
        paginationOpts: { numItems: PAGE_SIZE, cursor },
        sortOrder,
      }
    );

    setUsers((prev) => [...prev, ...(res.users || [])]);
    setCursor(res.continueCursor || null);
    setIsDone(res.isDone);
    setLoading(false);
  }, [convex, query, cursor, isDone, loading, sortOrder]);

  useEffect(() => {
    fetchUsers();
    // eslint-disable-next-line
  }, [query, sortOrder]);

  useEffect(() => {
    if (isDone || loading) return;
    const observer = new IntersectionObserver(
      (entries) => {
        if (entries[0]?.isIntersecting) {
          fetchUsers();
        }
      },
      { threshold: 1 }
    );
    if (loaderRef.current) {
      observer.observe(loaderRef.current);
    }
    return () => {
      if (loaderRef.current) {
        observer.unobserve(loaderRef.current);
      }
    };
  }, [fetchUsers, isDone, loading]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!searchQuery.trim()) return;
    const url = new URL(window.location.href);
    url.searchParams.set("q", searchQuery);
    window.history.pushState({}, "", url);
  };

  useEffect(() => {
    setSearchQuery(query);
  }, [query]);

  const hasResults = users.length > 0;
  const resultCount = users.length;

  return (
    <div className="container max-w-screen-xl mx-auto py-6 px-4 md:px-6">
      <div className="mb-6">
        <div className="flex items-center gap-2 mb-4">
          <Link href={`/search?q=${encodeURIComponent(query)}`}>
            <Button variant="ghost" size="icon" className="rounded-full">
              <ArrowLeft className="h-4 w-4" />
            </Button>
          </Link>
          <h1 className="text-2xl font-bold">
            {query ? `People matching "${query}"` : "Search People"}
          </h1>
          <div className="ml-auto">
            <Select value={sortOrder} onValueChange={v => setSortOrder(v as "asc" | "desc")}> 
              <SelectTrigger className="w-28">
                <SelectValue placeholder="Sort" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="asc">A-Z</SelectItem>
                <SelectItem value="desc">Z-A</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
        
        <form onSubmit={handleSubmit} className="flex w-full max-w-xl">
          <div className="relative flex-1">
            <Input
              placeholder="Search people"
              className="pr-10 h-10"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
            <Button 
              type="submit"
              size="icon"
              variant="ghost"
              className="absolute right-0 top-0 h-full aspect-square"
            >
              <SearchIcon className="h-4 w-4" />
            </Button>
          </div>
        </form>
      </div>

      <div className="my-4 pb-4 border-b">
        <div className="text-sm text-muted-foreground">
          {resultCount} results found
        </div>
      </div>

      {query ? (
        hasResults ? (
          <>
            <UserResults users={users} layout="grid" loading={loading} hasMore={!isDone} />
            <div ref={loaderRef} style={{ height: 40 }} />
          </>
        ) : (
          <div className="text-center py-12">
            <p className="text-muted-foreground">No users found matching &quot;{query}&quot;</p>
            <p className="text-sm text-muted-foreground mt-2">Try a different search term</p>
          </div>
        )
      ) : (
        <div className="text-center py-12">
          <p className="text-muted-foreground">Enter a search term to find people</p>
        </div>
      )}
    </div>
  );
} 