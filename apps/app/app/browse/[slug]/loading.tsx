import { Skeleton } from "@workspace/ui/components/skeleton";

export default function CategoryLoading() {
  return (
    <div>
      {/* Sticky header with tabs skeleton */}
      <div className="sticky top-0 z-10 bg-background border-b">
        <div className="container mx-auto px-4 py-4">
          <Skeleton className="h-8 w-48 mb-4" />
          
          {/* Subcategories tabs skeleton */}
          <div className="flex gap-2 overflow-x-auto pb-2 hide-scrollbar">
            {Array.from({ length: 8 }).map((_, i) => (
              <Skeleton key={i} className="h-9 w-28 rounded-full flex-shrink-0" />
            ))}
          </div>
        </div>
      </div>
      
      <div className="container mx-auto py-4 px-4">
        {/* Filter section skeleton */}
        <div className="mb-6 border-b pb-4">
          <div className="flex flex-col space-y-3">
            <div className="flex items-center justify-between">
              <Skeleton className="h-5 w-16" />
              <Skeleton className="h-4 w-4" />
            </div>
            
            <div className="flex flex-col space-y-2">
              {Array.from({ length: 3 }).map((_, i) => (
                <div key={i} className="flex items-center gap-2">
                  <Skeleton className="h-4 w-4 rounded-full" />
                  <Skeleton className="h-4 w-32" />
                </div>
              ))}
            </div>
          </div>
        </div>
        
        {/* Stream cards skeleton */}
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
          {Array.from({ length: 8 }).map((_, i) => (
            <div key={i} className="flex flex-col">
              <Skeleton className="w-full aspect-video rounded-lg" />
              <div className="mt-2">
                <div className="flex items-center gap-2">
                  <Skeleton className="w-6 h-6 rounded-full" />
                  <Skeleton className="h-4 w-24" />
                </div>
                <Skeleton className="h-4 w-full mt-1" />
                <Skeleton className="h-4 w-4/5 mt-1" />
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
} 