import Image from "next/image";
import Link from "next/link";
import { categories } from "@workspace/lib/constants/categories";
import { Card, CardContent } from "@workspace/ui/components/card";

export default function BrowsePage() {
  return (
    <div className="mx-auto p-4">
      <h1 className="text-2xl font-bold mb-6">Browse Categories</h1>
      
      <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-4">
        {categories
        .sort((a, b) => a.title.localeCompare(b.title))
        .map((category) => (
          <Link 
            key={category.id} 
            href={`/browse/${category.id}`}
            className="transition-transform hover:scale-[1.02]"
          >
            <Card className="overflow-hidden h-full border hover:border-primary">
              <div className="relative aspect-square bg-gradient-to-br from-primary/10 to-primary/5">
                {category.image && (
                  <Image
                    src={typeof category.image === 'string' 
                      ? category.image 
                      : category.image.default || category.image}
                    alt={category.title}
                    fill
                    className="object-cover"
                    sizes="(max-width: 640px) 50vw, (max-width: 768px) 33vw, (max-width: 1024px) 25vw, 16vw"
                  />
                )}
              </div>
              <CardContent className="p-3">
                <h2 className="font-semibold text-sm">{category.title}</h2>
                <p className="text-xs text-muted-foreground line-clamp-1">
                  {category.description}
                </p>
              </CardContent>
            </Card>
          </Link>
        ))}
      </div>
    </div>
  );
}
