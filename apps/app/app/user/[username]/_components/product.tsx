"use client"

import { useState } from "react"
import Image from "next/image"
import { ChevronLeft, ChevronRight, ChevronDown, ChevronUp, Minus, Plus } from "lucide-react"
import { But<PERSON> } from "@workspace/ui/components/button"
import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON>ooter, CardHeader } from "@workspace/ui/components/card"
import { Badge, CustomBadge } from "@workspace/ui/components/badge"
import { getAvatarImageUrl } from "@/lib/utils"
import { getCategoryTitle } from "@/lib/utils"

interface ProductCardProps {
  product?: any
  user?: any
}

export default function ProductCard({ product, user }: ProductCardProps) {
  const [currentImageIndex, setCurrentImageIndex] = useState(0)
  const [isDescriptionExpanded, setIsDescriptionExpanded] = useState(false)
  const [quantity, setQuantity] = useState(1)

  console.log("User", user)

  const nextImage = () => {
    setCurrentImageIndex((prev) => (prev === product.images.length - 1 ? 0 : prev + 1))
  }

  const prevImage = () => {
    setCurrentImageIndex((prev) => (prev === 0 ? product.images.length - 1 : prev - 1))
  }

  const incrementQuantity = () => {
    setQuantity((prev) => prev + 1)
  }

  const decrementQuantity = () => {
    setQuantity((prev) => (prev > 1 ? prev - 1 : 1))
  }

  const truncatedDescription =
    product.description.length > 100 ? product.description.substring(0, 100) + "..." : product.description

  console.log("Product", product)

  return (
    <div className="w-64">
      <Card className="w-full !p-0">
        <CardHeader className="!p-3 !mb-0 !pb-0">
          {product.category && (
            <Badge variant="secondary" className="w-fit text-xs">
              {getCategoryTitle(product.category)}
            </Badge>
          )}
          <h3 className="font-semibold text-sm leading-tight">{product.name}</h3>
        </CardHeader>

        <CardContent className="!pt-0 !p-3 !py-0">
          {/* Image Slider */}
          <div className="relative">
            <div className="aspect-square overflow-hidden rounded-md bg-gray-100">
              <Image
                src={getAvatarImageUrl(product.images[currentImageIndex]) || "/placeholder.svg"}
                alt={`${product.name} - Image ${currentImageIndex + 1}`}
                width={200}
                height={200}
                className="w-full h-full object-cover border border-input"
              />
            </div>

            {product.images.length > 1 && (
              <>
                <Button
                  variant="ghost"
                  size="sm"
                  className="absolute left-1 top-1/2 -translate-y-1/2 h-6 w-6 p-0 bg-white/80 hover:bg-white"
                  onClick={prevImage}
                >
                  <ChevronLeft className="h-3 w-3" />
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  className="absolute right-1 top-1/2 -translate-y-1/2 h-6 w-6 p-0 bg-white/80 hover:bg-white"
                  onClick={nextImage}
                >
                  <ChevronRight className="h-3 w-3" />
                </Button>

                {/* Image indicators */}
                <div className="absolute bottom-2 left-1/2 -translate-x-1/2 flex gap-1">
                  {product.images.map((_: any, index: number) => (
                    <button
                      key={index}
                      className={`w-1.5 h-1.5 rounded-full transition-colors ${
                        index === currentImageIndex ? "bg-white" : "bg-white/50"
                      }`}
                      onClick={() => setCurrentImageIndex(index)}
                    />
                  ))}
                </div>
              </>
            )}
          </div>

          {/* Description */}
          <div className="mb-3">
            <p className="text-xs text-muted-foreground leading-relaxed">
              {isDescriptionExpanded ? product.description : truncatedDescription}
            </p>
            {product.description.length > 100 && (
              <Button
                variant="ghost"
                size="sm"
                className="h-auto p-0 text-xs text-primary hover:bg-transparent"
                onClick={() => setIsDescriptionExpanded(!isDescriptionExpanded)}
              >
                {isDescriptionExpanded ? (
                  <>
                    Show less <ChevronUp className="ml-1 h-3 w-3" />
                  </>
                ) : (
                  <>
                    Show more <ChevronDown className="ml-1 h-3 w-3" />
                  </>
                )}
              </Button>
            )}
          </div>

          {/* Price */}
          <div className="flex items-center">
            <span className="text-lg font-bold">${product.price.toFixed(2)}</span>
            {product.inventory && (
              <CustomBadge circle="warning" variant="warning" className="ml-2 text-xs text-white">
                {product.inventory === 0 ? "Out of Stock" : `Only ${product.inventory} left`}
              </CustomBadge>
            )}
          </div>

          {/* Quantity Selector */}
          <div className="flex items-center">
            <span className="text-sm font-medium">Quantity:</span>
            <div className="flex items-center gap-2">
              <span className="text-sm font-medium w-6 text-center">{quantity}</span>
            </div>
          </div>
        </CardContent>

        <CardFooter className="!p-3 !pt-0">
          {!product.sellerId === user._id ? (
          <Button className="w-full text-sm" disabled={!product.inventory}>
            {product.inventory ? "Buy Now" : "Out of Stock"}
          </Button>
          ) : (
            <Button className="w-full text-sm" disabled={!product.inventory}>
              Edit
            </Button>
          )}
        </CardFooter>
      </Card>
    </div>
  )
}
