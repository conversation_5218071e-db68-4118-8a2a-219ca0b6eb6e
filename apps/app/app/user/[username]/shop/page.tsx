"use client";

import { useQuery } from "convex/react";
import { api } from "@workspace/backend/convex/_generated/api";
import { useParams, notFound } from "next/navigation";
import Link from "next/link";
import { But<PERSON> } from "@workspace/ui/components/button";
import { ArrowLeft } from "lucide-react";

export default function UserShopPage() {  
  const { username } = useParams<{ username: string }>();
  
  const user = useQuery(api.users.getUserByUsername, { username });
  
  if (user === undefined) {
    return (
      <div className="min-h-screen bg-black text-white p-4">
        <div className="max-w-4xl mx-auto">
          <div className="flex items-center mb-6">
            <Button variant="ghost" size="icon" asChild className="mr-2">
              <Link href={`/user/${username}`}>
                <ArrowLeft className="h-5 w-5" />
              </Link>
            </Button>
            <h1 className="text-xl font-bold">Shop</h1>
          </div>
          
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
            {Array(8).fill(null).map((_, i) => (
              <div key={i} className="bg-gray-900 rounded-lg p-3 animate-pulse">
                <div className="aspect-square bg-gray-800 rounded-md mb-2"></div>
                <div className="h-4 bg-gray-800 rounded w-full mb-1"></div>
                <div className="h-4 bg-gray-800 rounded w-1/2"></div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }
  
  if (user === null) {
    return notFound();
  }
  
  return (
    <div className="min-h-screen bg-black text-white p-4">
      <div className="max-w-4xl mx-auto">
        <div className="flex items-center mb-6">
          <Button variant="ghost" size="icon" asChild className="mr-2">
            <Link href={`/user/${username}`}>
              <ArrowLeft className="h-5 w-5" />
            </Link>
          </Button>
          <h1 className="text-xl font-bold">{user.username}'s Shop</h1>
        </div>
        
        {/* Shop items would go here */}
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
          {Array(8).fill(null).map((_, i) => (
            <div key={i} className="bg-gray-900 rounded-lg p-3">
              <div className="aspect-square bg-gray-800 rounded-md mb-2"></div>
              <div className="h-4 bg-gray-800 rounded w-full mb-1">Item {i+1}</div>
              <div className="h-4 bg-gray-800 rounded w-1/2">$99.99</div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
} 