"use client";

import { useQuery } from "convex/react";
import { api } from "@workspace/backend/convex/_generated/api";
import { useParams, notFound } from "next/navigation";
import Link from "next/link";
import { But<PERSON> } from "@workspace/ui/components/button";
import { Avatar, AvatarFallback } from "@workspace/ui/components/avatar";
import { ArrowLeft, StarIcon } from "lucide-react";

export default function UserReviewsPage() {
  const { username } = useParams<{ username: string }>();
  
  const user = useQuery(api.users.getUserByUsername, { username });
  
  if (user === undefined) {
    return (
      <div className="min-h-screen bg-black text-white p-4">
        <div className="max-w-4xl mx-auto">
          <div className="flex items-center mb-6">
            <Button variant="ghost" size="icon" asChild className="mr-2">
              <Link href={`/user/${username}`}>
                <ArrowLeft className="h-5 w-5" />
              </Link>
            </Button>
            <h1 className="text-xl font-bold">Reviews</h1>
          </div>
          
          <div className="space-y-4">
            {Array(5).fill(null).map((_, i) => (
              <div key={i} className="bg-gray-900 rounded-lg p-4 animate-pulse">
                <div className="flex items-center gap-3 mb-3">
                  <div className="w-10 h-10 rounded-full bg-gray-800"></div>
                  <div>
                    <div className="h-4 bg-gray-800 rounded w-24 mb-1"></div>
                    <div className="h-3 bg-gray-800 rounded w-16"></div>
                  </div>
                </div>
                <div className="h-4 bg-gray-800 rounded w-full mb-2"></div>
                <div className="h-4 bg-gray-800 rounded w-full mb-2"></div>
                <div className="h-4 bg-gray-800 rounded w-3/4"></div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }
  
  if (user === null) {
    return notFound();
  }
  
  return (
    <div className="min-h-screen bg-black text-white p-4">
      <div className="max-w-4xl mx-auto">
        <div className="flex items-center mb-6">
          <Button variant="ghost" size="icon" asChild className="mr-2">
            <Link href={`/user/${username}`}>
              <ArrowLeft className="h-5 w-5" />
            </Link>
          </Button>
          <h1 className="text-xl font-bold">Reviews for {user.username}</h1>
        </div>
        
        {/* Reviews would go here */}
        <div className="space-y-4">
          {Array(5).fill(null).map((_, i) => (
            <div key={i} className="bg-gray-900 rounded-lg p-4">
              <div className="flex items-center gap-3 mb-3">
                <Avatar className="h-10 w-10">
                  <AvatarFallback className="bg-gray-700">
                    U{i+1}
                  </AvatarFallback>
                </Avatar>
                <div>
                  <div className="font-medium">User {i+1}</div>
                  <div className="flex items-center text-yellow-500">
                    {Array(5).fill(null).map((_, j) => (
                      <StarIcon 
                        key={j} 
                        className={`h-3 w-3 ${j < 4 ? 'fill-current' : ''}`} 
                      />
                    ))}
                    <span className="text-xs text-gray-400 ml-1">1 month ago</span>
                  </div>
                </div>
              </div>
              <p className="text-gray-200">
                Example review text. This seller was great! Very responsive and the product was exactly as described.
              </p>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
} 