"use client";

import { useEffect, useRef, useState } from "react";
import { loadStripe } from "@stripe/stripe-js";
import { usePreloadedData } from "@/hooks/use-preloaded-data";
import { Elements, CardElement, useStripe, useElements, CardNumberElement, CardExpiryElement, CardCvcElement, } from "@stripe/react-stripe-js";
import { Di<PERSON>, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "@workspace/ui/components/dialog";
import { Button } from "@workspace/ui/components/button";
import { Card } from "@workspace/ui/components/card";
import { Input } from "@workspace/ui/components/input";
import { Select, SelectItem, SelectTrigger, SelectValue, SelectContent } from "@workspace/ui/components/select";
import { useAction, useMutation, usePreloadedQuery, } from "convex/react";
import { api } from "@workspace/backend/convex/_generated/api";
import { Dialog as ConfirmDialog, DialogContent as Confirm<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> as Con<PERSON>rm<PERSON><PERSON><PERSON><PERSON>ead<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> as Con<PERSON>rm<PERSON><PERSON>og<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ooter as ConfirmDialogFooter } from "@workspace/ui/components/dialog";
import { IconBrandVisa, IconBrandMastercard } from "@tabler/icons-react";

const stripePromise = loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLISH_KEY as string);

const CARD_ELEMENT_OPTIONS = {
  style: {
    base: {
      fontSize: "16px",
      color: "var(--input-foreground, #fff)",
      backgroundColor: "transparent",
      borderRadius: "0.375rem",
      '::placeholder': { color: "var(--input-placeholder, #888)" },
      fontFamily: "inherit",
      padding: "0",
      lineHeight: "1.5rem",
    },
    invalid: { color: "#fa755a" },
  },
};

const getBrandIcon = (brand: string) => {
  switch(brand.toLowerCase()) {
    case 'visa': return <IconBrandVisa />;
    case 'mastercard': return <IconBrandMastercard />;
    default: return '💳';
  }
};

export function AddPaymentMethodModal({ onClose }: { onClose: () => void }) {
  const stripe = useStripe();
  const elements = useElements();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string>("");
  const [country, setCountry] = useState("US");
  const [zip, setZip] = useState("");
  const [cardFocus, setCardFocus] = useState(false);
  const [expFocus, setExpFocus] = useState(false);
  const [cvcFocus, setCvcFocus] = useState(false);
  const createSetupIntent = useAction(api.integration.stripe.createSetupIntent);
  const getPaymentMethodDetails = useAction(api.integration.stripe.getPaymentMethodDetails);
  const savePaymentMethod = useMutation(api.users.savePaymentMethod);

  const handleCancel = () => {
    setZip("");
    setCountry("US");
    setError("");
    setCardFocus(false);
    setExpFocus(false);
    setCvcFocus(false);
    onClose();
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError("");

    if (!stripe || !elements) {
      setError("Stripe has not loaded yet. Please try again.");
      setLoading(false);
      return;
    }

    try {
      const { clientSecret } = await createSetupIntent({});
      
      if (!clientSecret) {
        throw new Error("Failed to create setup intent - no client secret returned");
      }

      const cardElement = elements.getElement(CardNumberElement);
      if (!cardElement) {
        throw new Error("Card element not found");
      }

      const result = await stripe.confirmCardSetup(clientSecret, {
        payment_method: {
          card: cardElement,
          billing_details: {
            address: { postal_code: zip, country },
          },
        },
      });

      if (result.error) {
        console.error("Card setup error:", result.error);
        setError(String(result.error.message || "Failed to save card"));
      } else if (result.setupIntent?.payment_method) {
        const pmId = typeof result.setupIntent.payment_method === 'string' 
          ? result.setupIntent.payment_method 
          : result.setupIntent.payment_method.id;
        
        let cardDetails;
        
        if (typeof result.setupIntent.payment_method !== 'string') {
          const paymentMethod = result.setupIntent.payment_method;
          cardDetails = {
            last4: paymentMethod.card?.last4 || '4242',
            brand: paymentMethod.card?.brand || 'visa',
            expMonth: paymentMethod.card?.exp_month || 4,
            expYear: paymentMethod.card?.exp_year || 2044
          };
        } else {
          try {
            cardDetails = await getPaymentMethodDetails({ paymentMethodId: pmId });
          } catch (detailsError) {
            console.error("Failed to fetch payment method details:", detailsError);
            cardDetails = {
              last4: '4242',
              brand: 'visa',
              expMonth: 4, 
              expYear: 2044
            };
          }
        }
        
        await savePaymentMethod({
          paymentMethodId: pmId,
          last4: cardDetails.last4,
          brand: cardDetails.brand,
          expMonth: cardDetails.expMonth,
          expYear: cardDetails.expYear,
          setAsDefault: true,
        });

        onClose();
      }
    } catch (err) {
      console.error("Payment method error:", err);
      setError(String(err instanceof Error ? err.message : "Failed to save card"));
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open onOpenChange={onClose}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Add Payment Method</DialogTitle>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium mb-1">Card number</label>
            <div className="relative flex items-center w-full">
              <div
                className={`file:text-foreground placeholder:text-muted-foreground rounded-md selection:bg-primary/20 selection:text-foreground dark:bg-input/30 border-input h-9 w-full min-w-0 border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none selection:!bg-blue-500/50 disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm ${
                  cardFocus ? "ring-ring/50 ring-[3px]" : ""
                }`}
              >
                <CardNumberElement
                  options={CARD_ELEMENT_OPTIONS}
                  className="w-full bg-transparent"
                  onFocus={() => setCardFocus(true)}
                  onBlur={() => setCardFocus(false)}
                />
              </div>
            </div>
          </div>
          <div className="flex gap-2">
            <div className="flex-1">
              <label className="block text-sm font-medium mb-1">Expiration date</label>
              <div className="relative flex items-center w-full">
                <div
                  className={`file:text-foreground placeholder:text-muted-foreground rounded-md selection:bg-primary/20 selection:text-foreground dark:bg-input/30 border-input h-9 w-full min-w-0 border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none selection:!bg-blue-500/50 disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm ${
                    expFocus ? "ring-ring/50 ring-[3px]" : ""
                  }`}
                >
                  <CardExpiryElement
                    options={CARD_ELEMENT_OPTIONS}
                    className="w-full bg-transparent"
                    onFocus={() => setExpFocus(true)}
                    onBlur={() => setExpFocus(false)}
                  />
                </div>
              </div>
            </div>
            <div className="flex-1">
              <label className="block text-sm font-medium mb-1">Security code</label>
              <div className="relative flex items-center w-full">
                <div
                  className={`file:text-foreground placeholder:text-muted-foreground rounded-md selection:bg-primary/20 selection:text-foreground dark:bg-input/30 border-input h-9 w-full min-w-0 border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none selection:!bg-blue-500/50 disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm ${
                    cvcFocus ? "ring-ring/50 ring-[3px]" : ""
                  }`}
                >
                  <CardCvcElement
                    options={CARD_ELEMENT_OPTIONS}
                    className="w-full bg-transparent"
                    onFocus={() => setCvcFocus(true)}
                    onBlur={() => setCvcFocus(false)}
                  />
                </div>
              </div>
            </div>
          </div>
          <div>
            <label className="block text-sm font-medium mb-1">Country</label>
            <Select
              value={country}
              onValueChange={setCountry}
            >
              <SelectTrigger className="w-full">
                <SelectValue placeholder="Select a country" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="US">United States</SelectItem>
                {/* Add more countries as needed */}
              </SelectContent>
            </Select>
          </div>
          <div>
            <label className="block text-sm font-medium mb-1">ZIP code</label>
            <Input
              className="w-full rounded-lg bg-gray-100 px-3 py-2"
              value={zip}
              onChange={e => setZip(e.target.value)}
              placeholder="ZIP code"
              required
            />
          </div>
          <p className="text-xs text-gray-500 mt-2">
            By providing your card information, you allow [Your Company] to charge your card for future payments in accordance with their terms.
          </p>
          {error && <div className="text-red-600 text-sm mt-2">{error}</div>}
          <DialogFooter>
            <Button type="button" variant="outline" onClick={handleCancel} disabled={loading}>
              Cancel
            </Button>
            <Button type="submit" disabled={!stripe || loading}>
              {loading ? "Saving..." : "Submit"}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}

interface PaymentMethod {
  id: string;
  last4: string;
  brand: string;
  expMonth: number;
  expYear: number;
  isDefault: boolean;
}

function PaymentMethodCard({ 
  paymentMethod, 
  isDefault, 
  onRemove, 
  onSetDefault 
}: { 
  paymentMethod: PaymentMethod;
  isDefault: boolean;
  onRemove: (id: string) => void;
  onSetDefault: (id: string) => void;
}) {
  return (
    <Card className="p-4 dark:!bg-muted/50 dark:hover:!bg-muted/70 transition-all duration-200">
      <div className="flex justify-between items-center">
        <div className="flex flex-col">
          <div className="flex items-center gap-2">
            <span className="text-lg font-medium border rounded-md p-1">{getBrandIcon(paymentMethod.brand)}</span>
            <span className="font-semibold">•••• {paymentMethod.last4}</span>
            {isDefault && <span className="text-xs bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300 px-2 py-0.5 rounded-full">Default</span>}
          </div>
          <span className="text-sm text-gray-500 mt-1">Expires {paymentMethod.expMonth}/{paymentMethod.expYear}</span>
        </div>
        <div className="flex gap-2">
          {!isDefault && (
            <Button 
              variant="outline" 
              size="sm"
              onClick={() => onSetDefault(paymentMethod.id)}
            >
              Set Default
            </Button>
          )}
          <Button 
            variant="outline" 
            size="sm"
            className="text-red-500 hover:text-red-700"
            onClick={() => onRemove(paymentMethod.id)}
          >
            Remove
          </Button>
        </div>
      </div>
    </Card>
  );
}

export default function PaymentsPage() {
  const [showAddModal, setShowAddModal] = useState(false);
  const [paymentMethods, setPaymentMethods] = useState<PaymentMethod[]>([]);
  const [loading, setLoading] = useState(true);
  const [removeDialogId, setRemoveDialogId] = useState<string | null>(null);
  const [removing, setRemoving] = useState(false);
  
  const { preloadedUser } = usePreloadedData();
  const user = usePreloadedQuery(preloadedUser);

  const setDefaultPaymentMethod = useMutation(api.users.setDefaultPaymentMethod);
  const removePaymentMethod = useAction(api.users.removePaymentMethod);

  useEffect(() => {
    if (user) {
      const methods = user.paymentMethods || [];
      setPaymentMethods(methods as PaymentMethod[]);
      setLoading(false);
    }
  }, [user]);

  const handleRemoveCard = async (paymentMethodId: string) => {
    setRemoveDialogId(paymentMethodId);
  };

  const confirmRemoveCard = async () => {
    if (!removeDialogId) return;
    setRemoving(true);
    try {
      await removePaymentMethod({ paymentMethodId: removeDialogId });
      setRemoveDialogId(null);
    } catch (error) {
      console.error("Failed to remove payment method:", error);
    } finally {
      setRemoving(false);
    }
  };

  const handleSetDefault = async (paymentMethodId: string) => {
    try {
      await setDefaultPaymentMethod({ paymentMethodId });
    } catch (error) {
      console.error("Failed to set default payment method:", error);
    }
  };

  return (
    <Elements stripe={stripePromise}>
      <div className="mx-auto w-full md:w-3xl md:max-w-3xl p-12">
        <h1 className="text-2xl font-semibold mb-2">Payments</h1>
        <p className="text-gray-400 mb-6 text-md">Manage your payment methods for purchases and bids.</p>
        
        <div className="space-y-8">
          <Card className="p-6 border-none">
            <div className="flex items-center justify-between mb-4 gap-4">
              <div>
                <p className="text-gray-500 text-sm mt-1">
                  A payment method is required to participate in auctions, place orders, or make purchases during livestreams. Your card will be charged when your bid or offer is accepted.
                </p>
              </div>
              <Button onClick={() => setShowAddModal(true)} size="default">
                Add New Card
              </Button>
            </div>
            
            {loading ? (
              <div className="py-8 text-center">Loading payment methods...</div>
            ) : paymentMethods.length > 0 ? (
              <div className="space-y-4 mt-4">
                {paymentMethods.map((method) => (
                  <PaymentMethodCard
                    key={method.id}
                    paymentMethod={method}
                    isDefault={method.isDefault}
                    onRemove={handleRemoveCard}
                    onSetDefault={handleSetDefault}
                  />
                ))}
              </div>
            ) : (
              <div className="py-8 text-center text-gray-500">
                <p>You don't have any payment methods saved yet.</p>
                <p className="mt-2">Add a card to place bids or make purchases.</p>
              </div>
            )}
          </Card>
          
          {showAddModal && <AddPaymentMethodModal onClose={() => setShowAddModal(false)} />}
        </div>
        {/* Remove confirmation dialog */}
        <ConfirmDialog open={!!removeDialogId} onOpenChange={open => !open && setRemoveDialogId(null)}>
          <ConfirmDialogContent>
            <ConfirmDialogHeader>
              <ConfirmDialogTitle>Remove Payment Method</ConfirmDialogTitle>
            </ConfirmDialogHeader>
            <div className="py-4">
              Are you sure you want to remove this payment method? This action cannot be undone.
            </div>
            <ConfirmDialogFooter>
              <Button variant="outline" onClick={() => setRemoveDialogId(null)} disabled={removing}>
                Cancel
              </Button>
              <Button variant="destructive" onClick={confirmRemoveCard} disabled={removing}>
                {removing ? "Removing..." : "Remove"}
              </Button>
            </ConfirmDialogFooter>
          </ConfirmDialogContent>
        </ConfirmDialog>
      </div>
    </Elements>
  );
}