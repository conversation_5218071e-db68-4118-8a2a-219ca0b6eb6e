"use client";

import React, { useState, useTransition } from "react";
import { useQuery, useAction } from "convex/react";
import { api } from "@workspace/backend/convex/_generated/api";
import { Id } from "@workspace/backend/convex/_generated/dataModel";
import { But<PERSON> } from "@workspace/ui/components/button";
import { Card, CardHeader, CardTitle, CardContent } from "@workspace/ui/components/card";
import { Skeleton } from "@workspace/ui/components/skeleton";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger, DialogDescription } from "@workspace/ui/components/dialog";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@workspace/ui/components/select";
import { IngressInput } from "livekit-server-sdk";

function isArrayOfStreams(val: any): val is { _id: Id<"streams">; title?: string; scheduledStartTime?: number; status?: string }[] {
  return Array.isArray(val);
}

function StreamKeysPanel({ streamId }: { streamId: Id<"streams"> }) {
  const stream = useQuery(api.streams.getStreamById, { streamId });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [serverUrl, setServerUrl] = useState<string>("");
  const [streamKey, setStreamKey] = useState<string>("");
  const [ingressType, setIngressType] = useState<string>("RTMP_INPUT");
  const [deleting, setDeleting] = useState(false);
  const [creating, setCreating] = useState(false);
  const [modalOpen, setModalOpen] = useState(false);
  const [selectedType, setSelectedType] = useState<string>("RTMP_INPUT");

  const createIngress = useAction(api.integration.livekit.createIngress);
  const deleteIngress = useAction(api.integration.livekit.deleteIngress);

  React.useEffect(() => {
    if (!stream) return;
    setServerUrl(stream.serverUrl || "");
    setStreamKey(stream.streamKey || "");
    setIngressType((stream as any)?.ingressType || "RTMP_INPUT");
    setError(null);
    setLoading(false);
  }, [stream]);

  const handleCopy = (value: string) => {
    navigator.clipboard.writeText(value);
  };

  const handleDeleteIngress = async () => {
    setDeleting(true);
    setError(null);
    try {
      await deleteIngress({ streamId });
      setServerUrl("");
      setStreamKey("");
    } catch (err: any) {
      setError(err?.message || "Failed to delete ingress");
    } finally {
      setDeleting(false);
    }
  };

  const handleCreateIngress = async () => {
    setCreating(true);
    setError(null);
    try {
      const result = await createIngress({ ingressType: selectedType });
      setServerUrl(result.serverUrl || "");
      setStreamKey(result.streamKey || "");
      setIngressType(result.ingressType || selectedType);
      setModalOpen(false);
    } catch (err: any) {
      setError(err?.message || "Failed to create ingress");
    } finally {
      setCreating(false);
    }
  };

  const hasIngress = !!stream?.ingressId;
  const hasKeys = !!serverUrl && !!streamKey;

  return (
    <Card className="bg-zinc-100 dark:bg-zinc-900 border border-input my-2">
      <CardHeader>
        <CardTitle>Streaming Info</CardTitle>
      </CardHeader>
      <CardContent>
        {loading ? (
          <div>
            <Skeleton className="h-6 w-1/2 mb-2" />
            <Skeleton className="h-6 w-1/2" />
          </div>
        ) : error ? (
          <div className="text-red-500 mb-2">{error}</div>
        ) : null}
        <div className="mb-4">
          <div className="font-semibold">Ingress Type</div>
          <div className="bg-slate-100 dark:bg-slate-800 p-2 rounded font-mono select-all">
            {ingressType === "WHIP_INPUT" ? "WHIP" : "RTMP"}
          </div>
        </div>
        <div className="mb-4">
          <div className="font-semibold">Server URL</div>
          <div className="flex items-center gap-2 bg-slate-100 dark:bg-slate-800 p-2 rounded font-mono select-all">
            <span className="flex-1">{serverUrl || <span className="text-muted-foreground">No server URL</span>}</span>
            {serverUrl && (
              <Button type="button" size="sm" onClick={() => handleCopy(serverUrl)}>Copy</Button>
            )}
          </div>
        </div>
        <div className="mb-4">
          <div className="font-semibold">Stream Key</div>
          <div className="flex items-center gap-2 bg-slate-100 dark:bg-slate-800 p-2 rounded font-mono select-all">
            <span className="flex-1">{streamKey || <span className="text-muted-foreground">No key generated yet</span>}</span>
            {streamKey && (
              <Button type="button" size="sm" onClick={() => handleCopy(streamKey)}>Copy</Button>
            )}
          </div>
        </div>
        {!hasKeys && (
          <div className="mb-4 flex gap-2">
            <Dialog open={modalOpen} onOpenChange={setModalOpen}>
              <DialogTrigger asChild>
                <Button type="button" size="sm" onClick={() => setModalOpen(true)}>
                  Generate Stream Key
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Generate Stream Key</DialogTitle>
                  <DialogDescription>
                  </DialogDescription>
                </DialogHeader>
                <div className="mb-4">
                  <Select value={selectedType} onValueChange={setSelectedType}>
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="Select Ingress Type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="RTMP_INPUT">RTMP</SelectItem>
                      <SelectItem value="WHIP_INPUT">WHIP</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="flex justify-end gap-2">
                  <Button type="button" variant="ghost" onClick={() => setModalOpen(false)}>
                    Cancel
                  </Button>
                  <Button type="button" onClick={handleCreateIngress} disabled={creating}>
                    {creating ? "Generating..." : "Generate"}
                  </Button>
                </div>
              </DialogContent>
            </Dialog>
            {hasIngress && (
              <Button type="button" size="sm" variant="destructive" onClick={handleDeleteIngress} disabled={deleting}>
                {deleting ? "Deleting..." : "Delete Ingress"}
              </Button>
            )}
          </div>
        )}
        {hasKeys && (
          <div className="mb-4">
            <Button type="button" size="sm" variant="destructive" onClick={handleDeleteIngress} disabled={deleting}>
              {deleting ? "Deleting..." : "Delete Ingress"}
            </Button>
          </div>
        )}
        <div className="mt-6">
          <div className="font-semibold mb-2">How to Stream with OBS</div>
          <ol className="list-decimal list-inside text-sm text-zinc-600 dark:text-zinc-300 space-y-1">
            <li>Open OBS Studio and go to <b>Settings</b> &rarr; <b>Stream</b>.</li>
            <li>Set <b>Service</b> to <b>Custom...</b></li>
            <li>Paste the <b>Server URL</b> and <b>Stream Key</b> above.</li>
            <li>Click <b>Start Streaming</b> in OBS.</li>
          </ol>
          <div className="font-semibold mt-4 mb-2">How to Stream from Your Phone</div>
          <ol className="list-decimal list-inside text-sm text-zinc-600 dark:text-zinc-300 space-y-1">
            <li>Download a mobile app that supports RTMP or WHIP streaming (e.g., <b>Larix Broadcaster</b>, <b>Streamlabs</b>).</li>
            <li>Paste the <b>Server URL</b> and <b>Stream Key</b> above into the app's settings.</li>
            <li>Start streaming from your phone!</li>
          </ol>
        </div>
      </CardContent>
    </Card>
  );
}

export default function SellerToolsPage() {
  const streams = useQuery(api.streams.listByUser, {});
  const [expandedStreamId, setExpandedStreamId] = useState<Id<"streams"> | null>(null);

  return (
    <div className="max-w-3xl mx-auto py-10 px-4">
      <h1 className="text-3xl font-bold mb-4">Seller Tools: OBS Setup</h1>
      <p className="mb-8 text-zinc-600 dark:text-zinc-300">
        Configure your OBS Studio and streaming widgets for your live shows. Select a show below to get your stream key.
      </p>
      <div className="space-y-8">
        <section className="bg-zinc-100 dark:bg-zinc-900 rounded-lg p-6 shadow">
          <h2 className="text-xl font-semibold mb-2">Your Shows</h2>
          {isArrayOfStreams(streams) && streams.length > 0 ? (
            <table className="w-full text-left">
              <thead>
                <tr>
                  <th className="py-2">Title</th>
                  <th className="py-2">Scheduled</th>
                  <th className="py-2">Status</th>
                  <th className="py-2">Actions</th>
                </tr>
              </thead>
              <tbody>
                {streams.map((stream: any) => (
                  <React.Fragment key={stream._id}>
                    <tr>
                      <td className="py-2">{stream.title || stream._id}</td>
                      <td className="py-2">{stream.scheduledStartTime ? new Date(stream.scheduledStartTime).toLocaleString() : "—"}</td>
                      <td className="py-2">{stream.status || "—"}</td>
                      <td className="py-2">
                        <Button
                          size="sm"
                          onClick={() => setExpandedStreamId(expandedStreamId === stream._id ? null : stream._id)}
                        >
                          {expandedStreamId === stream._id ? "Hide Key" : "Show Key"}
                        </Button>
                      </td>
                    </tr>
                    {expandedStreamId === stream._id && (
                      <tr>
                        <td colSpan={4}>
                          <div className="mt-4 mb-6">
                            <StreamKeysPanel streamId={stream._id} />
                          </div>
                        </td>
                      </tr>
                    )}
                  </React.Fragment>
                ))}
              </tbody>
            </table>
          ) : (
            <div className="text-muted-foreground">No streams found. Create a stream to get started.</div>
          )}
        </section>
        {/* You can add more sections for widgets, controls, etc. here */}
      </div>
    </div>
  );
} 