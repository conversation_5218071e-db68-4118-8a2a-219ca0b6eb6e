import { IngressInput } from "livekit-server-sdk";
import { useState, useTransition } from "react";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@workspace/ui/components/select";
import { useAction } from "convex/react";
import { api } from "@workspace/backend/convex/_generated/api";

const RTMP = String(IngressInput.RTMP_INPUT);
const WHIP = String(IngressInput.WHIP_INPUT);

type IngressType = typeof RTMP | typeof WHIP;

export const IngressConnect = () => {
  const [isPending, startTransition] = useTransition();
  const [ingressType, setIngressType] = useState<IngressType>(RTMP);
  const createIngress = useAction(api.integration.livekit.createIngress);

  const handleCreateIngress = () => {
    startTransition(() => {
      createIngress({ ingressType });
    });
  };

  return (
    <Select 
      value={ingressType} 
      onValueChange={(value) => setIngressType(value as IngressType)}
      disabled={isPending}
    >
      <SelectTrigger>
        <SelectValue placeholder="Select Ingress Type" />
      </SelectTrigger>
      <SelectContent>
        <SelectItem value={RTMP}>RTMP</SelectItem>
        <SelectItem value={WHIP}>WHIP</SelectItem>
      </SelectContent>
    </Select>
  );
};
