"use client";

import { useQuery } from "convex/react";
import { api } from "@workspace/backend/convex/_generated/api";
import { useParams } from "next/navigation";
import { redirect } from "next/navigation";
import { Id } from "@workspace/backend/convex/_generated/dataModel";

const StreamPage = () => {
  const params = useParams();
  const streamId = params?.streamId as string;

  const stream = useQuery(api.streams.getStreamById, { streamId: streamId as Id<"streams"> });

  if (!stream) {
    redirect("/");
  }

  return (
    <div>
      <span>No stream found</span>
    </div>
  );
};

export default StreamPage;
