"use client"

import { <PERSON><PERSON><PERSON>, Star, ExternalLink } from "lucide-react"
import { <PERSON><PERSON> } from "@workspace/ui/components/button"
import { Card, CardContent } from "@workspace/ui/components/card"
import { Badge } from "@workspace/ui/components/badge"
import { useDualSidebar } from "./dual-sidebar-provider"
import { cn } from "@workspace/ui/lib/utils"
import Image from "next/image"

const pinnedProducts = [
  {
    id: 1,
    name: "Gaming Headset Pro",
    price: "$89.99",
    originalPrice: "$129.99",
    image: "/placeholder.svg?height=80&width=80",
    rating: 4.8,
    discount: "30% OFF",
  },
  {
    id: 2,
    name: "RGB Mechanical Keyboard",
    price: "$149.99",
    originalPrice: "$199.99",
    image: "/placeholder.svg?height=80&width=80",
    rating: 4.9,
    discount: "25% OFF",
  },
  {
    id: 3,
    name: "Wireless Gaming Mouse",
    price: "$59.99",
    originalPrice: "$79.99",
    image: "/placeholder.svg?height=80&width=80",
    rating: 4.7,
    discount: "25% OFF",
  },
]

export function PinnedProducts() {
  const { leftSidebarOpen, rightSidebarOpen } = useDualSidebar()

  return (
    <div className={cn("absolute bottom-4 z-10 transition-all duration-300", "left-4 right-4")}>
      <div className="bg-black/80 backdrop-blur-sm rounded-lg p-4">
        <div className="flex items-center justify-between mb-3">
          <h3 className="text-white font-semibold flex items-center gap-2">
            <ShoppingCart className="h-4 w-4" />
            Featured Products
          </h3>
          <Button variant="ghost" size="sm" className="text-white hover:bg-white/20">
            View All
            <ExternalLink className="h-3 w-3 ml-1" />
          </Button>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
          {pinnedProducts.map((product) => (
            <Card
              key={product.id}
              className="bg-white/10 backdrop-blur-sm border-white/20 hover:bg-white/20 transition-colors"
            >
              <CardContent className="p-3">
                <div className="flex gap-3">
                  <Image
                    src={product.image || "/placeholder.svg"}
                    alt={product.name}
                    className="w-16 h-16 rounded-md object-cover bg-white/20"
                    width={64}
                    height={64}
                  />
                  <div className="flex-1 min-w-0">
                    <div className="flex items-start justify-between gap-2">
                      <h4 className="text-white text-sm font-medium line-clamp-2">{product.name}</h4>
                      <Badge variant="secondary" className="bg-green-600 text-white text-xs">
                        {product.discount}
                      </Badge>
                    </div>

                    <div className="flex items-center gap-1 mt-1">
                      <Star className="h-3 w-3 fill-yellow-400 text-yellow-400" />
                      <span className="text-white/80 text-xs">{product.rating}</span>
                    </div>

                    <div className="flex items-center justify-between mt-2">
                      <div className="flex items-center gap-2">
                        <span className="text-white font-semibold text-sm">{product.price}</span>
                        <span className="text-white/60 text-xs line-through">{product.originalPrice}</span>
                      </div>
                      <Button size="sm" className="h-6 px-2 text-xs">
                        Buy
                      </Button>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </div>
  )
}
