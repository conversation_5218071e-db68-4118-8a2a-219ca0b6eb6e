import { Send, Heart, Share, MoreVertical } from "lucide-react"
import { <PERSON><PERSON> } from "@workspace/ui/components/button"
import { Input } from "@workspace/ui/components/input"
import { ScrollArea } from "@workspace/ui/components/scroll-area"
import { Avatar, AvatarFallback, AvatarImage } from "@workspace/ui/components/avatar"
import { Sidebar, SidebarContent, SidebarGroup, SidebarGroupContent, SidebarHeader } from "@workspace/ui/components/sidebar"

const chatMessages = [
  { user: "StreamFan123", message: "Amazing stream! 🔥", time: "2m" },
  { user: "GamerPro", message: "How did you do that move?", time: "3m" },
  { user: "ChatMod", message: "Welcome everyone!", time: "5m" },
  { user: "ViewerX", message: "First time watching, love it!", time: "7m" },
  { user: "RegularUser", message: "Been following for months!", time: "10m" },
  { user: "NewViewer", message: "This is so cool!", time: "12m" },
]

export function RightSidebar() {
  return (
    <Sidebar side="right" className="border-l">
      <SidebarHeader>
        <div className="flex items-center justify-between p-4 border-b">
          <h3 className="font-semibold">Live Chat</h3>
          <div className="flex items-center gap-2">
            <Button variant="ghost" size="icon">
              <Heart className="h-4 w-4" />
            </Button>
            <Button variant="ghost" size="icon">
              <Share className="h-4 w-4" />
            </Button>
            <Button variant="ghost" size="icon">
              <MoreVertical className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </SidebarHeader>

      <SidebarContent>
        <SidebarGroup className="flex-1">
          <SidebarGroupContent className="flex flex-col h-full">
            <ScrollArea className="flex-1 px-4">
              <div className="space-y-4 py-4">
                {chatMessages.map((msg, index) => (
                  <div key={index} className="flex items-start gap-3">
                    <Avatar className="h-6 w-6">
                      <AvatarImage src={`/placeholder.svg?height=24&width=24`} />
                      <AvatarFallback className="text-xs">{msg.user.slice(0, 2).toUpperCase()}</AvatarFallback>
                    </Avatar>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2">
                        <span className="text-sm font-medium text-blue-600">{msg.user}</span>
                        <span className="text-xs text-muted-foreground">{msg.time}</span>
                      </div>
                      <p className="text-sm text-foreground break-words">{msg.message}</p>
                    </div>
                  </div>
                ))}
              </div>
            </ScrollArea>

            <div className="p-4 border-t">
              <div className="flex gap-2">
                <Input placeholder="Type a message..." className="flex-1" />
                <Button size="icon">
                  <Send className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>
    </Sidebar>
  )
}
