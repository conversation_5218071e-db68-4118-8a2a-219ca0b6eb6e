"use client"

import { <PERSON><PERSON>ronLeft } from "lucide-react"
import { <PERSON><PERSON> } from "@workspace/ui/components/button"
import { ScrollArea } from "@workspace/ui/components/scroll-area"
import { useDualSidebar } from "./dual-sidebar-provider"
import { cn } from "@workspace/ui/lib/utils"
import { IconBuildingStore } from "@tabler/icons-react"
import { useQuery } from "convex/react"
import { api } from "@workspace/backend/convex/_generated/api"
import { useParams } from "next/navigation"
import { Id } from "@workspace/backend/convex/_generated/dataModel"
import ProductCard from "./product-card"
import { Tabs, TabsList, TabsTrigger, TabsContent } from "@workspace/ui/components/tabs"
import { useState } from "react"
import { Elements } from "@stripe/react-stripe-js"
import { loadStripe } from "@stripe/stripe-js"
import { useCurrentUser } from "@/hooks/use-current-user"

const stripePromise = loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLISH_KEY!)

export function CollapsibleLeftSidebar() {
  const { leftSidebarOpen, toggleLeftSidebar } = useDualSidebar()
  const params = useParams()
  const { user } = useCurrentUser()

  const streamId = params.id as Id<"streams">;
  const products = useQuery(api.sellers.getProductsForStream, {
    streamId,
  })
  const stream = useQuery(api.streams.getStreamById, { streamId })
  const [tab, setTab] = useState("buy-now")
  const filteredProducts = products?.filter(product => {
    if (tab === "auction") return product.startingBid != null && product.startingBid !== undefined
    if (tab === "buy-now") return product.price != null && product.price !== undefined
    return false
  })

  const isHost = user && stream && user._id === stream.hostId;

  return (
    <div
      className={cn(
        "relative h-full bg-sidebar border-r transition-all duration-300 ease-in-out flex flex-col",
        leftSidebarOpen ? "w-80" : "w-16",
      )}
    >
      {/* Toggle Button */}
      <Button
        variant="ghost"
        size="icon"
        onClick={toggleLeftSidebar}
        className="absolute -right-3 top-6 z-10 h-6 w-6 rounded-full border dark:bg-background bg-white shadow-md hover:bg-accent"
      >
        <ChevronLeft className={cn("h-4 w-4 transition-transform dark:text-white text-black", !leftSidebarOpen && "rotate-180")} />
      </Button>

      {leftSidebarOpen ? (
        <>
          {/* Header with Tabs */}
          <div className="border-b">
            <div className="flex items-center justify-between p-4 pb-2">
              <h3 className="font-semibold dark:text-white text-black">Products</h3>
            </div>
            {/* Tab Navigation */}
            <div className="flex border-b">
              <button
                onClick={() => setTab('buy-now')}
                className={cn(
                  "flex-1 px-4 py-2 text-sm font-medium border-b-1 transition-colors",
                  tab === 'buy-now'
                    ? "border-primary text-primary"
                    : "border-transparent text-muted-foreground hover:text-foreground"
                )}
              >
                Buy it now
              </button>
              <button
                onClick={() => setTab('auction')}
                className={cn(
                  "flex-1 px-4 py-2 text-sm font-medium border-b-1 transition-colors",
                  tab === 'auction'
                    ? "border-primary text-primary"
                    : "border-transparent text-muted-foreground hover:text-foreground"
                )}
              >
                Auction
              </button>
            </div>
          </div>

          <ScrollArea className="h-full">
            <div className="p-4 space-y-6 h-full flex flex-col justify-between overflow-y-auto">
              <div>
                {filteredProducts?.map((product) => (
                  <Elements stripe={stripePromise} key={product._id}>
                    <ProductCard product={product} />
                  </Elements>
                ))}
              </div>
              {isHost && (
                <Button
                  className="w-full mt-auto"
                  variant="default"
                  onClick={() => window.open('/products/new', '_blank')}
                >
                  + Add Product
                </Button>
              )}
            </div>
          </ScrollArea>
        </>
      ) : (
        /* Collapsed State */
        <div className="flex flex-col items-center p-2 space-y-2">
          <div className="mt-8">
            <IconBuildingStore className="h-6 w-6 text-muted-foreground" />
          </div>
          <div className="text-xs text-muted-foreground writing-mode-vertical-rl text-orientation-mixed">
            Products
          </div>
        </div>
      )}
    </div>
  )
}
