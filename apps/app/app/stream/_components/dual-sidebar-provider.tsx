"use client"

import type React from "react"
import { createContext, useContext, useState } from "react"

interface DualSidebarContextType {
  leftSidebarOpen: boolean
  rightSidebarOpen: boolean
  toggleLeftSidebar: () => void
  toggleRightSidebar: () => void
  setLeftSidebarOpen: (open: boolean) => void
  setRightSidebarOpen: (open: boolean) => void
}

const DualSidebarContext = createContext<DualSidebarContextType | null>(null)

export function useDualSidebar() {
  const context = useContext(DualSidebarContext)
  if (!context) {
    throw new Error("useDualSidebar must be used within a DualSidebarProvider")
  }
  return context
}

interface DualSidebarProviderProps {
  children: React.ReactNode
  defaultLeftOpen?: boolean
  defaultRightOpen?: boolean
}

export function DualSidebarProvider({
  children,
  defaultLeftOpen = true,
  defaultRightOpen = true,
}: DualSidebarProviderProps) {
  const [leftSidebarOpen, setLeftSidebarOpen] = useState(defaultLeftOpen)
  const [rightSidebarOpen, setRightSidebarOpen] = useState(defaultRightOpen)

  const toggleLeftSidebar = () => setLeftSidebarOpen(!leftSidebarOpen)
  const toggleRightSidebar = () => setRightSidebarOpen(!rightSidebarOpen)

  return (
    <DualSidebarContext.Provider
      value={{
        leftSidebarOpen,
        rightSidebarOpen,
        toggleLeftSidebar,
        toggleRightSidebar,
        setLeftSidebarOpen,
        setRightSidebarOpen,
      }}
    >
      {children}
    </DualSidebarContext.Provider>
  )
}
