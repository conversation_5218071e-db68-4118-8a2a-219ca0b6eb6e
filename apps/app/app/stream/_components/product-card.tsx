"use client"

import { useState } from "react"
import Image from "next/image"
import { ChevronLeft, ChevronRight, ChevronDown, ChevronUp, Minus, Plus, X } from "lucide-react"
import { But<PERSON> } from "@workspace/ui/components/button"
import { Badge } from "@workspace/ui/components/badge"
import { Id } from "@workspace/backend/convex/_generated/dataModel"
import { getAvatarImageUrl } from "@/lib/utils"
import { Di<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Header, DialogTrigger, DialogContent } from "@workspace/ui/components/dialog"
import { Dialog } from "@workspace/ui/components/dialog"
import { getCategoryTitleById, getSubcategoryTitleById } from "@workspace/lib/constants/category-utils"
import { usePreloadedData } from "@/hooks/use-preloaded-data"
import { usePreloadedQuery } from "convex/react";
import { Select, SelectTrigger, SelectValue, SelectContent, SelectItem } from "@workspace/ui/components/select"
import { Input } from "@workspace/ui/components/input"
import { loadStripe } from "@stripe/stripe-js"
import { Elements, CardNumberElement, CardExpiryElement, CardCvcElement, useStripe, useElements } from "@stripe/react-stripe-js"
import { IconEdit, IconX } from "@tabler/icons-react"
import { useMutation, useAction } from "convex/react";
import { api } from "@workspace/backend/convex/_generated/api";

interface ProductCardProps {
  product: {
    _id: string
    name: string
    description?: string
    price?: number
    images?: Id<"_storage">[]
    inventory?: number
    category?: string
    status?: string
    currency?: string
    acceptOffers?: boolean
    condition?: string
    flashSale?: boolean
    hasHazardousMaterials?: boolean
    quantity?: number
    reserveForLive?: boolean
    sellerId: string
    shippingProfile?: string
    streamId?: string[]
    subcategory?: string
    tags?: string[]
    variants?: {
      quantity?: number
      color?: string
    }[]
    visibility?: "draft" | "published"
  }
}

const stripePromise = loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLISH_KEY as string)

function AddCardForm({ onSave, onCancel, loading }: { onSave: (pm: any) => void, onCancel: () => void, loading: boolean }) {
  const stripe = useStripe()
  const elements = useElements()
  const [country, setCountry] = useState("US")
  const [zip, setZip] = useState("")
  const [error, setError] = useState<string>("")

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!stripe || !elements) return
    // Simulate card save (replace with real logic as needed)
    onSave({ brand: "visa", last4: "4242", expMonth: 12, expYear: 2030, id: Math.random().toString() })
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-4 mt-4">
      <div>
        <label className="block text-sm font-medium mb-1">Card number</label>
        <div className="relative flex items-center w-full">
          <CardNumberElement className="w-full bg-transparent" />
        </div>
      </div>
      <div className="flex gap-2">
        <div className="flex-1">
          <label className="block text-sm font-medium mb-1">Expiration date</label>
          <CardExpiryElement className="w-full bg-transparent" />
        </div>
        <div className="flex-1">
          <label className="block text-sm font-medium mb-1">Security code</label>
          <CardCvcElement className="w-full dark:bg-black bg-white text-black dark:text-white" />
        </div>
      </div>
      <div className="flex gap-2">
        <div className="flex-1">
          <label className="block text-sm font-medium mb-1">Country</label>
          <Input value={country} onChange={e => setCountry(e.target.value)} />
        </div>
        <div className="flex-1">
          <label className="block text-sm font-medium mb-1">ZIP code</label>
          <Input value={zip} onChange={e => setZip(e.target.value)} />
        </div>
      </div>
      {error && <div className="text-red-600 text-sm mt-2">{error}</div>}
      <div className="flex gap-2 justify-end">
        <Button type="button" variant="outline" onClick={onCancel} disabled={loading}>Cancel</Button>
        <Button type="submit" disabled={loading}>{loading ? "Saving..." : "Save Card"}</Button>
      </div>
    </form>
  )
}

function AddAddressForm({ onSave, onCancel, loading }: { onSave: (address: any) => void, onCancel: () => void, loading: boolean }) {
  const [fullName, setFullName] = useState("")
  const [street, setStreet] = useState("")
  const [address2, setAddress2] = useState("")
  const [city, setCity] = useState("")
  const [state, setState] = useState("")
  const [zipCode, setZipCode] = useState("")
  const [country, setCountry] = useState("United States")
  const [error, setError] = useState("")

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    if (!fullName || !street || !city || !state || !zipCode) {
      setError("Please fill in all required fields.")
      return
    }
    onSave({
      id: Math.random().toString(),
      fullName,
      street,
      address2,
      city,
      state,
      zipCode,
      country,
    })
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-3 mt-4">
      <div>
        <label className="block text-sm font-medium mb-1">Full Name</label>
        <Input value={fullName} onChange={e => setFullName(e.target.value)} required />
      </div>
      <div>
        <label className="block text-sm font-medium mb-1">Address Line 1</label>
        <Input value={street} onChange={e => setStreet(e.target.value)} required />
      </div>
      <div>
        <label className="block text-sm font-medium mb-1">Address Line 2</label>
        <Input value={address2} onChange={e => setAddress2(e.target.value)} />
      </div>
      <div className="flex gap-2">
        <div className="flex-1">
          <label className="block text-sm font-medium mb-1">City</label>
          <Input value={city} onChange={e => setCity(e.target.value)} required />
        </div>
        <div className="flex-1">
          <label className="block text-sm font-medium mb-1">State</label>
          <Input value={state} onChange={e => setState(e.target.value)} required />
        </div>
      </div>
      <div className="flex gap-2">
        <div className="flex-1">
          <label className="block text-sm font-medium mb-1">ZIP Code</label>
          <Input value={zipCode} onChange={e => setZipCode(e.target.value)} required />
        </div>
        <div className="flex-1">
          <label className="block text-sm font-medium mb-1">Country</label>
          <Input value={country} onChange={e => setCountry(e.target.value)} required />
        </div>
      </div>
      {error && <div className="text-red-600 text-sm mt-2">{error}</div>}
      <div className="flex gap-2 justify-end">
        <Button type="button" variant="outline" onClick={onCancel} disabled={loading}>Cancel</Button>
        <Button type="submit" disabled={loading}>{loading ? "Saving..." : "Save Address"}</Button>
      </div>
    </form>
  )
}

export default function ProductCard({ product }: ProductCardProps) {
  const [currentImageIndex, setCurrentImageIndex] = useState(0)
  const [isDescriptionExpanded, setIsDescriptionExpanded] = useState(false)
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [isBuyNowOpen, setIsBuyNowOpen] = useState(false)
  const [quantity, setQuantity] = useState(1)
  const { preloadedUser } = usePreloadedData();
  const user = usePreloadedQuery(preloadedUser);
  const [editingPayment, setEditingPayment] = useState(false)
  const [selectedPaymentId, setSelectedPaymentId] = useState<string | null>(null)
  const [showAddCardForm, setShowAddCardForm] = useState(false)
  const [localPaymentMethods, setLocalPaymentMethods] = useState(user?.paymentMethods || [])
  const [addCardLoading, setAddCardLoading] = useState(false)
  const [editingAddress, setEditingAddress] = useState(false)
  const [selectedAddressId, setSelectedAddressId] = useState<string | null>(null)
  const [showAddAddressForm, setShowAddAddressForm] = useState(false)
  const [localAddresses, setLocalAddresses] = useState(user?.preferences?.shippingAddresses || [])
  const createOrderWithStripe = useAction(api.orders.createOrderWithStripe);
  const stripe = useStripe();
  const elements = useElements();
  const [buyNowLoading, setBuyNowLoading] = useState(false);
  const [buyNowError, setBuyNowError] = useState<string | null>(null);
  const [buyNowSuccess, setBuyNowSuccess] = useState(false);

  // Get default payment method
  const defaultPayment = localPaymentMethods.find((pm: any) => pm.isDefault) || localPaymentMethods[0];
  const selectedPayment = localPaymentMethods.find((pm: any) => pm.id === selectedPaymentId) || defaultPayment

  // Get default shipping address
  const defaultAddress = localAddresses.find((a: any) => a.isDefault) || localAddresses[0];
  const selectedAddress = localAddresses.find((a: any) => a.id === selectedAddressId) || defaultAddress

  const nextImage = () => {
    setCurrentImageIndex((prev) => (prev === (product.images?.length || 0) - 1 ? 0 : prev + 1))
  }

  const prevImage = () => {
    setCurrentImageIndex((prev) => (prev === 0 ? (product.images?.length || 0) - 1 : prev - 1))
  }

  const truncatedDescription =
    product.description?.length && product.description.length > 60 ? product.description.substring(0, 60) + "..." : product.description

  // Pricing calculations
  const subtotal = (product.price || 0) * quantity
  const shipping = 4.35
  const estTax = Math.round(subtotal * 0.265 * 100) / 100 // Example: 26.5% tax
  const total = Math.round((subtotal + shipping + estTax) * 100) / 100

  // Handler for Buy It Now
  const handleBuyNow = async () => {
    setBuyNowError(null);
    setBuyNowSuccess(false);
    if (!selectedPayment) {
      setBuyNowError("Please select a payment method.");
      return;
    }
    if (!selectedAddress) {
      setBuyNowError("Please select a shipping address.");
      return;
    }
    if (!stripe || !elements) {
      setBuyNowError("Stripe is not loaded.");
      return;
    }
    const streamId = product.streamId?.[0];
    if (!streamId) {
      setBuyNowError("This product is not available for purchase in a stream.");
      setBuyNowLoading(false);
      return;
    }
    setBuyNowLoading(true);
    try {
      const { orderId, clientSecret } = await createOrderWithStripe({
        streamId: streamId as Id<"streams">,
        productId: product._id as Id<"products">,
        quantity,
        shippingAddress: {
          street: selectedAddress.street,
          city: selectedAddress.city,
          state: selectedAddress.state,
          country: selectedAddress.country,
          zipCode: selectedAddress.zipCode,
        },
      });
      if (!clientSecret) {
        setBuyNowError("Could not initiate payment. Please try again.");
        setBuyNowLoading(false);
        return;
      }
      const result = await stripe.confirmCardPayment(clientSecret, {
        payment_method: {
          card: elements.getElement(CardNumberElement)!,
          billing_details: {
            name: selectedAddress.fullName,
            email: user?.email,
            address: {
              line1: selectedAddress.street,
              line2: selectedAddress.address2,
              city: selectedAddress.city,
              state: selectedAddress.state,
              postal_code: selectedAddress.zipCode,
            },
          },
        },
      });
      if (result.error) {
        setBuyNowError(result.error.message || "Payment failed.");
        setBuyNowLoading(false);
        return;
      }
      if (result.paymentIntent && result.paymentIntent.status === "succeeded") {
        setBuyNowSuccess(true);
        setBuyNowLoading(false);
        // Optionally close modal, refresh, etc.
      } else {
        setBuyNowError("Payment was not successful.");
        setBuyNowLoading(false);
      }
    } catch (err: any) {
      setBuyNowError(err.message || "An error occurred.");
      setBuyNowLoading(false);
    }
  };

  return (
    <>
      {/* Main Product Dialog */}
      <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
        <DialogTrigger asChild>
          <div
            className="w-full bg-muted border border-zinc-200 rounded-lg p-2 hover:bg-zinc-750 transition-colors dark:bg-zinc-800 dark:border-zinc-700 cursor-pointer"
            onClick={() => setIsModalOpen(true)}
          >
            <div className="flex gap-3">
              {/* Image Section */}
              <div className="relative flex-shrink-0">
                <div className="w-16 h-16 overflow-hidden rounded-md bg-gray-700">
                  <Image
                    src={getAvatarImageUrl(product.images?.[currentImageIndex]) || "/placeholder.svg"}
                    alt={`${product.name} - Image ${currentImageIndex + 1}`}
                    width={64}
                    height={64}
                    className="w-full h-full object-cover"
                  />
                </div>

                {product.images?.length && product.images.length > 1 && (
                  <>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="absolute -left-1 top-1/2 -translate-y-1/2 h-4 w-4 p-0 bg-black/60 hover:bg-black/80 text-white"
                      onClick={e => { e.stopPropagation(); prevImage(); }}
                    >
                      <ChevronLeft className="h-2 w-2" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="absolute -right-1 top-1/2 -translate-y-1/2 h-4 w-4 p-0 bg-black/60 hover:bg-black/80 text-white"
                      onClick={e => { e.stopPropagation(); nextImage(); }}
                    >
                      <ChevronRight className="h-2 w-2" />
                    </Button>

                    {/* Image indicators */}
                    <div className="absolute -bottom-1 left-1/2 -translate-x-1/2 flex gap-0.5">
                      {product.images?.map((_, index: number) => (
                        <button
                          key={index}
                          className={`w-1 h-1 rounded-full transition-colors ${
                            index === currentImageIndex ? "bg-white" : "bg-white/50"
                          }`}
                          onClick={e => { e.stopPropagation(); setCurrentImageIndex(index); }}
                        />
                      ))}
                    </div>
                  </>
                )}
              </div>

              {/* Content Section */}
              <div className="flex-1 min-w-0">
                <div className="flex items-start justify-between mb-1">
                  <div className="flex-1 min-w-0">
                    <h3 className="font-semibold dark:text-white text-black text-xs leading-tight truncate">{product.name}</h3>
                    <div className="flex items-center gap-2 mt-1">
                    {product.inventory && product.inventory <= 0 && (
                      <Badge variant="destructive" className="text-[10px] px-1 py-0 h-4">
                        Out
                      </Badge>
                    )}
                  </div>
                  </div>
                </div>

                {/* Description */}
                <div className="mb-2">
                  <p className="text-[10px] text-gray-400 leading-tight">
                    {isDescriptionExpanded ? product.description : truncatedDescription}
                  </p>
                  {product.description?.length && product.description.length > 60 && (
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-auto p-0 text-[10px] text-blue-400 hover:bg-transparent hover:text-blue-300"
                      onClick={e => { e.stopPropagation(); setIsDescriptionExpanded(!isDescriptionExpanded); }}
                    >
                      {isDescriptionExpanded ? (
                        <>
                          Less <ChevronUp className="ml-0.5 h-2 w-2" />
                        </>
                      ) : (
                        <>
                          More <ChevronDown className="ml-0.5 h-2 w-2" />
                        </>
                      )}
                    </Button>
                  )}
                </div>

              </div>
            </div>
            <div className="flex items-center justify-between mt-1">
              <div className="flex items-center">
                {/* Quantity */}
                <div className="flex items-center gap-1">
                  <span className="text-[10px] dark:text-gray-400 text-gray-600">Qty:</span>
                  <span className="text-[10px] dark:text-white text-black font-medium w-3 text-center">{product.quantity}</span>
                </div>
              </div>

              <span className="text-green-400 font-bold text-xs">
                {product.currency?.toUpperCase()} {product.price?.toFixed(2)}
              </span>
            </div>
            <div className="flex items-center justify-end mt-1">
              <Button className="w-fit rounded-xl" size="sm" onClick={e => { e.stopPropagation(); setIsBuyNowOpen(true) }}>
                Buy Now
              </Button>
            </div>
          </div>
        </DialogTrigger>

        <DialogContent className="min-w-4xl">
          <DialogHeader>
            <DialogTitle className="text-xl font-bold text-white">{product.name}</DialogTitle>
            {product.category && (
              <Badge className="w-fit text-sm px-2 py-1">
                {getCategoryTitleById(product.category)}
              </Badge>
            )}
          </DialogHeader>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Image Gallery */}
            <div className="space-y-4">
              <div className="relative">
                <div className="aspect-square overflow-hidden rounded-lg bg-gray-700">
                  <Image
                    src={getAvatarImageUrl(product.images?.[currentImageIndex]) || "/placeholder.svg"}
                    alt={`${product.name} - Image ${currentImageIndex + 1}`}
                    width={300}
                    height={300}
                    className="w-full h-full object-cover"
                  />
                </div>

                {product.images?.length && product.images.length > 1 && (
                  <>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="absolute left-2 top-1/2 -translate-y-1/2 h-8 w-8 p-0 bg-black/60 hover:bg-black/80 text-white"
                      onClick={prevImage}
                    >
                      <ChevronLeft className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="absolute right-2 top-1/2 -translate-y-1/2 h-8 w-8 p-0 bg-black/60 hover:bg-black/80 text-white"
                      onClick={nextImage}
                    >
                      <ChevronRight className="h-4 w-4" />
                    </Button>
                  </>
                )}
              </div>

              {/* Image Thumbnails */}
              {product.images?.length && product.images.length > 1 && (
                <div className="flex gap-2 justify-center">
                  {product.images?.map((image, index) => (
                    <button
                      key={index}
                      className={`w-16 h-16 rounded-md overflow-hidden border-2 transition-colors ${
                        index === currentImageIndex ? "border-blue-500" : "border-gray-600"
                      }`}
                      onClick={() => setCurrentImageIndex(index)}
                    >
                      <Image
                        src={image || "/placeholder.svg"}
                        alt={`${product.name} thumbnail ${index + 1}`}
                        width={64}
                        height={64}
                        className="w-full h-full object-cover"
                      />
                    </button>
                  ))}
                </div>
              )}
            </div>

            {/* Product Details */}
            <div className="space-y-6">
              {/* Description */}
              <div>
                <h3 className="text-lg font-semibold mb-2 text-white">Description</h3>
                <p className="text-gray-300 leading-relaxed">{product.description}</p>
              </div>

              {/* Price */}
              <div>
                <span className="text-3xl font-bold text-green-400">${product.price?.toFixed(2)}</span>
                {!product.inventory || product.inventory <= 0 && (
                  <Badge variant="destructive" className="ml-3">
                    Out of Stock
                  </Badge>
                )}
              </div>

              {/* Quantity Selector */}
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-300">Quantity</label>
                <div className="flex items-center">
                  <span className="text-lg font-medium text-white text-center">{product.quantity}</span>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="space-y-3">
                <Button
                  className="w-full rounded-xl"
                  disabled={!product.inventory || product.inventory <= 0}
                  size="lg"
                >
                  {product.inventory && product.inventory > 0 ? `Buy Now` : "Out of Stock"}
                </Button>
              </div>

              {/* Product Info */}
              <div className="pt-4 border-t border-gray-700">
                <div className="space-y-2 text-sm text-gray-400">
                  <div className="flex justify-between">
                    <span>Product ID:</span>
                    <span className="text-white">{product._id}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Availability:</span>
                    <span className={product.inventory && product.inventory > 0 ? "text-green-400" : "text-red-400"}>
                      {product.inventory && product.inventory > 0 ? "In Stock" : "Out of Stock"}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span>Category:</span>
                    <span className="text-white">{product.category ? getCategoryTitleById(product.category) : "N/A"}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Subcategory:</span>
                    <span className="text-white">{product.subcategory ? getSubcategoryTitleById(product.category || "", product.subcategory) : "N/A"}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      <Dialog open={isBuyNowOpen} onOpenChange={setIsBuyNowOpen}>
        <DialogContent className="min-w-4xl">
          <div className="p-6">
            {/* Product Info */}
            <div className="flex items-center gap-4 mb-4">
              <div className="flex-1">
                <h2 className="text-lg font-bold mb-1">{product.name}</h2>
                <div className="flex items-center gap-2 text-blue-400 text-xs font-medium">
                  <span className="h-2 w-2 rounded-full bg-blue-400 inline-block mr-1" />
                  {product.inventory && product.inventory <= 4 ? `Only` : ''} {product.inventory && product.inventory > 0 ? `${product.inventory} Available` : "Out of Stock"}
                </div>
              </div>
              <div className="w-16 h-16 rounded-md overflow-hidden bg-gray-800 flex items-center justify-center">
                <Image
                  src={getAvatarImageUrl(product.images?.[0]) || "/placeholder.svg"}
                  alt={product.name}
                  width={64}
                  height={64}
                  className="object-cover w-full h-full"
                />
              </div>
            </div>

            {/* Quantity Selector */}
            <div className="mb-6">
              <label className="block text-sm font-medium mb-1">Quantity</label>
              <div className="flex items-center border border-zinc-700 rounded-lg overflow-hidden bg-zinc-900 w-fit">
                <button
                  className="px-3 py-2 text-lg text-white disabled:text-zinc-600"
                  onClick={() => setQuantity(q => Math.max(1, q - 1))}
                  disabled={quantity <= 1}
                  type="button"
                >
                  <Minus className="h-4 w-4" />
                </button>
                <input
                  type="number"
                  min={1}
                  max={product.inventory || 99}
                  value={quantity}
                  onChange={e => setQuantity(Math.max(1, Math.min(Number(e.target.value), product.inventory || 99)))}
                  className="w-12 text-center bg-transparent text-white text-lg font-semibold outline-none border-0"
                />
                <button
                  className="px-3 py-2 text-lg text-white disabled:text-zinc-600"
                  onClick={() => setQuantity(q => Math.min((product.inventory || 99), q + 1))}
                  disabled={quantity >= (product.inventory || 99)}
                  type="button"
                >
                  <Plus className="h-4 w-4" />
                </button>
              </div>
            </div>

            {/* Payment Info */}
            <div className="mb-6">
              <h3 className="text-xl font-bold mb-1">Payment Info</h3>
              <p className="text-xs text-zinc-400 mb-2">This is required in order to place a bid, order or buy a product on a livestream. We charge your card if a bid or offer is accepted.</p>
              <div className="flex items-center justify-between border-b border-zinc-700 py-2">
                <div className="flex items-center gap-2 w-full">
                <span className="text-sm font-medium">Payment</span>

                {!editingPayment ? (
                  <span className="text-sm text-white border border-zinc-700 rounded-lg px-2 py-1">
                    {selectedPayment
                      ? `${selectedPayment.brand?.toUpperCase() || "CARD"} •••• ${selectedPayment.last4} - ${selectedPayment.expMonth}/${selectedPayment.expYear}`
                      : <span className="text-red-400">No payment method set</span>
                    }
                  </span>
                ) : (
                  <div className="flex flex-col w-full">
                    <Select
                      value={selectedPaymentId || ""}
                      onValueChange={val => {
                        if (val === "add_new") {
                          setShowAddCardForm(true)
                        } else {
                          setSelectedPaymentId(val)
                          setShowAddCardForm(false)
                          setEditingPayment(false)
                        }
                      }}
                    >
                      <SelectTrigger className="w-full bg-black text-white">
                        <SelectValue placeholder="Select An Existing Card" />
                      </SelectTrigger>
                      <SelectContent>
                        {localPaymentMethods.map((pm: any) => (
                          <SelectItem key={pm.id} value={pm.id}>
                            {pm.brand?.toUpperCase() || "CARD"} •••• {pm.last4} - {pm.expMonth}/{pm.expYear}
                          </SelectItem>
                        ))}
                        <SelectItem value="add_new">Add New Card</SelectItem>
                      </SelectContent>
                    </Select>
                    {showAddCardForm && (
                      <Elements stripe={stripePromise}>
                        <AddCardForm
                          loading={addCardLoading}
                          onCancel={() => setShowAddCardForm(false)}
                          onSave={pm => {
                            setAddCardLoading(true)
                            setTimeout(() => {
                              setLocalPaymentMethods((prev: any[]) => [...prev, pm])
                              setSelectedPaymentId(pm.id)
                              setShowAddCardForm(false)
                            }, 1000)
                          }}
                        />
                      </Elements>
                    )}
                  </div>
                )}
                </div>
                <Button variant="ghost" size="icon" className="ml-2 text-blue-400 hover:bg-transparent" onClick={() => setEditingPayment(e => !e)}>
                  {editingPayment ? <IconX className="h-4 w-4" /> : <IconEdit className="h-4 w-4" />}
                </Button>
              </div>
              <div className="flex items-center justify-between border-b border-zinc-700 py-2">
                <div className="flex items-start gap-2 w-full">
                  <span className="text-sm font-medium">Shipping</span>
                  {!editingAddress ? (
                    <span className="text-sm text-white border border-zinc-700 rounded-lg px-2 py-1">
                      {selectedAddress
                        ? <>{selectedAddress.fullName}<br />{selectedAddress.street}{selectedAddress.address2 ? `, ${selectedAddress.address2}` : ""}<br />{selectedAddress.city}, {selectedAddress.state} {selectedAddress.zipCode}<br />{selectedAddress.country}</>
                        : <span className="text-red-400">No shipping address set</span>
                      }
                    </span>
                  ) : (
                    <div className="flex flex-col w-full">
                      <Select
                        value={selectedAddressId || ""}
                        onValueChange={val => {
                          if (val === "add_new") {
                            setShowAddAddressForm(true)
                          } else {
                            setSelectedAddressId(val)
                            setShowAddAddressForm(false)
                            setEditingAddress(false)
                          }
                        }}
                      >
                        <SelectTrigger className="w-full bg-black text-white">
                          <SelectValue placeholder="Select An Existing Address" />
                        </SelectTrigger>
                        <SelectContent>
                          {localAddresses.map((a: any) => (
                            <SelectItem key={a.id} value={a.id}>
                              {a.fullName} - {a.street}, {a.city}, {a.state} {a.zipCode}
                            </SelectItem>
                          ))}
                          <SelectItem value="add_new">Add New Address</SelectItem>
                        </SelectContent>
                      </Select>
                      {showAddAddressForm && (
                        <AddAddressForm
                          loading={false}
                          onCancel={() => setShowAddAddressForm(false)}
                          onSave={address => {
                            setLocalAddresses((prev: any[]) => [...prev, address])
                            setSelectedAddressId(address.id)
                            setShowAddAddressForm(false)
                          }}
                        />
                      )}
                    </div>
                  )}
                </div>
                <Button variant="ghost" size="icon" className="ml-2 text-blue-400 hover:bg-transparent" onClick={() => setEditingAddress(e => !e)}>
                  {editingAddress ? <IconX className="h-4 w-4" /> : <IconEdit className="h-4 w-4" />}
                </Button>
              </div>
            </div>

            {/* Summary */}
            <div className="mb-6">
              <div className="flex justify-between text-sm mb-1">
                <span>Subtotal</span>
                <span>${subtotal.toFixed(2)}</span>
              </div>
              <div className="flex justify-between text-sm mb-1">
                <span>Shipping</span>
                <span>${shipping.toFixed(2)}</span>
              </div>
              <div className="flex justify-between text-sm mb-1">
                <span>Est Tax</span>
                <span>${estTax.toFixed(2)}</span>
              </div>
              <div className="flex justify-between text-lg font-bold border-t border-zinc-700 pt-2 mt-2">
                <span>Total</span>
                <span>${total.toFixed(2)}</span>
              </div>
            </div>

            {/* Buy It Now Button */}
            {buyNowError && <div className="text-red-500 text-sm mb-2">{buyNowError}</div>}
            {buyNowSuccess && <div className="text-green-500 text-sm mb-2">Purchase successful!</div>}
            <Button
              className="w-full py-3 rounded-full text-lg font-bold bg-blue-500 text-white hover:bg-blue-300 transition-colors"
              onClick={handleBuyNow}
              disabled={buyNowLoading || !selectedPayment || !selectedAddress || !product.streamId?.[0]}
            >
              {buyNowLoading ? "Processing..." : "Buy It Now"}
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </>
  )
}
