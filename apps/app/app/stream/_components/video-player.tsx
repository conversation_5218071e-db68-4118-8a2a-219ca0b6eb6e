import { Play, Pause, Volume2, Maximize, } from "lucide-react"
import { <PERSON><PERSON> } from "@workspace/ui/components/button"
import { Badge } from "@workspace/ui/components/badge"
import { IconMinimize, IconVolume3 } from "@tabler/icons-react"
import { useState } from "react"
import { useViewerCount, formatViewerCount } from "@/hooks/use-viewer-count"

type VideoPlayerProps = {
  videoContainerRef: React.RefObject<HTMLDivElement | null>
  videoRef: React.RefObject<HTMLVideoElement | null>
  audioRef: React.RefObject<HTMLAudioElement | null>
  isHost: boolean
  isConnected: boolean
  isLive: boolean
  isMuted: boolean
  volume: number
  toggleMute: () => void
  streamDetails: any
  handleVolumeChange: (event: React.ChangeEvent<HTMLInputElement>) => void
  toggleFullscreen: () => void
  isFullscreen: boolean
}

export function VideoPlayer({ 
  videoContainerRef, 
  videoRef, 
  audioRef, 
  isHost, 
  isConnected, 
  isLive, 
  isMuted, 
  volume, 
  toggleMute, 
  streamDetails, 
  handleVolumeChange, 
  isFullscreen,
  toggleFullscreen
 }: VideoPlayerProps) {
  const [isPlaying, setIsPlaying] = useState(true)

  const handlePlayPause = () => {
    const video = videoRef.current
    if (!video) return
    if (video.paused) {
      video.play()
      setIsPlaying(true)
    } else {
      video.pause()
      setIsPlaying(false)
    }
  }

  const { viewerCount, isLoading } = useViewerCount({
    streamId: streamDetails?._id,
    isLive,
    updateInterval: 15000,
    enabled: Boolean(streamDetails?._id && isLive),
  })

  return (
    <div ref={videoContainerRef} className="relative w-full aspect-video bg-black overflow-hidden group">
      {/* Video placeholder */}
      <video ref={videoRef} autoPlay playsInline className="w-full h-full object-contain"></video>
      {/* Check to see if full track attached */}
      <audio ref={audioRef} autoPlay playsInline className="hidden"></audio>
      {/* Live indicator */}
      <div className="absolute top-4 left-4">
        <Badge variant="destructive" className="bg-red-600 hover:bg-red-600">
          🔴 LIVE
        </Badge>
      </div>

      {/* Viewer count */}
      <div className="absolute top-4 right-4">
        <Badge variant="secondary" className="bg-black/50 text-white">
          {isLoading ? "..." : formatViewerCount(viewerCount)} viewers
        </Badge>
      </div>

      {/* Video controls */}
      <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none group-hover:pointer-events-auto">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Button variant="ghost" size="icon" className="text-white hover:bg-white/20" onClick={handlePlayPause}>
              {isPlaying ? <Pause className="h-5 w-5" /> : <Play className="h-5 w-5" />}
            </Button>
            <Button variant="ghost" size="icon" className="text-white hover:bg-white/20" onClick={toggleMute}>
              {isMuted ? <IconVolume3 size={20} /> : <Volume2 className="h-5 w-5" />}
            </Button>
            <input
              type="range"
              min="0"
              max="1"
              step="0.05"
              value={isMuted ? 0 : volume}
              onChange={handleVolumeChange}
              className="w-20 h-1.5 accent-primary cursor-pointer"
            />
          </div>

          <div className="flex items-center gap-2">
            <Button variant="ghost" size="icon" className="text-white hover:bg-white/20" onClick={toggleFullscreen}>
              {isFullscreen ? <IconMinimize size={20} /> : <Maximize className="h-5 w-5" />}
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}
