import Link from "next/link";

export default function LegalPage() {
  const legalDocuments = [
    {
      title: "Terms of Service",
      description:
        "General terms and conditions for using Liveciety's services",
      href: "/legal/terms-of-service",
    },
    {
      title: "Privacy Policy",
      description: "How we collect, use, and protect your personal information",
      href: "/legal/privacy-policy",
    },
    {
      title: "Community Guidelines",
      description: "Rules and standards for participating in our community",
      href: "/legal/community-guidelines",
    },
    {
      title: "Buyer Policy",
      description: "Policies and guidelines for buyers using our platform",
      href: "/legal/buyer-policy",
    },
    {
      title: "Seller Policy",
      description: "Rules and requirements for sellers on our platform",
      href: "/legal/seller-policy",
    },
    {
      title: "Seller Terms of Service",
      description: "Specific terms and conditions for sellers",
      href: "/legal/seller-terms-of-service",
    },
    {
      title: "Return, Refund & Cancellation Policy",
      description: "Guidelines for returns, refunds, and order cancellations",
      href: "/legal/return-refund-cancellation",
    },
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto py-16 px-4 sm:px-6 lg:px-8">
        <div className="text-center">
          <h1 className="text-4xl font-extrabold text-gray-900 sm:text-5xl sm:tracking-tight lg:text-6xl">
            Legal Information
          </h1>
          <p className="max-w-xl mt-5 mx-auto text-xl text-gray-500">
            Important policies, terms, and guidelines that help keep our
            community safe and transparent
          </p>
        </div>

        <div className="mt-16">
          <div className="grid gap-6 lg:grid-cols-2">
            {legalDocuments.map((doc) => (
              <Link
                key={doc.href}
                href={doc.href}
                className="relative group rounded-lg border border-gray-200 bg-white p-6 hover:border-gray-300 transition-all duration-200 shadow-sm hover:shadow"
              >
                <div>
                  <h3 className="text-xl font-semibold text-gray-900 group-hover:text-indigo-600 transition-colors duration-200">
                    {doc.title}
                  </h3>
                  <p className="mt-2 text-base text-gray-500">
                    {doc.description}
                  </p>
                </div>
                <span className="absolute inset-0" aria-hidden="true" />
              </Link>
            ))}
          </div>
        </div>

        <div className="mt-16 text-center">
          <p className="text-base text-gray-500">
            Last updated: May 1, 2025 •{" "}
            <a
              href="mailto:<EMAIL>"
              className="text-indigo-600 hover:text-indigo-500"
            >
              Contact Legal Team
            </a>
          </p>
        </div>
      </div>
    </div>
  );
}
