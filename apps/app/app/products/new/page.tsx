"use client";

import type React from "react";
import { useState, useCallback } from "react";
import { Upload, X, Plus, ImageIcon, Trash2 } from "lucide-react";
import { useAction, useMutation, useQuery } from "convex/react";
import { api } from "@workspace/backend/convex/_generated/api";
import { toast } from "sonner";
import { <PERSON><PERSON> } from "@workspace/ui/components/button";
import { Card, CardContent, CardHeader, CardTitle } from "@workspace/ui/components/card";
import { Input } from "@workspace/ui/components/input";
import { Label } from "@workspace/ui/components/label";
import { Switch } from "@workspace/ui/components/switch";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@workspace/ui/components/tabs";
import { Textarea } from "@workspace/ui/components/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@workspace/ui/components/select";
import { Id } from "@workspace/backend/convex/_generated/dataModel";
import Image from "next/image";
import { getAvatarImageUrl } from "@/lib/utils";
import { categories, subcategories, Subcategory } from "@workspace/lib/constants/categories";
import { cn } from "@workspace/ui/lib/utils";
import { getSubcategoryTitleById, getCategoryTitleById } from "@workspace/lib/constants/category-utils";

export default function NewProductPage() {
  const user = useQuery(api.users.viewer);
  const addProduct = useMutation(api.sellers.addProduct);
  const generateUploadUrl = useMutation(api.files.generateUploadUrl);
  const deleteFileById = useAction(api.files.deleteById);
  const streams = useQuery(api.sellers.getActiveStreams);

  const [uploadedImages, setUploadedImages] = useState<Id<"_storage">[]>([]);
  const [isDragOver, setIsDragOver] = useState(false);
  const [pricingType, setPricingType] = useState("buy-now");
  const [flashSale, setFlashSale] = useState(false);
  const [acceptOffers, setAcceptOffers] = useState(true);
  const [reserveForLive, setReserveForLive] = useState(true);
  const [variantsEnabled, setVariantsEnabled] = useState(true);
  const [variants, setVariants] = useState([
    { id: 1, title: "", quantity: "" },
    { id: 2, title: "", quantity: "" },
  ]);
  const [form, setForm] = useState<{
    title: string;
    description: string;
    price: string;
    startingBid: string;
    shippingProfile: string;
    hazardousMaterials: string;
    category: string;
    subcategory: string;
    quantity: string;
    costPerItem: string;
    sku: string;
    streamId: Id<"streams">[];
    visibility: string;
  }>({
    title: "",
    description: "",
    price: "",
    startingBid: "",
    shippingProfile: "",
    hazardousMaterials: "none",
    category: "",
    subcategory: "",
    quantity: "",
    costPerItem: "",
    sku: "",
    streamId: [],
    visibility: "draft",
  });
  const [loading, setLoading] = useState(false);
  const categoryOptions = categories.map(cat => ({
    value: cat.id,
    label: cat.title,
  }));
  const sellerCategories = user?.sellerProfile?.categories || [];
  const singleCategory = sellerCategories.length === 1 ? sellerCategories[0] : undefined;
  const selectedCategory = form.category || singleCategory;
  const availableSubcategories = (selectedCategory && subcategories[selectedCategory])
    ? subcategories[selectedCategory]
    : [];

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  }, []);

  const handleFileUpload = async (file: File) => {
    try {
      let imageId: Id<"_storage"> | undefined = undefined;
      if (file) {
        try {
          const uploadUrl = await generateUploadUrl();
          const uploadResponse = await fetch(uploadUrl, {
            method: "POST",
            headers: { "Content-Type": file.type },
            body: file,
          });
          const { storageId } = await uploadResponse.json();
          imageId = storageId;
        } catch (error) {
          console.error("Image upload failed:", error);
          toast.error("Image upload failed. Please try again.");
          return;
        }
      }

      setUploadedImages((prev) => [...prev, imageId as Id<"_storage">]);
    } catch (err) {
      toast.error("Failed to upload image");
      console.error(err);
    }
  };

  const handleDrop = useCallback(async (e: React.DragEvent) => {
    e.preventDefault();
    const files = Array.from(e.dataTransfer.files);
    for (const file of files) {
      if (file.type.startsWith("image/")) {
        await handleFileUpload(file);
      }
    }
  }, [generateUploadUrl]);

  const handleFileSelect = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    for (const file of files) {
      if (file.type.startsWith("image/")) {
        await handleFileUpload(file);
      }
    }
    e.target.value = "";
  };

  const removeImage = async (index: number) => {
    const storageId = uploadedImages[index];
    setUploadedImages((prev) => prev.filter((_, i) => i !== index));
    if (storageId) {
      try {
        await deleteFileById({ storageId: storageId as Id<"_storage"> });
      } catch (err) {
        toast.error("Failed to delete image");
      }
    }
  };

  const addVariant = () => {
    const newId = Math.max(...variants.map((v) => v.id)) + 1;
    setVariants([...variants, { id: newId, title: "", quantity: "" }]);
  };

  const removeVariant = (id: number) => {
    setVariants(variants.filter((v) => v.id !== id));
  };

  const updateVariant = (id: number, field: string, value: string) => {
    setVariants(variants.map((v) => (v.id === id ? { ...v, [field]: value } : v)));
  };

  const handleFormChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setForm((prev) => ({ ...prev, [name]: value }));
  };

  const handleSelectChange = (name: string, value: string) => {
    setForm((prev) => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    console.log("Submitting form", form);
    
    try {
      await addProduct({
        name: form.title,
        description: form.description,
        price: pricingType === "buy-now" && form.price && !isNaN(Number(form.price)) ? Number(form.price) : 0,
        currency: "usd",
        flashSale,
        acceptOffers,
        reserveForLive,
        shippingProfile: form.shippingProfile,
        hasHazardousMaterials: form.hazardousMaterials !== "none",
        category: (form.category || singleCategory) ?? "",
        subcategory: form.subcategory ?? "",
        streamId: form.streamId ?? [],
        images: uploadedImages as Id<"_storage">[],
        variants: variantsEnabled
          ? variants.map((v) => {
              const obj: { quantity?: number; color?: string } = {};
              if (v.quantity && !isNaN(Number(v.quantity))) obj.quantity = Number(v.quantity);
              if (v.title) obj.color = v.title;
              return obj;
            })
          : [],
        condition: "new",
        inventory: form.quantity && !isNaN(Number(form.quantity)) ? Number(form.quantity) : 1,
        ...(form.quantity && !isNaN(Number(form.quantity)) ? { quantity: Number(form.quantity) } : {}),
        ...(pricingType === "auction" && form.startingBid && !isNaN(Number(form.startingBid)) ? { startingBid: Number(form.startingBid) } : {}),
        visibility: "published",
      });
      toast.success("Product created!");
      setForm({
        title: "",
        description: "",
        price: "",
        startingBid: "",
        shippingProfile: "",
        hazardousMaterials: "none",
        category: "",
        subcategory: "",
        quantity: "",
        costPerItem: "",
        sku: "",
        streamId: [],
        visibility: "draft",
      });
      setUploadedImages([]);
      setVariants([
        { id: 1, title: "", quantity: "" },
        { id: 2, title: "", quantity: "" },
      ]);
    } catch (err: any) {
      toast.error(err?.message || "Failed to create product");
    } finally {
      setLoading(false);
    }
  };

  const handleSaveAsDraft = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    try {
      await addProduct({
        name: form.title,
        description: form.description,
        price: pricingType === "buy-now" && form.price && !isNaN(Number(form.price)) ? Number(form.price) : 0,
        currency: "usd",
        flashSale,
        acceptOffers,
        reserveForLive,
        shippingProfile: form.shippingProfile,
        hasHazardousMaterials: form.hazardousMaterials !== "none",
        category: (form.category || singleCategory) ?? "",
        subcategory: form.subcategory ?? "",
        streamId: form.streamId ?? [],
        images: uploadedImages as Id<"_storage">[],
        variants: variantsEnabled
          ? variants.map((v) => {
              const obj: { quantity?: number; color?: string } = {};
              if (v.quantity && !isNaN(Number(v.quantity))) obj.quantity = Number(v.quantity);
              if (v.title) obj.color = v.title;
              return obj;
            })
          : [],
        condition: "new",
        inventory: form.quantity && !isNaN(Number(form.quantity)) ? Number(form.quantity) : 1,
        ...(form.quantity && !isNaN(Number(form.quantity)) ? { quantity: Number(form.quantity) } : {}),
        ...(pricingType === "auction" && form.startingBid && !isNaN(Number(form.startingBid)) ? { startingBid: Number(form.startingBid) } : {}),
        visibility: "draft",
      });
      toast.success("Product saved as draft!");
    } catch (err: any) {
      toast.error(err?.message || "Failed to save product as draft");
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    setForm({
      title: "",
      description: "",
      price: "",
      startingBid: "",
      shippingProfile: "",
      hazardousMaterials: "none",
      category: "",
      subcategory: "",
      quantity: "",
      costPerItem: "",
      sku: "",
      streamId: [],
      visibility: "draft",
    });
    setUploadedImages([]);
    setVariants([
      { id: 1, title: "", quantity: "" },
      { id: 2, title: "", quantity: "" },
    ]);
  };

  return (
    <div className="min-h-screen p-4">
      <form onSubmit={handleSubmit}>
        <div className="max-w-7xl mx-auto">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Left Column: Media + Product Details + Shows */}
            <div className="flex flex-col gap-6">
              {/* Media Section */}
              <Card className="h-fit">
                <CardHeader>
                  <CardTitle>Media</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div
                    className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
                      isDragOver ? "border-blue-500 bg-blue-50 dark:bg-blue-950" : "border-zinc-300 hover:border-zinc-400 dark:border-zinc-700 dark:hover:border-zinc-600"
                    }`}
                    onDragOver={handleDragOver}
                    onDragLeave={handleDragLeave}
                    onDrop={handleDrop}
                  >
                    <div className="flex flex-col items-center space-y-4">
                      <div className="w-16 h-16 bg-gray-100 dark:bg-zinc-900 rounded-lg flex items-center justify-center">
                        <Plus className="w-8 h-8 text-gray-400 dark:text-zinc-400" />
                      </div>
                      <div>
                        <p className="text-lg font-medium text-gray-900 dark:text-zinc-100">Add Photo</p>
                        <p className="text-sm text-gray-500 dark:text-zinc-400 mt-1">Drag and drop images here or click to browse</p>
                      </div>
                      <input
                        type="file"
                        multiple
                        accept="image/*"
                        onChange={handleFileSelect}
                        className="hidden"
                        id="file-upload"
                      />
                      <label htmlFor="file-upload">
                        <Button variant="outline" className="cursor-pointer" asChild>
                          <span>
                            <Upload className="w-4 h-4 mr-2" />
                            Choose Files
                          </span>
                        </Button>
                      </label>
                    </div>
                  </div>

                  {/* Uploaded Images */}
                  {uploadedImages.length > 0 && (
                    <div className="grid grid-cols-2 sm:grid-cols-3 gap-4">
                      {uploadedImages.map((storageId, index) => (
                        <div key={index} className="relative group">
                          <Image
                            src={getAvatarImageUrl(storageId) || "/placeholder.svg"}
                            alt={`Upload ${index + 1}`}
                            className="w-full h-24 object-cover rounded-lg border"
                            width={100}
                            height={100}
                            unoptimized
                          />
                          <button
                            type="button"
                            onClick={() => removeImage(index)}
                            className="absolute -top-2 -right-2 w-6 h-6 bg-red-500 text-white rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity"
                          >
                            <X className="w-4 h-4" />
                          </button>
                        </div>
                      ))}
                    </div>
                  )}

                  {/* <div className="flex items-center space-x-2 p-4 bg-gray-50 dark:bg-zinc-900 rounded-lg">
                    <ImageIcon className="w-5 h-5 text-gray-400" />
                    <div>
                      <p className="text-sm font-medium text-gray-900 dark:text-zinc-100">Mobile Upload</p>
                      <p className="text-xs text-gray-500 dark:text-zinc-400">Upload photos and video directly from your phone.</p>
                    </div>
                    <Button variant="outline" size="sm" className="ml-auto">
                      Try It
                    </Button>
                  </div> */}
                </CardContent>
              </Card>

              {/* Product Details Section */}
              <Card className="mt-0">
                <CardHeader>
                  <CardTitle>Product Details</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className={cn(
                    "grid gap-4",
                    user?.sellerProfile?.categories?.length && user.sellerProfile.categories.length > 1 ? "grid-cols-2" : "grid-cols-1"
                  )}>
                    {user?.sellerProfile?.categories?.length && user.sellerProfile.categories.length > 1 && (
                      <div>
                        <Label htmlFor="category" className="text-sm text-gray-600">
                          Category <span className="text-red-500">*</span>
                        </Label>
                        <Select value={getCategoryTitleById(form.category) || ""} onValueChange={v => {
                          handleSelectChange("category", v);
                          handleSelectChange("subcategory", "");
                        }}>
                          <SelectTrigger className="mt-1 w-full">
                            <SelectValue placeholder="Select category" />
                          </SelectTrigger>
                          <SelectContent>
                            {categoryOptions.map(cat => (
                              <SelectItem key={cat.label} value={cat.label}>{cat.label}</SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                    )}
                    {user?.sellerProfile?.categories?.length === 1 && (
                      <div>
                        <Label htmlFor="category" className="text-sm text-gray-600">
                          Category <span className="text-red-500">*</span>
                        </Label>
                        <Input
                          id="category"
                          name="category"
                          value={getCategoryTitleById(singleCategory || "")}
                          readOnly
                          className="mt-1 w-full bg-gray-100 dark:bg-zinc-900 cursor-not-allowed"
                        />
                      </div>
                    )}
                    <div>
                      <Label htmlFor="subcategory" className="text-sm text-gray-600">
                        Subcategory
                      </Label>
                      <Select value={getSubcategoryTitleById(form.category, form.subcategory) || ""} onValueChange={v => handleSelectChange("subcategory", v)}>
                        <SelectTrigger className="mt-1 w-full">
                          <SelectValue placeholder="Select subcategory" />
                        </SelectTrigger>
                        <SelectContent>
                          {availableSubcategories.map((option: Subcategory) => (
                            <SelectItem key={option.id} value={option.id}>{option.title}</SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div>
                    <Label htmlFor="quantity" className="text-sm text-gray-600">
                      Quantity <span className="text-red-500">*</span>
                    </Label>
                    <Input id="quantity" name="quantity" type="number" placeholder="1" className="mt-1" value={form.quantity} onChange={handleFormChange} />
                  </div>

                  <div>
                    <Label htmlFor="title" className="text-sm text-gray-600">
                      Title <span className="text-red-500">*</span>
                    </Label>
                    <Input id="title" name="title" placeholder="Enter product title" className="mt-1" value={form.title} onChange={handleFormChange} />
                  </div>

                  <div>
                    <Label htmlFor="description" className="text-sm text-gray-600">
                      Description <span className="text-red-500">*</span>
                    </Label>
                    <Textarea id="description" name="description" placeholder="Enter product description" rows={4} className="mt-1" value={form.description} onChange={handleFormChange} />
                  </div>
                </CardContent>
              </Card>

              {/* Streams Card */}
              <Card>
                <CardHeader>
                  <CardTitle>Streams</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center justify-between mb-4">
                      <div>
                        <p className="font-medium text-gray-900 dark:text-zinc-100">Reserve for Live</p>
                        <p className="text-sm text-gray-500 dark:text-zinc-400">
                          Turn this on to make this product only purchasable within a show.
                        </p>
                      </div>
                      <Switch checked={reserveForLive} onCheckedChange={setReserveForLive} />
                    </div>
                    {reserveForLive && (
                      <div>
                        <Label htmlFor="streams" className="text-sm text-gray-600 dark:text-zinc-400">
                          Streams
                        </Label>
                        <Select>
                          <SelectTrigger className="mt-1 bg-white border-gray-300 dark:bg-zinc-900 dark:border-zinc-700 w-full">
                            <div className="truncate">
                              {form.streamId.length === 0
                                ? "Select streams"
                                : form.streamId.length === 1
                                  ? streams?.find(s => s._id === form.streamId[0])?.title || "Select streams"
                                  : `${form.streamId.length} streams selected`}
                            </div>
                          </SelectTrigger>
                          <SelectContent>
                            <div className="p-2">
                              <div 
                                className="text-blue-600 text-sm font-medium mb-2 cursor-pointer hover:underline"
                                onClick={() => {
                                  const allSelected = streams?.every(stream => form.streamId.includes(stream._id));
                                  setForm(prev => ({
                                    ...prev,
                                    streamId: allSelected ? [] : streams?.map(stream => stream._id) || []
                                  }));
                                }}
                              >
                                {streams?.every(stream => form.streamId.includes(stream._id)) ? 'Deselect all' : 'Select all'}
                              </div>
                              <div className="space-y-2">
                                {streams?.map((stream) => (
                                  <div key={stream._id} className="flex items-center space-x-2">
                                    <input
                                      type="checkbox"
                                      id={stream._id}
                                      className="rounded border-gray-300"
                                      checked={form.streamId.includes(stream._id)}
                                      onChange={(e) => {
                                        setForm(prev => ({
                                          ...prev,
                                          streamId: e.target.checked 
                                            ? [...prev.streamId, stream._id]
                                            : prev.streamId.filter(id => id !== stream._id)
                                        }));
                                      }}
                                    />
                                    <label htmlFor={stream._id} className="text-sm">
                                      <div className="font-medium">{stream.title}</div>
                                      <div className="text-gray-500 text-xs">
                                        {stream.scheduledTime
                                          ? `${new Date(stream.scheduledTime).toLocaleDateString()} • ${new Date(stream.scheduledTime).toLocaleTimeString()}`
                                          : "No scheduled time"}
                                      </div>
                                    </label>
                                  </div>
                                ))}
                              </div>
                            </div>
                          </SelectContent>
                        </Select>
                      </div>
                    )}
                </CardContent>
              </Card>
            </div>

            {/* Right Column - Separate Cards */}
            <div className="space-y-6">
              {/* Pricing Card */}
              <Card>
                <CardHeader>
                  <CardTitle>Pricing</CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  <Tabs value={pricingType} onValueChange={setPricingType}>
                    <TabsList className="grid w-full grid-cols-2">
                      <TabsTrigger
                        value="buy-now"
                        className="data-[state=active]:bg-black data-[state=active]:text-white data-[state=active]:dark:bg-zinc-900"
                      >
                        Buy It Now
                      </TabsTrigger>
                      <TabsTrigger 
                        value="auction"
                        className="data-[state=active]:bg-black data-[state=active]:text-white data-[state=active]:dark:bg-zinc-900"
                      >
                        Auction
                      </TabsTrigger>
                    </TabsList>
                    <TabsContent value="buy-now" className="space-y-4">
                      <div>
                        <Label htmlFor="buy-now-price" className="text-sm text-gray-600 dark:text-zinc-400">
                          Price (USD) <span className="text-red-500">*</span>
                        </Label>
                        <Input id="buy-now-price" name="price" type="number" placeholder="0.00" className="mt-1" value={form.price} onChange={handleFormChange} required={pricingType === "buy-now"} />
                      </div>
                    </TabsContent>
                    <TabsContent value="auction" className="space-y-4">
                      <div>
                        <Label htmlFor="starting-bid" className="text-sm text-gray-600 dark:text-zinc-400">
                          Starting Bid (USD) <span className="text-red-500">*</span>
                        </Label>
                        <Input id="starting-bid" name="startingBid" type="number" placeholder="0.00" className="mt-1" value={form.startingBid} onChange={handleFormChange} required={pricingType === "auction"} />
                      </div>
                    </TabsContent>
                  </Tabs>

                  {/* Toggle Switches */}
                  <div className="space-y-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="font-medium text-gray-900 dark:text-zinc-100">Flash Sale</p>
                        <p className="text-sm text-gray-500 dark:text-zinc-400">Turn this on to enable flash sales on this product.</p>
                      </div>
                      <Switch checked={flashSale} onCheckedChange={setFlashSale} />
                    </div>

                    <div className="flex items-center justify-between">
                      <div>
                        <p className="font-medium text-gray-900 dark:text-zinc-100">Accept Offers</p>
                        <p className="text-sm text-gray-500 dark:text-zinc-400">
                          Turn this on if you are willing to accept offers in lives and the marketplace. You can accept,
                          counter, or decline the offers.
                        </p>
                      </div>
                      <Switch checked={acceptOffers} onCheckedChange={setAcceptOffers} />
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Shipping Card */}
              <Card>
                <CardHeader>
                  <CardTitle>Shipping</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <Label htmlFor="shipping-profile" className="text-sm text-gray-600 dark:text-zinc-400">
                      Shipping Profile <span className="text-red-500">*</span>
                    </Label>
                    <Select value={form.shippingProfile} onValueChange={v => handleSelectChange("shippingProfile", v)}>
                      <SelectTrigger className="mt-1 bg-white border-gray-300 dark:bg-zinc-900 dark:border-zinc-700 w-full">
                        <SelectValue placeholder="" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="0-1oz">0-1 oz</SelectItem>
                        <SelectItem value="1-3oz">1-3 oz</SelectItem>
                        <SelectItem value="4-7oz">4-7 oz</SelectItem>
                        <SelectItem value="8-11oz">8-11 oz</SelectItem>
                        <SelectItem value="12-15oz">12-15 oz</SelectItem>
                        <SelectItem value="1lb">1 lb</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label htmlFor="hazardous-materials" className="text-sm text-gray-600 dark:text-zinc-400">
                      Hazardous Materials <span className="text-red-500">*</span>
                    </Label>
                    <Select value={form.hazardousMaterials} onValueChange={v => handleSelectChange("hazardousMaterials", v)}>
                      <SelectTrigger className="mt-1 bg-white border-gray-300 dark:bg-zinc-900 dark:border-zinc-700 w-full">
                        <SelectValue placeholder="No Hazardous Materials" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="none">No Hazardous Materials</SelectItem>
                        <SelectItem value="flammable">Flammable</SelectItem>
                        <SelectItem value="corrosive">Corrosive</SelectItem>
                        <SelectItem value="toxic">Toxic</SelectItem>
                        <SelectItem value="other">Other</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="text-xs text-gray-500 dark:text-zinc-400 italic">
                    Carriers restrict the shipping of fragrances, nail polish, electronics containing lithium batteries,
                    and any items that may pose risks to health and safety.
                  </div>
                </CardContent>
              </Card>

              {/* Optional Fields Card */}
              <Card>
                <CardHeader>
                  <CardTitle>Optional Fields</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <p className="text-sm text-gray-500 dark:text-zinc-400">This information can only be seen by you.</p>
                  <div>
                    <Label htmlFor="cost-per-item" className="text-sm text-gray-600 dark:text-zinc-400">
                      Cost Per Item
                    </Label>
                    <Input id="cost-per-item" name="costPerItem" type="number" placeholder="" className="mt-1 bg-white border-gray-300 dark:bg-zinc-900 dark:border-zinc-700 w-full" value={form.costPerItem} onChange={handleFormChange} />
                  </div>
                  <div>
                    <Label htmlFor="sku" className="text-sm text-gray-600 dark:text-zinc-400">
                      SKU
                    </Label>
                    <Input id="sku" name="sku" type="text" placeholder="" className="mt-1 bg-white border-gray-300 dark:bg-zinc-900 dark:border-zinc-700 w-full" value={form.sku} onChange={handleFormChange} />
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>

          {/* Variants Section - Full Width Below Columns */}
          <Card className="mt-6">
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle>Variants</CardTitle>
                <Switch checked={variantsEnabled} onCheckedChange={setVariantsEnabled} />
              </div>
              <p className="text-sm text-gray-500 dark:text-zinc-400">Add various colors or sizes and quantities for this product.</p>
            </CardHeader>
            {variantsEnabled && (
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4 text-sm font-medium text-gray-600 dark:text-zinc-400">
                  <div>Title</div>
                  <div>Quantity</div>
                </div>

                {variants.map((variant) => (
                  <div key={variant.id} className="grid grid-cols-2 gap-4 items-center">
                    <Input
                      placeholder="Title"
                      value={variant.title}
                      onChange={(e) => updateVariant(variant.id, "title", e.target.value)}
                      className="bg-white border-gray-300 dark:bg-zinc-900 dark:border-zinc-700"
                    />
                    <div className="flex items-center gap-2">
                      <Input
                        placeholder="Quantity"
                        value={variant.quantity}
                        onChange={(e) => updateVariant(variant.id, "quantity", e.target.value)}
                        className="bg-white border-gray-300 dark:bg-zinc-900 dark:border-zinc-700"
                      />
                      <Button
                        variant="ghost"
                        size="sm"
                        type="button"
                        onClick={() => removeVariant(variant.id)}
                        className="text-gray-400 hover:text-red-500 dark:text-zinc-400 dark:hover:text-red-500"
                      >
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                ))}

                <Button
                  type="button"
                  onClick={addVariant}
                >
                  <Plus className="w-4 h-4 mr-1" />
                  Add row
                </Button>
              </CardContent>
            )}
          </Card>
        </div>
        <div className="sticky bottom-1 mt-4 left-0 w-full z-50 backdrop-blur-md bg-muted/50 rounded-lg border border-zinc-200 dark:border-zinc-800 px-4 py-3 flex justify-end space-x-4">
          <Button variant="outline" type="button" onClick={handleSaveAsDraft}>Save Draft</Button>
          <Button variant="outline" type="button" onClick={handleCancel}>Cancel</Button>
          <Button className="bg-blue-500 hover:bg-blue-600 text-white" type="submit" disabled={loading}>
            {loading ? "Publishing..." : "Publish"}
          </Button>
        </div>
      </form>
    </div>
  );
}
