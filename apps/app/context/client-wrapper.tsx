"use client";

import React from "react";
import { PreloadedDataProvider } from "./preloaded-data-context";
import { PreloadedData } from "./preloaded-data-context";
import AuthLayout from "@/components/layout/layout";
import { OnboardingChecker } from "@/providers/onboarding-checker";
import { usePathname } from "next/navigation";
import { ChatSidebarProvider } from "@/hooks/use-chat-sidebar";

export function ClientWrapper({
  children,
  preloadedData,
  isAuthenticated,
}: {
  children: React.ReactNode;
  preloadedData: PreloadedData;
  isAuthenticated: boolean;
}) {
  const pathname = usePathname();
  const isOnboardingPath = pathname.startsWith("/onboarding");

  if (!preloadedData.preloadedUser) {
    return <div>Loading...</div>;
  }

  return (
    <PreloadedDataProvider preloadedData={preloadedData}>
      <ChatSidebarProvider>
        {isAuthenticated && <OnboardingChecker />}
        {isAuthenticated && !isOnboardingPath ? (
          <AuthLayout>{children}</AuthLayout>
        ) : (
          <div className="overflow-y-auto">{children}</div>
        )}
      </ChatSidebarProvider>
    </PreloadedDataProvider>
  );
}
