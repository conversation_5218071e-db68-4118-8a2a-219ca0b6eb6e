import { type ClassValue, clsx } from "clsx";
import { PressableStateCallbackType } from "react-native";
import { twMerge } from "tailwind-merge";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export function isTextChildren(
  children:
    | React.ReactNode
    | ((state: PressableStateCallbackType) => React.ReactNode),
) {
  return Array.isArray(children)
    ? children.every((child) => typeof child === "string")
    : typeof children === "string";
}

/**
 * Utility function to check if a string is a valid Convex storage ID
 * @param id The string to check
 * @returns boolean indicating if the string is a valid storage ID
 */
export function isValidStorageId(id: string | null | undefined): boolean {
  if (!id) return false;
  return (
    typeof id === "string" &&
    (/^[0-9a-fA-F-]{36}$/.test(id) || /^[a-z0-9]{32,}$/.test(id))
  );
}

/**
 * Gets a properly formatted image URL from either a storage ID or direct URL
 * @param imageId The image ID or URL
 * @param fallbackUrl Optional fallback URL if image is not available
 * @returns A valid image URL or the fallback
 */
export function getImageUrl(image?: string) {
  if (typeof image !== "string" || !image) return undefined;

  if (image.startsWith("http://") || image.startsWith("https://")) {
    return image;
  }
  if (image.includes("/getImage?storageId=")) {
    return image;
  }
  
  return `${process.env.EXPO_PUBLIC_CONVEX_SITE_URL}/getImage?storageId=${image}`;
}

/**
 * Generate a fallback avatar URL based on username and color
 * @param username The username to use for the avatar
 * @param color The background color for the avatar
 * @returns A URL for a generated avatar image
 */
export function getUserAvatarFallback(
  username: string = "Anonymous",
  color: string = "2C2C2E",
): string {
  // Remove the # from the color if present
  const cleanColor = color.replace("#", "");

  // Create a UI Avatars URL
  return `https://ui-avatars.com/api/?name=${encodeURIComponent(username)}&background=${cleanColor}&color=ffffff`;
}
