import <PERSON> from "@auth/core/providers/google";
import Apple from "@auth/core/providers/apple";
import { Password } from "@convex-dev/auth/providers/Password";
import { convexAuth } from "@convex-dev/auth/server";
import { ExtendedProfile, LoginType } from "./lib/types";
import { generateColor } from "./helpers/utils";
import { hashPassword, verifyPassword } from "./helpers/scrypt";
import { validateUsername } from "../lib/profanity";
import { LoopsPasswordReset } from "./email/helpers/reset";

const PasswordProvider = Password({
  crypto: {
    hashSecret: hashPassword,
    verifySecret: verifyPassword,
  },
  validatePasswordRequirements: (password: string) => {
    if (password.length < 8) {
      throw new Error("Password must be at least 8 characters long");
    }
  },
  profile: (params: any) => {
    const email = params.email as string;
    return {
      email,
      image: "",
      loginType: params.loginType as string,
      username: params.username || undefined,
      firstName: params.firstName || undefined,
      lastName: params.lastName || undefined,
      phone: params.phone || undefined,
    };
  },
  reset: LoopsPasswordReset,
});

export const { auth, signIn, signOut, store, isAuthenticated } = convexAuth({
  providers: [
    Google({
      clientId: process.env.AUTH_GOOGLE_ID,
      clientSecret: process.env.AUTH_GOOGLE_SECRET,
      authorization: {
        params: {
          prompt: "consent",
          access_type: "offline",
          response_type: "code",
        },
      },
    }),
    Apple({
      clientId: process.env.AUTH_APPLE_ID,
      clientSecret: process.env.AUTH_APPLE_SECRET,
      profile: (appleInfo) => {
        const name = appleInfo.user
          ? `${appleInfo.user.name.firstName} ${appleInfo.user.name.lastName}`
          : undefined;
        return {
          id: appleInfo.sub,
          name: name,
          email: appleInfo.email,
        };
      },
    }),
    PasswordProvider,
  ],
  callbacks: {
    async createOrUpdateUser(ctx, args) {
      const profile = args.profile as ExtendedProfile;
      const type = args.type as LoginType;

      if (!profile.email) {
        throw new Error("Email is required");
      }

      const existingUserByEmail = await ctx.db
        .query("users")
        .filter((q) => q.eq(q.field("email"), profile.email))
        .first();

      if (existingUserByEmail) {
        await ctx.db.patch(existingUserByEmail._id, {
          lastLoginType: ["password", "oauth", "magiclink", "credentials"].includes(type) ? type : "password",
          image: profile.image || existingUserByEmail.image,
          name: profile.name || existingUserByEmail.name,
          firstName:
            profile.name?.split(" ")[0] || existingUserByEmail.firstName,
          lastName: profile.name?.split(" ")[1] || existingUserByEmail.lastName,
        });
        return existingUserByEmail._id;
      }

      const color = generateColor();
      
      // Generate username from email or provided username
      let desiredUsername;
      if (profile.username) {
        // If username is provided in the profile (from signUp form)
        desiredUsername = profile.username.toLowerCase();
      } else {
        // Default to email prefix
        desiredUsername = profile.email?.split("@")[0]?.toLowerCase() || "";
      }
      
      // Validate the username format
      const validationResult = validateUsername(desiredUsername);
      if (!validationResult.isValid) {
        throw new Error(`Invalid username: ${validationResult.error}`);
      }
      
      // Check if the username is already taken
      const existingUserByUsername = await ctx.db
        .query("users")
        .filter((q) => q.eq(q.field("username"), desiredUsername))
        .first();
      
      // If username is taken, generate a unique username
      let finalUsername = desiredUsername;
      if (existingUserByUsername) {
        // Append a random number to make it unique
        finalUsername = `${desiredUsername}${Math.floor(Math.random() * 10000)}`;
        
        // Make sure even the new username is unique (check again)
        const stillExists = await ctx.db
          .query("users")
          .filter((q) => q.eq(q.field("username"), finalUsername))
          .first();
        
        if (stillExists) {
          // Try with a different random number
          finalUsername = `${desiredUsername}${Math.floor(Math.random() * 1000000)}`;
        }
      }

      const userId = await ctx.db.insert("users", {
        email: profile.email,
        image: profile.image || "",
        name: profile.name || `${profile.firstName} ${profile.lastName}` || finalUsername,
        firstName: profile.firstName || profile.name?.split(" ")[0] || finalUsername,
        lastName: profile.lastName || profile.name?.split(" ")[1] || "",
        username: finalUsername,
        color: color,
        lastLoginType: ["password", "oauth", "magiclink", "credentials"].includes(type) ? type : "password",
        role: "user",
        phone: profile.phone || "",
        finishedSignUp: false,
      });

      return userId;
    },
  },
});
