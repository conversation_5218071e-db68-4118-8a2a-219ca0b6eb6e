import { v } from "convex/values";
import { CURRENCIES, INTERVALS, PLANS } from "./constants";
import { z } from "zod";

export const username = z
  .string()
  .min(3)
  .max(32)
  .toLowerCase()
  .trim()
  .regex(
    /^[a-zA-Z0-9]+$/,
    "Username may only contain alphanumeric characters.",
  );

export const LOGIN_TYPE = v.union(
  v.literal("password"),
  v.literal("oauth"),
  v.literal("magiclink"),
  v.literal("credentials"),
);

export const USER_STATUS = v.union(
  v.literal("online"),
  v.literal("idle"),
  v.literal("offline"),
);

export const USER_ROLE = v.union(
  v.literal("user"),
  v.literal("seller"),
  v.literal("admin"),
);

export const STREAM_STATUS = v.union(
  v.literal("scheduled"),
  v.literal("live"),
  v.literal("ended"),
);

export const PRODUCT_CONDITION = v.union(
  v.literal("new"),
  v.literal("like_new"),
  v.literal("good"),
  v.literal("fair"),
);

export const PRODUCT_STATUS = v.union(
  v.literal("draft"),
  v.literal("active"),
  v.literal("sold_out"),
  v.literal("archived"),
);

export const ORDER_STATUS = v.union(
  v.literal("pending"),
  v.literal("confirmed"),
  v.literal("shipped"),
  v.literal("delivered"),
  v.literal("cancelled"),
  v.literal("refunded"),
);

export const PRODUCT_VISIBILITY = v.union(
  v.literal("published"),
  v.literal("draft"),
);

export const CURRENCY_VALIDATOR = v.union(
  v.literal(CURRENCIES.USD),
  v.literal(CURRENCIES.EUR),
);

export const PRICE_VALIDATOR = v.object({
  stripeId: v.string(),
  amount: v.number(),
});

export const PRICES_VALIDATOR = v.object({
  amount: v.number(),
  currency: CURRENCY_VALIDATOR,
});

export const INTERVAL_VALIDATOR = v.union(
  v.literal(INTERVALS.MONTH),
  v.literal(INTERVALS.YEAR),
);

export const PLAN_KEY_VALIDATOR = v.union(
  v.literal(PLANS.FREE),
  v.literal(PLANS.STANDARD),
  v.literal(PLANS.PRO),
  v.literal(PLANS.ENTERPRISE),
);
