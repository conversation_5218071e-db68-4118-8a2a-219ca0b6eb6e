import { mutation, MutationCtx } from "../_generated/server";
import { aggregateUsers } from "../custom";

const aggregateUsersMigration = mutation({
  args: {},
  handler: async (ctx: MutationCtx) => {
    const users = await ctx.db.query("users").collect();

    console.log("Migrating users to aggregateUsers", users.length);

    let count = 0;

    for (const user of users) {
      await aggregateUsers.insertIfDoesNotExist(ctx, user);
      count++;
    }
    return { aggregated: count };
  }
});

export default aggregateUsersMigration; 