import { httpRouter } from "convex/server";
import { auth } from "./auth";
import { httpAction } from "./_generated/server";
import { Id } from "./_generated/dataModel";
import { twilio } from "./integration/twilio";
import Stripe from "stripe";
import { ERRORS } from "./lib/errors";
import { api, internal } from "./_generated/api";

const http = httpRouter();

auth.addHttpRoutes(http);

/**
 * @name mobileOAuthCallback
 * @description Handles OAuth callbacks for mobile app
 * @method GET
 * @route /api/auth/mobile/callback/google
 */
http.route({
  path: "/api/auth/mobile/callback/google",
  method: "GET",
  handler: httpAction(async (ctx, request) => {
    console.log('🔵 Mobile OAuth callback received');

    // Get the authorization code from the query parameters
    const url = new URL(request.url);
    const code = url.searchParams.get('code');
    const state = url.searchParams.get('state');

    console.log('🔵 OAuth callback params:', { code: !!code, state });

    if (!code) {
      return new Response('Missing authorization code', { status: 400 });
    }

    // Create a response that redirects back to the mobile app
    const appRedirectUrl = `liveciety://oauth/success?code=${encodeURIComponent(code)}`;

    // Return an HTML page that automatically redirects to the mobile app
    const html = `
      <!DOCTYPE html>
      <html>
      <head>
        <title>Authentication Successful</title>
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <style>
          body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            margin: 0;
            background: #18181B;
            color: white;
            text-align: center;
          }
          .container {
            padding: 2rem;
            max-width: 400px;
          }
          .success {
            color: #10B981;
            font-size: 1.5rem;
            margin-bottom: 1rem;
          }
          .message {
            margin-bottom: 2rem;
            opacity: 0.8;
          }
          .redirect-link {
            color: #3B82F6;
            text-decoration: none;
            padding: 0.75rem 1.5rem;
            border: 1px solid #3B82F6;
            border-radius: 0.5rem;
            display: inline-block;
            margin-top: 1rem;
          }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="success">✅ Authentication Successful!</div>
          <div class="message">
            You have successfully signed in with Google.
            <br><br>
            Redirecting back to the Liveciety app...
          </div>
          <a href="${appRedirectUrl}" class="redirect-link">
            Open Liveciety App
          </a>
        </div>
        <script>
          // Automatically redirect to the app after a short delay
          setTimeout(() => {
            window.location.href = '${appRedirectUrl}';
          }, 2000);
        </script>
      </body>
      </html>
    `;

    return new Response(html, {
      status: 200,
      headers: {
        'Content-Type': 'text/html',
      },
    });
  }),
});

/**
 * @name registerTwilioRoutes
 * @description Registers Twilio routes.
 */
twilio.registerRoutes(http);

/**
 * @name twilioDeliveryStatus
 * @description Handles Twilio message delivery status callbacks.
 * @method POST
 * @route /twilio/delivery-status
 */
http.route({
  path: "/twilio/sms/delivery-status",
  method: "POST",
  handler: httpAction(async (ctx, request) => {
    const formData = await request.formData();

    return new Response("Status received", { status: 200 });
  }),
});

/**
 * @name getImage
 * @description Handles getting an image from the storage bucket.
 * @method GET
 * @route /getImage
 */
http.route({
  path: "/getImage",
  method: "GET",
  handler: httpAction(async (ctx, request) => {
    const { searchParams } = new URL(request.url);
    const rawStorageId = searchParams.get("storageId");

    if (!rawStorageId) {
      return new Response("Missing storageId parameter", {
        status: 400,
      });
    }

    const storageId = rawStorageId.includes("?storageId=")
      ? rawStorageId.split("?storageId=")[1]
      : rawStorageId;

    const blob = await ctx.storage.get(storageId as Id<"_storage">);

    if (blob === null) {
      return new Response("Image not found", {
        status: 404,
      });
    }

    return new Response(blob);
  }),
});

async function getStripeEvent(request: Request) {
  if (!process.env.STRIPE_WEBHOOK_SECRET) {
    console.error("Stripe webhook secret is not configured");
    throw new Error(`Stripe - ${ERRORS.ENVS_NOT_INITIALIZED}`);
  }
  try {
    const signature = request.headers.get("Stripe-Signature");
    if (!signature) {
      console.error("No Stripe signature found in request headers");
      throw new Error(ERRORS.STRIPE_MISSING_SIGNATURE);
    }
    
    const clonedRequest = request.clone();
    const payload = await clonedRequest.text();
    
    try {
      const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
        apiVersion: "2025-02-24.acacia",
      });
      
      const event = await stripe.webhooks.constructEventAsync(
        payload,
        signature,
        process.env.STRIPE_WEBHOOK_SECRET!
      );
      
      return event;
    } catch (err) {
      console.error("Failed to construct Stripe event:", err);
      throw new Error(
        err instanceof Error ? err.message : ERRORS.STRIPE_SOMETHING_WENT_WRONG,
      );
    }
  } catch (err: unknown) {
    console.error("Error in getStripeEvent:", err);
    throw err instanceof Error
      ? err
      : new Error(ERRORS.STRIPE_SOMETHING_WENT_WRONG);
  }
}

/**
 * @name stripeWebhook
 * @description Handles Stripe webhooks.
 * @method POST
 * @route /stripe
 */
http.route({
  path: "/stripe",
  method: "POST",
  handler: httpAction(async (ctx, request) => {
    let event;
    try {
      const isDev = process.env.NODE_ENV === "development";
      
      if (isDev) {
        const payload = await request.clone().text();
        try {
          event = JSON.parse(payload);
        } catch (err) {
          console.error("Failed to parse webhook payload:", err);
          return new Response("Invalid webhook payload", { status: 400 });
        }
      } else {
        event = await getStripeEvent(request);
      }
    } catch (err) {
      console.error("Webhook Error:", err);
      return new Response("Webhook Error", { status: 400 });
    }

    try {
      
      switch (event.type) {
        case 'setup_intent.succeeded':
          const setupIntent = event.data.object;
          break;
        case 'payment_method.attached':
          const paymentMethod = event.data.object;
          break;
        case 'payment_method.detached':
          break;
        default:
          console.error(`Unhandled event type ${event.type}`);
      }
      
      return new Response("OK", { status: 200 });
    } catch (error) {
      console.error("Error processing webhook:", error);
      return new Response("Error processing webhook", { status: 500 });
    }
  }),
});

/**
 * @name livekit
 * @description Handles LiveKit webhooks.
 * @method POST
 * @route /livekit
 */
http.route({
  path: "/livekit",
  method: "POST",
  handler: httpAction(async (ctx, request) => {
    const authorization = request.headers.get("Authorization");
    if (!authorization) {
      return new Response("Error occurred -- no authorization headers", { status: 400 });
    }
    const body = await request.text();
    const result = await ctx.runAction(api.actions.livekit.handleLivekitWebhook, {
      body,
      authorization,
    });
    if (result.error) {
      return new Response(result.error, { status: 400 });
    }
    return new Response("Success!", { status: 200 });
  }),
});

export default http;
