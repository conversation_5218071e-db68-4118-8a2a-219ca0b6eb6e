import { httpRouter } from "convex/server";
import { auth } from "./auth";
import { httpAction } from "./_generated/server";
import { Id } from "./_generated/dataModel";
import { twilio } from "./integration/twilio";
import Stripe from "stripe";
import { ERRORS } from "./lib/errors";
import { api, internal } from "./_generated/api";

const http = httpRouter();

auth.addHttpRoutes(http);

/**
 * @name registerTwilioRoutes
 * @description Registers Twilio routes.
 */
twilio.registerRoutes(http);

/**
 * @name twilioDeliveryStatus
 * @description Handles Twilio message delivery status callbacks.
 * @method POST
 * @route /twilio/delivery-status
 */
http.route({
  path: "/twilio/sms/delivery-status",
  method: "POST",
  handler: httpAction(async (ctx, request) => {
    const formData = await request.formData();

    return new Response("Status received", { status: 200 });
  }),
});

/**
 * @name getImage
 * @description Handles getting an image from the storage bucket.
 * @method GET
 * @route /getImage
 */
http.route({
  path: "/getImage",
  method: "GET",
  handler: httpAction(async (ctx, request) => {
    const { searchParams } = new URL(request.url);
    const rawStorageId = searchParams.get("storageId");

    if (!rawStorageId) {
      return new Response("Missing storageId parameter", {
        status: 400,
      });
    }

    const storageId = rawStorageId.includes("?storageId=")
      ? rawStorageId.split("?storageId=")[1]
      : rawStorageId;

    const blob = await ctx.storage.get(storageId as Id<"_storage">);

    if (blob === null) {
      return new Response("Image not found", {
        status: 404,
      });
    }

    return new Response(blob);
  }),
});

async function getStripeEvent(request: Request) {
  if (!process.env.STRIPE_WEBHOOK_SECRET) {
    console.error("Stripe webhook secret is not configured");
    throw new Error(`Stripe - ${ERRORS.ENVS_NOT_INITIALIZED}`);
  }
  try {
    const signature = request.headers.get("Stripe-Signature");
    if (!signature) {
      console.error("No Stripe signature found in request headers");
      throw new Error(ERRORS.STRIPE_MISSING_SIGNATURE);
    }
    
    const clonedRequest = request.clone();
    const payload = await clonedRequest.text();
    
    try {
      const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
        apiVersion: "2025-02-24.acacia",
      });
      
      const event = await stripe.webhooks.constructEventAsync(
        payload,
        signature,
        process.env.STRIPE_WEBHOOK_SECRET!
      );
      
      return event;
    } catch (err) {
      console.error("Failed to construct Stripe event:", err);
      throw new Error(
        err instanceof Error ? err.message : ERRORS.STRIPE_SOMETHING_WENT_WRONG,
      );
    }
  } catch (err: unknown) {
    console.error("Error in getStripeEvent:", err);
    throw err instanceof Error
      ? err
      : new Error(ERRORS.STRIPE_SOMETHING_WENT_WRONG);
  }
}

/**
 * @name stripeWebhook
 * @description Handles Stripe webhooks.
 * @method POST
 * @route /stripe
 */
http.route({
  path: "/stripe",
  method: "POST",
  handler: httpAction(async (ctx, request) => {
    let event;
    try {
      const isDev = process.env.NODE_ENV === "development";
      
      if (isDev) {
        const payload = await request.clone().text();
        try {
          event = JSON.parse(payload);
        } catch (err) {
          console.error("Failed to parse webhook payload:", err);
          return new Response("Invalid webhook payload", { status: 400 });
        }
      } else {
        event = await getStripeEvent(request);
      }
    } catch (err) {
      console.error("Webhook Error:", err);
      return new Response("Webhook Error", { status: 400 });
    }

    try {
      
      switch (event.type) {
        case 'setup_intent.succeeded':
          const setupIntent = event.data.object;
          break;
        case 'payment_method.attached':
          const paymentMethod = event.data.object;
          break;
        case 'payment_method.detached':
          break;
        default:
          console.error(`Unhandled event type ${event.type}`);
      }
      
      return new Response("OK", { status: 200 });
    } catch (error) {
      console.error("Error processing webhook:", error);
      return new Response("Error processing webhook", { status: 500 });
    }
  }),
});

/**
 * @name livekit
 * @description Handles LiveKit webhooks.
 * @method POST
 * @route /livekit
 */
http.route({
  path: "/livekit",
  method: "POST",
  handler: httpAction(async (ctx, request) => {
    const authorization = request.headers.get("Authorization");
    if (!authorization) {
      return new Response("Error occurred -- no authorization headers", { status: 400 });
    }
    const body = await request.text();
    const result = await ctx.runAction(api.actions.livekit.handleLivekitWebhook, {
      body,
      authorization,
    });
    if (result.error) {
      return new Response(result.error, { status: 400 });
    }
    return new Response("Success!", { status: 200 });
  }),
});

export default http;
