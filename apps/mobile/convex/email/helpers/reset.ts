import { LoopsClient, APIError } from "loops";
import { generateUniqueCode } from "../../helpers/utils";

const loops = new LoopsClient(process.env.LOOPS_API_KEY as string);

export const LoopsPasswordReset = {
  id: "loops-reset",
  type: "email",
  name: "Loops",
  apiKey: process.env.LOOPS_API_KEY,
  async generateVerificationToken() {
    return generateUniqueCode();
  },
  async sendVerificationRequest({ identifier, provider, token }: {
    identifier: string;
    provider: any;
    token: string;
    url: string;
    expires: Date;
    theme: any;
    request: Request;
  }) {
    const email = identifier;
    try {
      const resp = await loops.sendTransactionalEmail({
        transactionalId: "cmap9z5az006nmr6p2vxk1hz0",
        email,
        dataVariables: {
          resetCode: token,
        },
      });
      if (!resp.success) {
        throw new Error("Could not send");
      }
    } catch (error) {
      if (error instanceof APIError) {
        console.error(error.json);
        throw new Error((error.json && (error.json as any).message) || "Could not send");
      }
      throw error;
    }
  },
} as const;