import { Twi<PERSON> } from "@convex-dev/twilio";
import { components } from "../_generated/api";

const accountSid = process.env.TWILIO_ACCOUNT_SID?.trim();
const authToken = process.env.TWILIO_AUTH_TOKEN?.trim();
const fromNumber = process.env.TWILIO_PHONE_NUMBER?.trim();

const validateCredentials = () => {
  const errors = [];

  if (!accountSid) {
    errors.push("TWILIO_ACCOUNT_SID is missing");
  } else if (!accountSid.startsWith("AC")) {
    errors.push("TWILIO_ACCOUNT_SID must start with 'AC'");
  }

  if (!authToken) {
    errors.push("TWILIO_AUTH_TOKEN is missing");
  } else if (authToken.length !== 32) {
    errors.push("TWILIO_AUTH_TOKEN must be 32 characters long");
  }

  if (!fromNumber) {
    errors.push("TWILIO_PHONE_NUMBER is missing");
  } else if (!/^\+\d{10,15}$/.test(fromNumber)) {
    errors.push(
      "TWILIO_PHONE_NUMBER must be in E.164 format (e.g., +**********)",
    );
  }

  if (errors.length > 0) {
    throw new Error(`Twilio Configuration Errors:\n${errors.join("\n")}`);
  }
};

validateCredentials();

const twilioConfig = {
  credentials: {
    accountSid,
    authToken,
  },
  defaultFrom: fromNumber,
};

export const twilio = new Twilio(components.twilio, twilioConfig);
