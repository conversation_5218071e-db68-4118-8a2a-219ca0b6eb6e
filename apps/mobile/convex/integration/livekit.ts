"use node";

import { v } from "convex/values";
import { action } from "../_generated/server";
import { api, internal } from "../_generated/api";
import { AccessToken, IngressInput, IngressInfo, IngressClient, CreateIngressOptions, RoomServiceClient } from "livekit-server-sdk";
import { Doc, Id } from "../_generated/dataModel";

const rawLiveKitApiKey = process.env.LIVEKIT_API_KEY;
const rawLiveKitApiSecret = process.env.LIVEKIT_API_SECRET;
const liveKitHostForSDK = process.env.LIVEKIT_HOST?.replace(/^https?:\/\//, '').replace(/^wss?:\/\//, '').replace(/^\/\//, '');
const liveKitHttpUrl = process.env.LIVEKIT_API_URL; 


if (!rawLiveKitApiKey || !rawLiveKitApiSecret) {
  console.warn(
    "LiveKit API Key or Secret is not set in Convex environment variables. Streaming token generation might fail."
  );
}
if (!liveKitHostForSDK) {
  console.warn(
    "LIVEKIT_HOST is not set or is invalid in Convex environment variables. It should be your LiveKit project host (e.g., 'my-project-abcdef12.livekit.cloud', without any scheme). Token generation may fail if SDK relies on it implicitly."
  );
}
if (!liveKitHttpUrl) {
  console.warn(
    "LIVEKIT_API_URL is not set in Convex environment variables. This is required for Ingress operations (e.g., 'https://my-project-abcdef12.livekit.cloud'). OBS Ingress generation/Viewer count will fail."
  );
}

export const generateViewerToken = action({
  args: {
    streamId: v.id("streams"),
  },
  handler: async (ctx, args): Promise<string> => {
    if (!rawLiveKitApiKey || !rawLiveKitApiSecret ) { 
      throw new Error("LiveKit API Key or Secret is not configured correctly in Convex environment variables.");
    }
    const identity: Doc<"users"> | null = await ctx.runQuery(api.users.viewer);
    const stream: Doc<"streams"> | null = await ctx.runQuery(api.streams.getStreamById, { streamId: args.streamId });

    if (!stream) {
      throw new Error("Stream not found");
    }

    let tokenIdentity: string;
    let tokenName: string | undefined;

    if (!identity) {
      tokenIdentity = `viewer-${Math.random().toString(36).substring(7)}`;
      tokenName = "Anonymous Viewer";
    } else {
      tokenIdentity = identity._id;
      tokenName = identity.name ?? identity.email ?? "User";
    }

    const at = new AccessToken(rawLiveKitApiKey, rawLiveKitApiSecret, {
      identity: tokenIdentity,
      name: tokenName,
    });

    at.addGrant({
      room: stream.roomName,
      roomJoin: true,
      canPublish: false,
      canPublishData: true,
    });

    return await at.toJwt();
  },
});

export const generateHostToken = action({
  args: {
    streamId: v.id("streams"),
  },
  handler: async (ctx, args): Promise<string> => {
    if (!rawLiveKitApiKey || !rawLiveKitApiSecret) { 
      throw new Error("LiveKit API Key or Secret is not configured correctly in Convex environment variables.");
    }

    const user = await ctx.runQuery(internal.users.INTERNAL_getUser);
    if (!user) {
      throw new Error("User must be logged in to host a stream.");
    }

    const stream = await ctx.runQuery(api.streams.getStreamById, { streamId: args.streamId });
    if (!stream) {
      throw new Error("Stream not found.");
    }
    if (stream.hostId !== user._id) {
      throw new Error("Only the host can generate a host token for this stream.");
    }

    const hostIdentityDoc: Doc<"users"> | null = await ctx.runQuery(api.users.getUserById, { userId: user._id });
     if (!hostIdentityDoc) {
      throw new Error("Host user identity not found.");
    }

    const at = new AccessToken(rawLiveKitApiKey, rawLiveKitApiSecret, {
      identity: user._id,
      name: hostIdentityDoc.name ?? hostIdentityDoc.email ?? "Host",
    });

    at.addGrant({
      room: stream.roomName,
      roomJoin: true,
      canPublish: true,
      canPublishData: true,
      roomCreate: true,
    });

    return await at.toJwt();
  },
});

export const generateIngestInfo = action({
  args: {
    streamId: v.id("streams"),
  },
  handler: async (ctx, args): Promise<{ ingestUrl: string; streamKey: string }> => {
    if (!rawLiveKitApiKey || !rawLiveKitApiSecret || !liveKitHttpUrl) {
      let missingVars = [];
      if (!rawLiveKitApiKey) missingVars.push("LIVEKIT_API_KEY");
      if (!rawLiveKitApiSecret) missingVars.push("LIVEKIT_API_SECRET");
      if (!liveKitHttpUrl) missingVars.push("LIVEKIT_API_URL");
      const errorMessage = `LiveKit environment variables missing for Ingress: ${missingVars.join(", ")}.`;
      console.error(errorMessage);
      throw new Error(errorMessage);
    }

    const user = await ctx.runQuery(internal.users.INTERNAL_getUser);
    if (!user) throw new Error("User must be logged in to generate ingress info.");
    
    const hostIdentityDoc: Doc<"users"> | null = await ctx.runQuery(api.users.getUserById, { userId: user._id });
    if (!hostIdentityDoc) throw new Error("Host user identity not found for ingress.");

    const stream = await ctx.runQuery(api.streams.getStreamById, { streamId: args.streamId });
    if (!stream) throw new Error("Stream not found.");
    if (stream.hostId !== user._id) throw new Error("Only the host can generate ingress info.");

    const ingressClient = new IngressClient(liveKitHttpUrl, rawLiveKitApiKey, rawLiveKitApiSecret);
    const ingressOptions: CreateIngressOptions = {
        name: `ingress-${stream._id}-${Date.now()}`,
        roomName: stream.roomName,
        participantIdentity: user._id, 
        participantName: hostIdentityDoc.name ?? hostIdentityDoc.email ?? `Host-${user._id.substring(0,6)}`,
    };

    const existingIngresses = await ingressClient.listIngress({ roomName: stream.roomName });
    let ingress: IngressInfo | undefined = existingIngresses.find(i => i.inputType === IngressInput.RTMP_INPUT && i.url && i.streamKey) ?? 
                                       existingIngresses.find(i => i.inputType === IngressInput.RTMP_INPUT);
        
    if (!ingress?.url || !ingress?.streamKey) {
        ingress = await ingressClient.createIngress(IngressInput.RTMP_INPUT, ingressOptions);
    }
    
    if (!ingress || !ingress.url || !ingress.streamKey) {
        throw new Error("Failed to create or retrieve a usable LiveKit ingress.");
    }
    return { ingestUrl: ingress.url, streamKey: ingress.streamKey };
  },
});

export const getRoomParticipantsCount = action({
  args: { roomName: v.string() },
  handler: async (ctx, args): Promise<number> => {
    if (!liveKitHttpUrl || !rawLiveKitApiKey || !rawLiveKitApiSecret) {
      console.error("LiveKit API URL, Key or Secret not configured for RoomServiceClient.");
      return 0; // Or throw error
    }
    try {
      const roomService = new RoomServiceClient(liveKitHttpUrl, rawLiveKitApiKey, rawLiveKitApiSecret);
      const participants = await roomService.listParticipants(args.roomName);
      // Filter out the ingress participant if it's named in a specific way, or count all.
      // For simplicity, counting all for now. LiveKit often has an "ingress" participant.
      return participants.length;
    } catch (error) {
      console.error(`Failed to get participant count for room ${args.roomName}:`, error);
      return 0; // Return 0 on error
    }
  }
});
