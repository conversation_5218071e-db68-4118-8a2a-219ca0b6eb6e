import { getAuthUserId } from "@convex-dev/auth/server";
import { MutationCtx, QueryCtx, ActionCtx } from "../_generated/server";
import { DataModel, Doc, Id } from "../_generated/dataModel";

/**
 * @name getCurrentUser
 * @description Get the current user from the database
 * @param {QueryCtx | MutationCtx} ctx - The context object
 * @returns {Promise<Doc<"users"> | null>} The current user or null if not authenticated
 */
export const getCurrentUser = async (
  ctx: QueryCtx | MutationCtx,
): Promise<Doc<"users"> | null> => {
  const userId = await getAuthUserId(ctx);
  if (!userId) return null;

  const user = await ctx.db.get(userId);
  if (!user) return null;

  return {
    ...user,
  };
};

/**
 * @name generateUniqueCode
 * @description Generate a random unique code
 * @returns {string} The unique code
 */
export const generateUniqueCode = () => {
  const chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
  return Array.from(
    { length: 8 },
    () => chars[Math.floor(Math.random() * chars.length)],
  ).join("");
};

/**
 * @name generateColor
 * @description Generate a random color
 * @returns {string} The color
 */
export const generateColor = () => {
  return "#" + Math.floor(Math.random() * 16777215).toString(16);
};

/**
 * @function getImageUrl
 * @param {string} storageId - The storage id of the image
 * @returns {string | undefined} - The image url
 * @description This function will return the image url based on the storage id
 */
export function getImageUrl(storageId: string): string | undefined {
  let imageUrl;

  if (storageId) {
    imageUrl = new URL(`https://decisive-perch-342.convex.site/getImage`);
    imageUrl.searchParams.set("storageId", storageId);
  }

  let imageUrlString = imageUrl?.href;
  imageUrlString = imageUrlString?.replace(".cloud", ".site");

  return imageUrlString;
}

/**
 * Converts a JSON object to a CSV string
 * @param json - The JSON object to convert
 * @returns The CSV string
 */
export function jsonToCsv(json: any): string {
  const array = [Object.keys(json[0])].concat(
    json.map((obj: any) => Object.values(obj)),
  );

  return array.map((row) => row.join(",")).join("\n");
}

/**
 * Removes a record ID from an array field of records in a specified table
 * @param ctx - The Convex mutation context
 * @param recordId - The ID of the record to remove
 * @param tableName - The name of the table to query
 * @param fieldName - The name of the array field containing the IDs
 */
export async function removeRelatedIdFromObject<
  T extends keyof DataModel,
  K extends keyof DataModel[T]["document"],
>(ctx: MutationCtx, recordId: Id<any>, tableName: T, fieldName: K) {
  const records = await ctx.db
    .query(tableName)
    .filter(
      (record: any) =>
        Array.isArray(record[fieldName]) &&
        record[fieldName].includes(recordId),
    )
    .collect();

  for (const record of records) {
    const updatedArray = Array.isArray(record[fieldName])
      ? record[fieldName].filter((id: Id<any>) => id !== recordId)
      : [];
    await ctx.db.patch(record._id, { [fieldName]: updatedArray } as any);
  }
}

/**
 * Updates a record by adding an ID to its array field
 * @param ctx - The Convex mutation context
 * @param recordId - The ID of the record to update
 * @param fieldName - The name of the array field
 * @param idToAdd - The ID to add to the array
 */
export async function addIdToRecordField<
  T extends keyof DataModel,
  K extends keyof DataModel[T]["document"],
>(ctx: MutationCtx, recordId: Id<T>, fieldName: K, idToAdd: Id<any>) {
  if (!recordId) {
    throw new Error("No record ID provided");
  }

  const record = await ctx.db.get(recordId);

  if (!record) {
    throw new Error("Record not found");
  }

  const updatedArray = record[fieldName]
    ? [...(record[fieldName] as any[]), idToAdd]
    : [idToAdd];
  await ctx.db.patch(recordId, { [fieldName]: updatedArray } as any);
}

/**
 * Remove favorites for a record
 * @param ctx - The Convex mutation context
 * @param recordId - The ID of the record to remove favorites for
 */
export async function removeFavoritesForRecord(
  ctx: MutationCtx,
  recordId: Id<any>,
) {
  try {
    const favorites = await ctx.db
      .query("favorites")
      .withIndex("by_object", (q) => q.eq("objectId", recordId))
      .collect();

    for (const favorite of favorites) {
      await ctx.db.delete(favorite._id);
    }
  } catch (error) {
    console.error("Error removing favorites:", error);
    throw error;
  }
}

export function isValidStorageId(id: string) {
  return (
    typeof id === "string" &&
    (/^[0-9a-fA-F-]{36}$/.test(id) || /^[a-z0-9]{32,}$/.test(id))
  );
}