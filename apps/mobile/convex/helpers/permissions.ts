import { v } from "convex/values";
import { Id } from "../_generated/dataModel";
import {
  Permission,
  UserRole,
  UserStatus,
} from "../lib/types";
import { PERMISSIONS, ROLES } from "../lib/constants";

/**
 * @name ROLE_PERMISSIONS
 * @description Define role-permission mappings
 */
export const ROLE_PERMISSIONS: Record<UserRole, Permission[]> = {
  admin: [
    PERMISSIONS.ROOM.CREATE,
    PERMISSIONS.ROOM.DELETE,
    PERMISSIONS.ROOM.UPDATE,
    PERMISSIONS.ROOM.BLOCK_USER,
    PERMISSIONS.ROOM.UNBLOCK_USER,
    PERMISSIONS.ROOM.MUTE_USER,
    PERMISSIONS.ROOM.UNMUTE_USER,
    PERMISSIONS.ROOM.MUTE_ALL,
    PERMISSIONS.ROOM.UNMUTE_ALL,
    PERMISSIONS.ROOM.MUTE_NOTIFICATIONS,
    PERMISSIONS.ROOM.UNMUTE_NOTIFICATIONS,
    PERMISSIONS.USER.UPDATE,
    PERMISSIONS.USER.DELETE,
  ],
  member: [PERMISSIONS.ROOM.JOIN] as Permission[],
  guest: [PERMISSIONS.ROOM.JOIN] as Permission[],
  blocked: [],
};

/**
 * @name hasPermission
 * @description Check if a user has a specific permission in a room
 */
export function hasPermission(
  role: UserRole,
  permission: Permission,
): boolean {
  const rolePermissions = ROLE_PERMISSIONS[role];
  return rolePermissions.includes(permission);
}

/**
 * @name hasAnyPermission
 * @description Check if a user has any of the specified permissions in a room
 */
export function hasAnyPermission(
  members: { id: Id<"users">; role: UserRole }[],
  userId: Id<"users">,
  permissions: Permission[],
): boolean {
  const member = members.find((u) => u.id === userId);
  if (!member) return false;
  return permissions.some((permission) =>
    hasPermission(member.role, permission),
  );
}

/**
 * @name hasAllPermissions
 * @description Check if a user has all of the specified permissions in a room
 */
export function hasAllPermissions(
  members: { id: Id<"users">; role: UserRole }[],
  userId: Id<"users">,
  permissions: Permission[],
): boolean {
  const member = members.find((u) => u.id === userId);
  if (!member) return false;
  return permissions.every((permission) =>
    hasPermission(member.role, permission),
  );
}

/**
 * @name getUserPermissions
 * @description Get all permissions for a user in a room
 */
export function getUserPermissions(
  members: { id: Id<"users">; role: UserRole }[],
  userId: Id<"users">,
): Permission[] {
  const member = members.find((u) => u.id === userId);
  if (!member) return [];
  return ROLE_PERMISSIONS[member.role];
}

/**
 * @name permissionValidator
 * @description Validator for permission strings
 */
export const permissionValidator = v.union(
  ...Object.values(PERMISSIONS).flatMap((category) =>
    Object.values(category).map((permission) => v.literal(permission)),
  ),
);
