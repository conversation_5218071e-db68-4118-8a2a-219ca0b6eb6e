import { mutation } from "./_generated/server";
import { v } from "convex/values";
import { generateColor } from "./helpers/utils";

export const createOrUpdateOAuthUser = mutation({
  args: {
    email: v.string(),
    name: v.optional(v.string()),
    image: v.optional(v.string()),
    firstName: v.optional(v.string()),
    lastName: v.optional(v.string()),
    googleId: v.string(),
  },
  handler: async (ctx, args) => {
    console.log('🔵 createOrUpdateOAuthUser called with:', args.email);

    // Check if user already exists
    const existingUser = await ctx.db
      .query("users")
      .filter((q) => q.eq(q.field("email"), args.email))
      .first();

    if (existingUser) {
      console.log('🔵 Existing user found, updating:', existingUser._id);
      await ctx.db.patch(existingUser._id, {
        lastLoginType: "oauth",
        image: args.image || existingUser.image,
        name: args.name || existingUser.name,
        firstName: args.firstName || existingUser.firstName,
        lastName: args.lastName || existingUser.lastName,
      });
      return existingUser._id;
    }

    console.log('🔵 Creating new user');
    
    const username = (args.email?.split("@")[0]?.toLowerCase() || "user") + Math.floor(Math.random() * 1000);
    const color = generateColor();
    
    const userId = await ctx.db.insert("users", {
      email: args.email,
      image: args.image || "",
      name: args.name || username,
      firstName: args.firstName || username,
      lastName: args.lastName || "",
      username: username,
      color: color,
      lastLoginType: "oauth",
      role: "user",
      phone: "",
      finishedSignUp: false,
    });

    console.log('🔵 New user created:', userId);
    return userId;
  },
});

export const createOAuthSession = mutation({
  args: {
    userId: v.id("users"),
  },
  handler: async (ctx, args) => {
    console.log('🔵 Creating OAuth session for user:', args.userId);

    // Create a simple session without using Convex Auth's token generation
    // since it requires CONVEX_SITE_URL which isn't available in this context
    const sessionId = await ctx.db.insert("authSessions", {
      userId: args.userId,
      expirationTime: Date.now() + (30 * 24 * 60 * 60 * 1000), // 30 days
    });

    console.log('🔵 Session created:', sessionId);

    // Generate a simple session token that we can use
    const sessionToken = `oauth_session_${sessionId}_${Date.now()}`;

    return {
      sessionId,
      sessionToken,
      userId: args.userId,
    };
  },
});

// Mutation to validate OAuth session and sign in the user
export const signInWithOAuthSession = mutation({
  args: {
    sessionToken: v.string(),
  },
  handler: async (ctx, args) => {
    console.log('🔵 Validating OAuth session token');

    // Extract session ID from token
    const tokenParts = args.sessionToken.split('_');
    if (tokenParts.length < 3 || tokenParts[0] !== 'oauth' || tokenParts[1] !== 'session') {
      throw new Error('Invalid session token format');
    }

    const sessionId = tokenParts[2];

    // Find the session in authSessions table
    const session = await ctx.db
      .query("authSessions")
      .filter((q) => q.eq(q.field("_id"), sessionId))
      .first();

    if (!session) {
      throw new Error('Session not found');
    }

    // Check if session is expired
    if (session.expirationTime < Date.now()) {
      throw new Error('Session expired');
    }

    console.log('🔵 OAuth session validated for user:', session.userId);

    // Return the user ID so the client can establish the session
    return {
      userId: session.userId,
      sessionValid: true,
    };
  },
});
