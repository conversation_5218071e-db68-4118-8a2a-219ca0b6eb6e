import { mutation } from "./_generated/server";
import { v } from "convex/values";
import { generateColor } from "./helpers/utils";
import { internal } from "./_generated/api";

export const createOrUpdateOAuthUser = mutation({
  args: {
    email: v.string(),
    name: v.optional(v.string()),
    image: v.optional(v.string()),
    firstName: v.optional(v.string()),
    lastName: v.optional(v.string()),
    googleId: v.string(),
  },
  handler: async (ctx, args) => {
    console.log('🔵 createOrUpdateOAuthUser called with:', args.email);

    // Check if user already exists
    const existingUser = await ctx.db
      .query("users")
      .filter((q) => q.eq(q.field("email"), args.email))
      .first();

    if (existingUser) {
      console.log('🔵 Existing user found, updating:', existingUser._id);
      await ctx.db.patch(existingUser._id, {
        lastLoginType: "oauth",
        image: args.image || existingUser.image,
        name: args.name || existingUser.name,
        firstName: args.firstName || existingUser.firstName,
        lastName: args.lastName || existingUser.lastName,
      });
      return existingUser._id;
    }

    console.log('🔵 Creating new user');
    
    const username = (args.email?.split("@")[0]?.toLowerCase() || "user") + Math.floor(Math.random() * 1000);
    const color = generateColor();
    
    const userId = await ctx.db.insert("users", {
      email: args.email,
      image: args.image || "",
      name: args.name || username,
      firstName: args.firstName || username,
      lastName: args.lastName || "",
      username: username,
      color: color,
      lastLoginType: "oauth",
      role: "user",
      phone: "",
      finishedSignUp: false,
    });

    console.log('🔵 New user created:', userId);
    return userId;
  },
});

export const createOAuthSession = mutation({
  args: {
    userId: v.id("users"),
  },
  handler: async (ctx, args) => {
    console.log('🔵 Creating OAuth session for user:', args.userId);

    // Use Convex Auth's internal store to create a proper session
    const sessionResult = await ctx.runMutation(internal.auth.store, {
      args: {
        type: "signIn",
        userId: args.userId,
        generateTokens: true,
      }
    });

    console.log('🔵 Convex Auth session created:', sessionResult);

    return {
      sessionResult,
      userId: args.userId,
    };
  },
});
