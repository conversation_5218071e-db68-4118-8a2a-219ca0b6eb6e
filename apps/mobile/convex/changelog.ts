import { query, mutation } from "./_generated/server";
import { v } from "convex/values";

/**
 * Get all published changelog entries, ordered by date (newest first)
 */
export const getChangelog = query({
  args: {
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const limit = args.limit ?? 10;
    
    return await ctx.db
      .query("changelog")
      .filter((q) => q.eq(q.field("isPublished"), true))
      .order("desc")
      .take(limit);
  },
});

/**
 * Get all changelog entries for admin, both published and drafts
 */
export const getAllChangelogsAdmin = query({
  args: {
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const limit = args.limit ?? 50;
    
    // No filter on isPublished so both published and drafts are returned
    return await ctx.db
      .query("changelog")
      .order("desc")
      .take(limit);
  },
});

/**
 * Get the latest changelog entry
 */
export const getLatestChangelog = query({
  args: {},
  handler: async (ctx) => {
    const latestEntry = await ctx.db
      .query("changelog")
      .filter((q) => q.eq(q.field("isPublished"), true))
      .order("desc")
      .first();
    
    return latestEntry;
  },
});

/**
 * Add a new changelog entry (admin only)
 */
export const addChangelogEntry = mutation({
  args: {
    version: v.string(),
    date: v.string(),
    isPublished: v.boolean(),
    changes: v.array(
      v.object({
        type: v.union(
          v.literal("feature"),
          v.literal("improvement"),
          v.literal("bugfix")
        ),
        description: v.string(),
      })
    ),
    summary: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    // In a real implementation, we would check if the user is an admin
    const user = await ctx.auth.getUserIdentity();
    
    if (!user) {
      throw new Error("Authentication required");
    }
    
    return await ctx.db.insert("changelog", {
      version: args.version,
      date: args.date,
      isPublished: args.isPublished,
      changes: args.changes,
      summary: args.summary,
    });
  },
});

/**
 * Update an existing changelog entry (admin only)
 */
export const updateChangelogEntry = mutation({
  args: {
    id: v.id("changelog"),
    version: v.optional(v.string()),
    date: v.optional(v.string()),
    isPublished: v.optional(v.boolean()),
    changes: v.optional(
      v.array(
        v.object({
          type: v.union(
            v.literal("feature"),
            v.literal("improvement"),
            v.literal("bugfix")
          ),
          description: v.string(),
        })
      )
    ),
    summary: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    // In a real implementation, we would check if the user is an admin
    const user = await ctx.auth.getUserIdentity();
    
    if (!user) {
      throw new Error("Authentication required");
    }
    
    // Get the user record to check if they're an admin
    const userRecord = await ctx.db
      .query("users")
      .filter((q) => q.eq(q.field("email"), user.email))
      .first();
    
    const { id, ...updates } = args;
    
    // Only include fields that were provided
    const updateData: Record<string, any> = {};
    
    if (updates.version !== undefined) updateData.version = updates.version;
    if (updates.date !== undefined) updateData.date = updates.date;
    if (updates.isPublished !== undefined) updateData.isPublished = updates.isPublished;
    if (updates.changes !== undefined) updateData.changes = updates.changes;
    if (updates.summary !== undefined) updateData.summary = updates.summary;
    
    return await ctx.db.patch(id, updateData);
  },
});

/**
 * Delete a changelog entry (admin only)
 */
export const deleteChangelogEntry = mutation({
  args: {
    id: v.id("changelog"),
  },
  handler: async (ctx, args) => {
    // In a real implementation, we would check if the user is an admin
    const user = await ctx.auth.getUserIdentity();
    
    if (!user) {
      throw new Error("Authentication required");
    }
    
    return await ctx.db.delete(args.id);
  },
}); 