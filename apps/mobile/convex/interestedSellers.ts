import { v } from "convex/values";
import { mutation, query, internalQuery } from "./_generated/server";
import { internal, api } from "./_generated/api";
import { getCurrentUser } from "./helpers/utils";

/**
 * @name submitSellerInterest
 * @description Submit information for a user interested in becoming a seller
 */
export const submitSellerInterest = mutation({
  args: {
    email: v.string(),
    category: v.optional(v.string()),
    subcategories: v.optional(v.array(v.string())),
    currentlySellingOther: v.optional(v.boolean()),
    hasSellingExperience: v.boolean(),
    platform: v.optional(v.string()),
    platformLink: v.optional(v.string()),
    monthlyRevenue: v.optional(v.string()),
    primaryGoal: v.optional(v.string()),
    socialMedia: v.optional(v.array(
      v.object({
        platform: v.string(),
        username: v.optional(v.string()),
      })
    )),
    additionalInfo: v.optional(v.string()),
    useLoopsEmail: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    const user = await getCurrentUser(ctx);

    if (!user) {
      throw new Error("User not found");
    }

    const sellerId = await ctx.db.insert("interestedSellers", {
      userId: user._id,
      email: args.email,
      hasSellingExperience: args.hasSellingExperience,
      platform: args.platform,
      platformLink: args.platformLink,
      monthlyRevenue: args.monthlyRevenue,
      primaryGoal: args.primaryGoal,
      socialMedia: args.socialMedia,
      additionalInfo: args.additionalInfo,
      submittedAt: Date.now(),
      status: "new",
    });

    await ctx.db.patch(user._id, {
      isSellerInterested: true,
    });

    if (args.useLoopsEmail) {
      await ctx.scheduler.runAfter(0, internal.emails.sendSellerInterestLoopsEmail, {
        sellerId,
        email: args.email,
        hasSellingExperience: args.hasSellingExperience,
        platform: args.platform,
        platformLink: args.platformLink,
        monthlyRevenue: args.monthlyRevenue,
        primaryGoal: args.primaryGoal,
        socialMedia: args.socialMedia,
        additionalInfo: args.additionalInfo,
      });
    } else {
      await ctx.scheduler.runAfter(0, internal.emails.sendSellerInterestEmail, {
        sellerId,
        email: args.email,
        hasSellingExperience: args.hasSellingExperience,
        platform: args.platform,
        platformLink: args.platformLink,
        monthlyRevenue: args.monthlyRevenue,
        primaryGoal: args.primaryGoal,
        socialMedia: args.socialMedia,
        additionalInfo: args.additionalInfo,
      });
    }

    return sellerId;
  },
});

/**
 * @name getSellerInterest
 * @description Get seller interest information by ID
 */
export const getSellerInterest = query({
  args: { id: v.id("interestedSellers") },
  handler: async (ctx, args) => {
    return await ctx.db.get(args.id);
  },
});

/**
 * @name getSellerInterestByUser
 * @description Get seller interest information by user ID
 */
export const getSellerInterestByUser = query({
  args: { userId: v.id("users") },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("interestedSellers")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .first();
  },
});

/**
 * @name updateSellerInterestStatus
 * @description Update the status of a seller interest
 */
export const updateSellerInterestStatus = mutation({
  args: {
    id: v.id("interestedSellers"),
    status: v.union(
      v.literal("new"),
      v.literal("contacted"),
      v.literal("approved"),
      v.literal("rejected")
    ),
    notes: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const seller = await ctx.db.get(args.id);
    if (!seller) {
      throw new Error("Seller interest not found");
    }

    return await ctx.db.patch(args.id, {
      status: args.status,
      notes: args.notes,
    });
  },
});

/**
 * @name getSellerInterestInternal
 * @description INTERNAL: Get seller interest information by ID (for use in actions)
 */
export const getSellerInterestInternal = internalQuery({
  args: { id: v.id("interestedSellers") },
  handler: async (ctx, args) => {
    return await ctx.db.get(args.id);
  },
});

/**
 * @name approveInterestedSeller
 * @description Approve a seller interest, update user role to 'seller' and status to 'approved'
 */
export const approveInterestedSeller = mutation({
  args: {
    interestedSellerId: v.id("interestedSellers"),
    userId: v.id("users"),
  },
  handler: async (ctx, args) => {
    await ctx.db.patch(args.interestedSellerId, { status: "approved" });
    await ctx.db.patch(args.userId, { role: "seller" });
    return { success: true };
  },
});

/**
 * @name rejectInterestedSeller
 * @description Reject a seller interest, update status to 'rejected'
 */
export const rejectInterestedSeller = mutation({
  args: {
    interestedSellerId: v.id("interestedSellers"),
  },
  handler: async (ctx, args) => {
    await ctx.db.patch(args.interestedSellerId, { status: "rejected" });
    return { success: true };
  },
});

/**
 * @name getInterestedSellersByStatus
 * @description Get all interested sellers by status with their user data
 */
export const getInterestedSellersByStatus = query({
  args: {
    status: v.union(
      v.literal("new"),
      v.literal("contacted"),
      v.literal("approved"),
      v.literal("rejected")
    ),
    paginationOpts: v.optional(v.object({
      numItems: v.number(),
      cursor: v.union(v.string(), v.null()),
    })),
  },
  handler: async (ctx, args) => {
    const paginationOpts = args.paginationOpts || { numItems: 25, cursor: null };

    // Query interested sellers by status
    let interestedSellersQuery = ctx.db
      .query("interestedSellers")
      .withIndex("by_status", (q) => q.eq("status", args.status))
      .order("desc");

    const { page: interestedSellers, continueCursor, isDone } = await interestedSellersQuery.paginate(paginationOpts);

    // Get user data for each interested seller
    const usersWithSellerData = await Promise.all(
      interestedSellers.map(async (seller) => {
        if (!seller.userId) return null;

        const user = await ctx.db.get(seller.userId);
        if (!user) return null;

        // Get follow counts
        let followersCount = 0;
        try {
          const followCounts = await ctx.runQuery(api.users.getFollowCounts, { userId: user._id });
          followersCount = followCounts.followers;
        } catch (error) {
          console.error(`Error getting follow counts for user ${user._id}:`, error);
        }

        return {
          _id: user._id,
          _creationTime: user._creationTime,
          username: user.username,
          name: user.name,
          role: user.role,
          email: user.email,
          followersCount,
          interestedSellerStatus: seller.status,
          interestedSellerId: seller._id,
          sellerSubmittedAt: seller.submittedAt,
        };
      })
    );

    // Filter out null values (users that don't exist)
    const validUsers = usersWithSellerData.filter(user => user !== null);

    return {
      users: validUsers,
      continueCursor,
      isDone,
    };
  },
});