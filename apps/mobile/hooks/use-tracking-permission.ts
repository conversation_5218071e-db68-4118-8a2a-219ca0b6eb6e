import { useEffect, useState, useCallback } from "react";
import { Platform } from "react-native";
import {
  requestTrackingPermissionsAsync,
  getTrackingPermissionsAsync,
} from "expo-tracking-transparency";

type TrackingPermissionStatus =
  | "unavailable"
  | "denied"
  | "authorized"
  | "restricted"
  | "not-determined"
  | "error";

export const useTrackingPermission = () => {
  const [trackingStatus, setTrackingStatus] =
    useState<TrackingPermissionStatus>("unavailable");
  const [isLoading, setIsLoading] = useState(false);

  // Use useCallback to memoize and avoid recreating functions on each render
  const checkPermission = useCallback(async () => {
    // Return early for non-iOS platforms
    if (Platform.OS !== "ios") {
      return "unavailable" as TrackingPermissionStatus;
    }

    try {
      setIsLoading(true);
      const { status } = await getTrackingPermissionsAsync();
      const typedStatus = status as TrackingPermissionStatus;

      // Update state safely
      setTrackingStatus(typedStatus);
      return typedStatus;
    } catch (error) {
      console.error("Error checking tracking permission:", error);
      return "error" as TrackingPermissionStatus;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const requestPermission = useCallback(async () => {
    // Return early for non-iOS platforms
    if (Platform.OS !== "ios") {
      return "unavailable" as TrackingPermissionStatus;
    }

    try {
      setIsLoading(true);
      const { status } = await requestTrackingPermissionsAsync();
      const typedStatus = status as TrackingPermissionStatus;

      // Update state safely
      setTrackingStatus(typedStatus);
      return typedStatus;
    } catch (error) {
      console.error("Error requesting tracking permission:", error);
      return "error" as TrackingPermissionStatus;
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Check permission on mount only
  useEffect(() => {
    let isMounted = true;

    const initialCheck = async () => {
      if (Platform.OS === "ios") {
        const status = await checkPermission();
        if (isMounted) {
          setTrackingStatus(status);
        }
      }
    };

    initialCheck();

    return () => {
      isMounted = false;
    };
  }, [checkPermission]);

  return {
    trackingStatus,
    requestPermission,
    checkPermission,
    isLoading,
  };
};
