import { useEffect, useState } from "react";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { useQuery } from "convex/react";
import { api } from "../convex/_generated/api";

const USER_STORAGE_KEY = "@user_data";

export function useUser() {
  const [storedUser, setStoredUser] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const liveUser = useQuery(api.users.viewer);

  useEffect(() => {
    // Load user from storage on mount
    loadUserFromStorage();
  }, []);

  useEffect(() => {
    // Update storage when live user changes
    if (liveUser !== undefined) {
      updateUserStorage(liveUser);
    }
  }, [liveUser]);

  const loadUserFromStorage = async () => {
    try {
      const userJson = await AsyncStorage.getItem(USER_STORAGE_KEY);
      if (userJson) {
        setStoredUser(JSON.parse(userJson));
      }
    } catch (error) {
      console.error("Error loading user from storage:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const updateUserStorage = async (userData: any) => {
    try {
      if (userData) {
        await AsyncStorage.setItem(USER_STORAGE_KEY, JSON.stringify(userData));
        setStoredUser(userData);
      } else {
        await AsyncStorage.removeItem(USER_STORAGE_KEY);
        setStoredUser(null);
      }
    } catch (error) {
      console.error("Error updating user storage:", error);
    }
  };

  const clearUser = async () => {
    try {
      await AsyncStorage.removeItem(USER_STORAGE_KEY);
      setStoredUser(null);
    } catch (error) {
      console.error("Error clearing user storage:", error);
    }
  };

  // Return live user if available, otherwise return stored user
  const user = liveUser !== undefined ? liveUser : storedUser;

  return {
    user,
    isLoading,
    clearUser,
    updateUserStorage,
  };
}
