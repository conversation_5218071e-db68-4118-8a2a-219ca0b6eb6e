import { useState, useMemo } from "react";
import { useQuery } from "convex/react";
import { api } from "../convex/_generated/api";
import { Id } from "../convex/_generated/dataModel";
import { TabType, User } from "../types";

export function useUserFollowData(
  id: Id<"users"> | undefined,
  initialTab: TabType = "followers"
) {
  // Fetch user profile
  const profileUser = useQuery(
    api.users.getUser,
    id ? { userId: id } : "skip"
  );

  // Fetch follow stats
  const followStats = useQuery(
    api.users.getFollowCounts,
    id ? { userId: id } : "skip"
  );

  // Fetch followers, following, mutuals
  const followers = useQuery(
    api.users.getFollowers,
    id ? { userId: id } : "skip"
  ) as User[] | undefined;

  const following = useQuery(
    api.users.getFollowing,
    id ? { userId: id } : "skip"
  ) as User[] | undefined;

  const mutuals = useQuery(
    api.users.getMutualFollowers,
    id ? { userId: id } : "skip"
  ) as User[] | undefined;

  // Tab state
  const [activeTab, setActiveTab] = useState<TabType>(initialTab);

  // Compute active list
  const activeList = useMemo(() => {
    if (activeTab === "followers") return followers;
    if (activeTab === "following") return following;
    if (activeTab === "mutuals") return mutuals;
    return [];
  }, [activeTab, followers, following, mutuals]);

  return {
    profileUser,
    followStats,
    followers,
    following,
    mutuals,
    activeTab,
    setActiveTab,
    activeList,
  };
} 