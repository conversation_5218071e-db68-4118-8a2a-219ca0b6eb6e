{"cli": {"version": ">= 5.9.1", "appVersionSource": "local"}, "build": {"development": {"ios": {"resourceClass": "m-medium", "credentialsSource": "remote", "distribution": "internal", "buildConfiguration": "Debug", "env": {"EXPO_NO_DOTENV": "1"}}, "developmentClient": true, "channel": "development", "env": {"EXPO_PUBLIC_API_URL": "http://localhost:3000", "APP_VARIANT": "development"}}, "preview": {"distribution": "internal", "channel": "preview"}, "production": {"ios": {"resourceClass": "m-medium", "credentialsSource": "remote", "image": "latest", "env": {}, "cache": {"key": "pod-cache-v1-{{ checksum \"ios/Podfile.lock\" }}", "paths": ["ios/Pods"]}}, "channel": "production"}}, "submit": {"production": {"ios": {"ascAppId": "6744982020", "appleTeamId": "CGZ7NDUZS2"}}}}