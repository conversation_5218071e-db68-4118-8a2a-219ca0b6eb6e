import React, { useState } from "react";
import { useEffect } from "react";
import { Platform, View, Text } from "react-native";
import {
  router,
  usePathname,
} from "expo-router";
import { useQuery } from "convex/react";
import { api } from "../../convex/_generated/api";
import { ConvexAuthProvider } from "@convex-dev/auth/react";
import { ConvexReactClient } from "convex/react";
import * as SecureStore from "expo-secure-store";
import * as SplashScreen from "expo-splash-screen";

const convex = new ConvexReactClient(
  process.env.EXPO_PUBLIC_CONVEX_URL!,
  {
    unsavedChangesWarning: false,
  },
);

const secureStorage = {
  getItem: async (key: string) => {
    try {
      return await SecureStore.getItemAsync(key);
    } catch (e) {
      console.error("SecureStore getItem error:", e);
      return null;
    }
  },
  setItem: async (key: string, value: string) => {
    try {
      await SecureStore.setItemAsync(key, value);
    } catch (e) {
      console.error("SecureStore setItem error:", e);
    }
  },
  removeItem: async (key: string) => {
    try {
      await SecureStore.deleteItemAsync(key);
    } catch (e) {
      console.error("SecureStore removeItem error:", e);
    }
  },
};

// Prevent the splash screen from auto-hiding
SplashScreen.preventAutoHideAsync().catch(() => {
  /* reloading the app might trigger some race conditions, ignore them */
});

function AuthGuardInner() {
  const pathname = usePathname();
  const [isNavigationReady, setIsNavigationReady] = useState(true);
  const user = useQuery(api.users.viewer);
  const hasActiveCode = useQuery(api.users.hasActiveVerificationCode);

  useEffect(() => {
    let isMounted = true;

    const handleNavigation = async () => {
      try {
        // Don't do anything while data is loading
        if (user === undefined || hasActiveCode === undefined) {
          return;
        }

        // Ensure component is still mounted before navigation
        if (!isMounted) return;

        const inAuthGroup = pathname.startsWith("/screens/auth");

        // If no user and not in auth group, redirect to login
        if (user === null && !inAuthGroup) {
          await router.replace("/screens/auth/login");
          return;
        }

        // If we have a user but they're in the auth group, check their status
        if (user && inAuthGroup) {
          // Check user status and redirect accordingly
          if (!user.username) {
            if (!pathname.includes("/screens/auth/choose-username")) {
              router.replace("/screens/auth/choose-username");
            }
          } else if (!user.preferences?.categories) {
            if (!pathname.includes("/screens/auth/choose-categories")) {
              router.replace("/screens/auth/choose-categories");
            }
          } else if (!user.preferences?.subcategories) {
            if (!pathname.includes("/screens/auth/choose-subcategories")) {
              router.replace("/screens/auth/choose-subcategories");
            }
          } else {
            // User has completed all steps, redirect to home
            await router.replace("/");
          }
        }

        // Hide the splash screen once navigation is determined
        SplashScreen.hideAsync().catch(() => {
          /* reloading the app might trigger some race conditions, ignore them */
        });
      } catch (error) {
        console.error("AuthGuard error:", error);
        // Hide splash screen even if there's an error
        SplashScreen.hideAsync().catch(() => {});
      }
    };

    handleNavigation();

    return () => {
      isMounted = false;
    };
  }, [user, hasActiveCode, pathname]);

  // Return null as we're just handling navigation
  return null;
}

export function AuthGuard({ children }: { children: React.ReactNode }) {
  try {
    return (
      <ConvexAuthProvider
        client={convex}
        storage={
          Platform.OS === "android" || Platform.OS === "ios"
            ? secureStorage
            : undefined
        }
      >
        <AuthGuardInner />
        {children}
      </ConvexAuthProvider>
    );
  } catch (error) {
    console.error("AuthGuard initialization error:", error);
    // Return a fallback UI instead of crashing
    return (
      <View style={{ flex: 1, justifyContent: "center", alignItems: "center" }}>
        <Text>Unable to initialize app. Please try again.</Text>
      </View>
    );
  }
}
