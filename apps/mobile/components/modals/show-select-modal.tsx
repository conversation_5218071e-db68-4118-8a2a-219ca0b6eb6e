import React from "react";
import { View, Text, StyleSheet, TouchableOpacity, Modal } from "react-native";
import { useRouter } from "expo-router";

interface ShowSelectModalProps {
  isVisible: boolean;
  onClose: () => void;
}

export default function ShowSelectModal({
  isVisible,
  onClose,
}: ShowSelectModalProps) {
  const router = useRouter();

  const handleScheduleShow = () => {
    // TODO: Navigate to show scheduling screen
    onClose();
    router.push("/screens/stream/schedule");
  };

  return (
    <Modal
      visible={isVisible}
      transparent={true}
      animationType="slide"
      onRequestClose={onClose}
    >
      <View style={styles.container}>
        <View style={styles.content}>
          <Text style={styles.title}>Oops!</Text>
          <Text style={styles.subtitle}>No scheduled shows available.</Text>
          <Text style={styles.description}>
            You will need to schedule a new show to feature products in.
          </Text>

          <TouchableOpacity
            style={styles.scheduleButton}
            onPress={handleScheduleShow}
          >
            <Text style={styles.scheduleButtonText}>Schedule a Show</Text>
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    justifyContent: "flex-end",
  },
  content: {
    backgroundColor: "#1C1C1E",
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    padding: 24,
    alignItems: "center",
    paddingBottom: 48,
  },
  title: {
    fontSize: 24,
    fontWeight: "600",
    color: "#fff",
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 17,
    fontWeight: "600",
    color: "#fff",
    marginBottom: 8,
  },
  description: {
    fontSize: 15,
    color: "#8E8E93",
    textAlign: "center",
    marginBottom: 32,
  },
  scheduleButton: {
    backgroundColor: "#1a96d2",
    paddingVertical: 16,
    paddingHorizontal: 32,
    borderRadius: 100,
    width: "100%",
  },
  scheduleButtonText: {
    color: "#000",
    fontSize: 17,
    fontWeight: "600",
    textAlign: "center",
  },
});
