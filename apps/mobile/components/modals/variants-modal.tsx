import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  TextInput,
  ScrollView,
} from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { useRouter, Stack, useLocalSearchParams } from "expo-router";

interface Variant {
  title: string;
  quantity: string;
}

export default function VariantsModal() {
  const router = useRouter();
  const params = useLocalSearchParams();
  const [variants, setVariants] = useState<Variant[]>([
    { title: "", quantity: "" },
  ]);

  useEffect(() => {
    // Initialize with existing variants if available
    if (params.variants) {
      try {
        const existingVariants = JSON.parse(params.variants as string);
        if (Array.isArray(existingVariants) && existingVariants.length > 0) {
          setVariants(existingVariants);
        }
      } catch (e) {
        console.error("Failed to parse variants:", e);
      }
    }
  }, [params.variants]);

  const getTotalQuantity = () => {
    return variants.reduce((total, variant) => {
      const qty = parseInt(variant.quantity) || 0;
      return total + qty;
    }, 0);
  };

  const handleRemoveVariant = (index: number) => {
    if (variants.length > 1) {
      setVariants(variants.filter((_, i) => i !== index));
    }
  };

  const handleUpdateVariant = (
    index: number,
    field: keyof Variant,
    value: string,
  ) => {
    const newVariants = [...variants];
    if (field === "quantity") {
      // Only allow numbers and limit to 1000
      const numValue = value.replace(/[^0-9]/g, "");
      const qty = parseInt(numValue);
      if (qty > 1000) {
        value = "1000";
      } else {
        value = numValue;
      }
    }
    newVariants[index] = {
      title: field === "title" ? value : newVariants[index]?.title || "",
      quantity:
        field === "quantity" ? value : newVariants[index]?.quantity || "",
    };
    setVariants(newVariants);
  };

  const handleAddVariant = () => {
    if (variants.length < 10) {
      // Limit to 10 variants
      setVariants([...variants, { title: "", quantity: "" }]);
    }
  };

  const handleSave = () => {
    // Filter out empty variants
    const validVariants = variants.filter((v) => v.title.trim() && v.quantity);
    if (validVariants.length > 0) {
      router.push({
        pathname: "/screens/stream/new-listing",
        params: {
          savedVariants: JSON.stringify(validVariants),
        },
      });
    } else {
      router.push("/screens/stream/new-listing");
    }
  };

  return (
    <>
      <Stack.Screen
        options={{
          headerShown: false,
        }}
      />
      <View style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity onPress={() => router.back()}>
            <Ionicons name="chevron-back" size={24} color="#fff" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Variants</Text>
          <TouchableOpacity onPress={handleSave}>
            <Text style={styles.saveButton}>Save</Text>
          </TouchableOpacity>
        </View>

        <View style={styles.infoContainer}>
          <Text style={styles.itemsCount}>Items: {variants.length}</Text>
          <Text style={styles.quantityInfo}>
            Quantity: {getTotalQuantity()} (1,000 max)
          </Text>
        </View>

        <ScrollView style={styles.variantsContainer}>
          {variants.map((variant, index) => (
            <View key={index} style={styles.variantRow}>
              <View style={styles.inputContainer}>
                <TextInput
                  style={styles.input}
                  placeholder="Title*"
                  placeholderTextColor="#8E8E93"
                  value={variant.title}
                  onChangeText={(value) =>
                    handleUpdateVariant(index, "title", value)
                  }
                />
                <TextInput
                  style={styles.input}
                  placeholder="Quantity*"
                  placeholderTextColor="#8E8E93"
                  keyboardType="numeric"
                  value={variant.quantity}
                  onChangeText={(value) =>
                    handleUpdateVariant(index, "quantity", value)
                  }
                />
              </View>
              <TouchableOpacity
                style={styles.deleteButton}
                onPress={() => handleRemoveVariant(index)}
                disabled={variants.length === 1}
              >
                <Ionicons
                  name="trash-outline"
                  size={24}
                  color={variants.length === 1 ? "#666" : "#FF453A"}
                />
              </TouchableOpacity>
            </View>
          ))}
        </ScrollView>

        <TouchableOpacity
          style={[
            styles.addButton,
            variants.length >= 10 && styles.addButtonDisabled,
          ]}
          onPress={handleAddVariant}
          disabled={variants.length >= 10}
        >
          <Ionicons name="add" size={24} color="#fff" />
        </TouchableOpacity>
      </View>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#1C1C1E",
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: 16,
    paddingTop: 60,
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: "#2C2C2E",
  },
  headerTitle: {
    fontSize: 17,
    fontWeight: "600",
    color: "#fff",
  },
  saveButton: {
    fontSize: 17,
    color: "#0A84FF",
    fontWeight: "600",
  },
  infoContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    padding: 16,
  },
  itemsCount: {
    fontSize: 17,
    color: "#fff",
  },
  quantityInfo: {
    fontSize: 17,
    color: "#8E8E93",
  },
  variantsContainer: {
    flex: 1,
    padding: 16,
  },
  variantRow: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 16,
  },
  inputContainer: {
    flex: 1,
    flexDirection: "row",
    gap: 8,
  },
  input: {
    flex: 1,
    padding: 12,
    color: "#fff",
    fontSize: 17,
    borderWidth: 1,
    backgroundColor: "#222",
    borderRadius: 12,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    borderColor: "#2C2C2E",
  },
  deleteButton: {
    marginLeft: 8,
    padding: 8,
  },
  addButton: {
    position: "absolute",
    right: 16,
    bottom: 16,
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: "#0A84FF",
    alignItems: "center",
    justifyContent: "center",
  },
  addButtonDisabled: {
    backgroundColor: "#0A84FF44",
  },
});
