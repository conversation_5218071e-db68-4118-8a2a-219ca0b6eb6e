import React, { useState, useRef } from "react";
import {
  View,
  StyleSheet,
  TouchableOpacity,
  Modal,
  Text,
  Image,
} from "react-native";
import {
  CameraView,
  CameraType,
  useCameraPermissions,
  FlashMode,
} from "expo-camera";
import { Ionicons } from "@expo/vector-icons";

interface CameraModalProps {
  isVisible: boolean;
  onClose: () => void;
  onCapture: (uris: string[]) => void;
  maxPhotos?: number;
}

export function CameraModal({
  isVisible,
  onClose,
  onCapture,
  maxPhotos = 8,
}: CameraModalProps) {
  const [type, setType] = useState<CameraType>("back");
  const [flash, setFlash] = useState<FlashMode>("off");
  const [permission, requestPermission] = useCameraPermissions();
  const [capturedImages, setCapturedImages] = useState<string[]>([]);
  const cameraRef = useRef<CameraView>(null);

  if (!permission) {
    // Camera permissions are still loading
    return null;
  }

  if (!permission.granted) {
    // Camera permissions are not granted yet
    return (
      <Modal
        animationType="slide"
        transparent={false}
        visible={isVisible}
        onRequestClose={onClose}
      >
        <View style={styles.container}>
          <Text style={styles.text}>
            We need your permission to show the camera
          </Text>
          <TouchableOpacity style={styles.button} onPress={requestPermission}>
            <Text style={styles.text}>Grant Permission</Text>
          </TouchableOpacity>
        </View>
      </Modal>
    );
  }

  const handleCapture = async () => {
    if (cameraRef.current && capturedImages.length < maxPhotos) {
      try {
        const photo = await cameraRef.current.takePictureAsync({
          quality: 1,
          base64: false,
        });
        const newImages = [...capturedImages, photo.uri];
        setCapturedImages(newImages);
        onCapture(newImages);
      } catch (error) {
        console.error("Error taking picture:", error);
      }
    }
  };

  const handleFinish = () => {
    onCapture(capturedImages);
    onClose();
  };

  const toggleCameraType = () => {
    setType((currentType) => (currentType === "back" ? "front" : "back"));
  };

  const toggleFlash = () => {
    setFlash((current) => (current === "off" ? "on" : "off"));
  };

  return (
    <Modal
      animationType="slide"
      transparent={false}
      visible={isVisible}
      onRequestClose={onClose}
    >
      <View style={styles.container}>
        <CameraView
          ref={cameraRef}
          style={styles.camera}
          facing={type}
          flash={flash}
        >
          {/* Close Button */}
          <TouchableOpacity style={styles.closeButton} onPress={handleFinish}>
            <Ionicons name="close" size={24} color="white" />
          </TouchableOpacity>

          {/* Guide Frame */}
          <View style={styles.guideFrame}>
            <Text style={styles.guideText}>
              Your product cover image should fit within the frame for best
              results.
            </Text>
            <View style={styles.frameContainer}>
              {/* Left Frame */}
              <View style={styles.frameSide}>
                <View style={[styles.frameCorner, styles.topLeft]} />
                <View style={styles.verticalLine} />
                <View style={[styles.frameCorner, styles.bottomLeft]} />
              </View>

              {/* Right Frame */}
              <View style={styles.frameSide}>
                <View style={[styles.frameCorner, styles.topRight]} />
                <View style={styles.verticalLine} />
                <View style={[styles.frameCorner, styles.bottomRight]} />
              </View>
            </View>
          </View>

          {/* Bottom Controls */}
          <View style={styles.bottomControls}>
            <TouchableOpacity
              style={styles.controlButton}
              onPress={toggleFlash}
            >
              <Ionicons
                name={flash === "off" ? "flash-off-outline" : "flash-outline"}
                size={24}
                color="white"
              />
            </TouchableOpacity>

            <TouchableOpacity
              style={[
                styles.captureButton,
                capturedImages.length >= maxPhotos &&
                  styles.captureButtonDisabled,
              ]}
              onPress={handleCapture}
              disabled={capturedImages.length >= maxPhotos}
            >
              <View style={styles.captureOuter}>
                <View style={styles.captureInner} />
              </View>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.controlButton}
              onPress={toggleCameraType}
            >
              <Ionicons name="camera-reverse-outline" size={24} color="white" />
            </TouchableOpacity>
          </View>

          {/* Image Slots */}
          <View style={styles.imageSlots}>
            {Array(maxPhotos)
              .fill(0)
              .map((_, index) => (
                <View key={index} style={styles.imageSlot}>
                  {capturedImages[index] && (
                    <Image
                      source={{ uri: capturedImages[index] }}
                      style={styles.thumbnailImage}
                    />
                  )}
                </View>
              ))}
          </View>
        </CameraView>
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#1C1C1E",
  },
  camera: {
    flex: 1,
  },
  closeButton: {
    position: "absolute",
    top: 50,
    right: 20,
    padding: 8,
    zIndex: 1,
  },
  guideFrame: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    paddingHorizontal: 20,
  },
  guideText: {
    color: "white",
    fontSize: 16,
    textAlign: "center",
    marginBottom: 20,
    fontWeight: "500",
    paddingHorizontal: 20,
  },
  frameContainer: {
    width: "100%",
    height: "60%",
    flexDirection: "row",
    justifyContent: "space-between",
    position: "absolute",
  },
  frameSide: {
    height: "100%",
    justifyContent: "space-between",
    alignItems: "center",
  },
  frameCorner: {
    width: 20,
    height: 20,
    borderColor: "white",
    position: "relative",
  },
  topLeft: {
    borderLeftWidth: 3,
    borderTopWidth: 3,
  },
  topRight: {
    borderRightWidth: 3,
    borderTopWidth: 3,
  },
  bottomLeft: {
    borderLeftWidth: 3,
    borderBottomWidth: 3,
  },
  bottomRight: {
    borderRightWidth: 3,
    borderBottomWidth: 3,
  },
  verticalLine: {
    width: 1,
    flex: 1,
    backgroundColor: "white",
    marginVertical: 10,
  },
  bottomControls: {
    flexDirection: "row",
    justifyContent: "space-around",
    alignItems: "center",
    paddingBottom: 20,
  },
  controlButton: {
    padding: 10,
    width: 44,
    height: 44,
    alignItems: "center",
    justifyContent: "center",
  },
  captureButton: {
    alignItems: "center",
  },
  captureOuter: {
    width: 74,
    height: 74,
    borderRadius: 37,
    backgroundColor: "rgba(255, 255, 255, 0.3)",
    justifyContent: "center",
    alignItems: "center",
    borderWidth: 2,
    borderColor: "white",
  },
  captureInner: {
    width: 66,
    height: 66,
    borderRadius: 33,
    backgroundColor: "white",
  },
  imageSlots: {
    flexDirection: "row",
    justifyContent: "space-around",
    paddingHorizontal: 16,
    paddingBottom: 40,
  },
  imageSlot: {
    width: 40,
    height: 40,
    borderRadius: 8,
    backgroundColor: "transparent",
    borderWidth: 1,
    borderColor: "rgba(255,255,255,0.3)",
    overflow: "hidden",
  },
  thumbnailImage: {
    width: "100%",
    height: "100%",
    borderRadius: 8,
  },
  text: {
    color: "white",
    fontSize: 16,
  },
  button: {
    padding: 16,
    backgroundColor: "#2C2C2E",
    borderRadius: 8,
    marginTop: 16,
  },
  captureButtonDisabled: {
    opacity: 0.5,
  },
});
