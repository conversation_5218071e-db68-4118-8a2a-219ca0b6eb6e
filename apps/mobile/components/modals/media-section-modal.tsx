import React, { useEffect, useState } from "react";
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  Alert,
} from "react-native";
import { Ionicons } from "@expo/vector-icons";
import * as ImagePicker from "expo-image-picker";
import { CameraModal } from "./camera-modal";

interface MediaSelectionModalProps {
  isVisible: boolean;
  onClose: () => void;
  onSelectOption: (
    option: "upload" | "photo" | "video" | "scan",
    uris?: string | string[],
  ) => void;
}

export function MediaSelectionModal({
  isVisible,
  onClose,
  onSelectOption,
}: MediaSelectionModalProps) {
  const [showCamera, setShowCamera] = useState(false);

  useEffect(() => {
    (async () => {
      const { status } =
        await ImagePicker.requestMediaLibraryPermissionsAsync();
      const { status: cameraStatus } =
        await ImagePicker.requestCameraPermissionsAsync();
      if (status !== "granted" || cameraStatus !== "granted") {
        Alert.alert(
          "Permission Required",
          "Sorry, we need camera and media library permissions to make this work!",
        );
      }
    })();
  }, []);

  const handleUploadPress = async () => {
    try {
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.All,
        allowsEditing: true,
        quality: 1,
        videoMaxDuration: 30,
      });

      if (!result.canceled && result.assets[0]) {
        onSelectOption("upload", result.assets[0].uri);
        onClose();
      }
    } catch (error) {
      console.error("Error picking media:", error);
      Alert.alert("Error", "Failed to pick media. Please try again.");
    }
  };

  const handleVideoPress = async () => {
    try {
      const result = await ImagePicker.launchCameraAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Videos,
        allowsEditing: true,
        videoMaxDuration: 30,
        quality: 1,
      });

      if (!result.canceled && result.assets[0]) {
        onSelectOption("video", result.assets[0].uri);
        onClose();
      }
    } catch (error) {
      console.error("Error recording video:", error);
      Alert.alert("Error", "Failed to record video. Please try again.");
    }
  };

  const handleCameraCapture = (uris: string[]) => {
    onSelectOption("photo", uris);
    setShowCamera(false);
  };

  return (
    <>
      <Modal
        animationType="slide"
        transparent={true}
        visible={isVisible && !showCamera}
        onRequestClose={onClose}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.header}>
              <Text style={styles.headerTitle}>Add Media</Text>
              <TouchableOpacity onPress={onClose} style={styles.closeButton}>
                <Ionicons name="close" size={24} color="#fff" />
              </TouchableOpacity>
            </View>

            <TouchableOpacity style={styles.option} onPress={handleUploadPress}>
              <View style={styles.iconContainer}>
                <Ionicons name="cloud-upload-outline" size={24} color="#fff" />
              </View>
              <View style={styles.optionContent}>
                <Text style={styles.optionTitle}>Upload Media</Text>
                <Text style={styles.optionSubtitle}>
                  Videos must be 30s or less
                </Text>
              </View>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.option}
              onPress={() => setShowCamera(true)}
            >
              <View style={styles.iconContainer}>
                <Ionicons name="camera-outline" size={24} color="#fff" />
              </View>
              <View style={styles.optionContent}>
                <Text style={styles.optionTitle}>Take Photos</Text>
                <Text style={styles.optionSubtitle}>Take up to 8 photos</Text>
              </View>
            </TouchableOpacity>

            <TouchableOpacity style={styles.option} onPress={handleVideoPress}>
              <View style={styles.iconContainer}>
                <Ionicons name="videocam-outline" size={24} color="#fff" />
              </View>
              <View style={styles.optionContent}>
                <Text style={styles.optionTitle}>Record Video</Text>
                <Text style={styles.optionSubtitle}>Maximum duration: 30s</Text>
              </View>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>

      <CameraModal
        isVisible={showCamera}
        onClose={() => {
          setShowCamera(false);
          onClose();
        }}
        onCapture={handleCameraCapture}
        maxPhotos={8}
      />
    </>
  );
}

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    justifyContent: "flex-end",
  },
  modalContent: {
    backgroundColor: "#1C1C1E",
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    padding: 16,
    minHeight: 300,
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    marginBottom: 24,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: "600",
    color: "#fff",
  },
  closeButton: {
    padding: 8,
  },
  option: {
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: "#2C2C2E",
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: "#2C2C2E",
    alignItems: "center",
    justifyContent: "center",
    marginRight: 16,
  },
  optionContent: {
    flex: 1,
  },
  optionTitle: {
    fontSize: 17,
    color: "#fff",
    marginBottom: 4,
  },
  optionSubtitle: {
    fontSize: 13,
    color: "#8E8E93",
  },
});
