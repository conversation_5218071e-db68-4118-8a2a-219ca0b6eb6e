import React from "react";
import { View, Text, StyleSheet, TouchableOpacity, Modal } from "react-native";
import { Ionicons } from "@expo/vector-icons";

interface SellingFormatModalProps {
  isVisible: boolean;
  onClose: () => void;
  onSelect: (option: string) => void;
  selectedOption: string;
}

interface FormatOption {
  title: string;
  description: string;
}

const SELLING_FORMATS: FormatOption[] = [
  {
    title: "Auction",
    description: "Products sold to the highest bidder.",
  },
  {
    title: "Buy Now",
    description: "Products sold at a fixed price. Includes flash sales.",
  },
  {
    title: "Singles",
    description: "Products sold individually.",
  },
  {
    title: "Breaks",
    description:
      "Parts of an unopened pack or box of products sold to the highest bidder.",
  },
  {
    title: "Surprise Sets",
    description:
      "Bundles of products sold without the buyer knowing exactly what they'll receive.",
  },
];

export function SellingFormatModal({
  isVisible,
  onClose,
  onSelect,
  selectedOption,
}: SellingFormatModalProps) {
  return (
    <Modal
      animationType="slide"
      transparent={true}
      visible={isVisible}
      onRequestClose={onClose}
    >
      <View style={styles.modalOverlay}>
        <View style={styles.modalContent}>
          <View style={styles.header}>
            <Text style={styles.headerTitle}>Primary Selling Format</Text>
            <TouchableOpacity onPress={onClose} style={styles.closeButton}>
              <Ionicons name="close" size={24} color="#fff" />
            </TouchableOpacity>
          </View>

          <View style={styles.optionsList}>
            {SELLING_FORMATS.map((format) => (
              <TouchableOpacity
                key={format.title}
                style={styles.option}
                onPress={() => {
                  onSelect(format.title);
                  onClose();
                }}
              >
                <View style={styles.optionContent}>
                  <Text style={styles.optionTitle}>{format.title}</Text>
                  <Text style={styles.optionDescription}>
                    {format.description}
                  </Text>
                </View>
                <View style={styles.radioContainer}>
                  {selectedOption === format.title ? (
                    <View style={styles.radioSelected}>
                      <View style={styles.radioInner} />
                    </View>
                  ) : (
                    <View style={styles.radioUnselected} />
                  )}
                </View>
              </TouchableOpacity>
            ))}
          </View>
        </View>
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    justifyContent: "flex-end",
  },
  modalContent: {
    backgroundColor: "#1C1C1E",
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    padding: 16,
    minHeight: 530,
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    marginBottom: 24,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: "600",
    color: "#fff",
  },
  closeButton: {
    padding: 8,
  },
  optionsList: {
    flex: 1,
  },
  option: {
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: "#2C2C2E",
  },
  optionContent: {
    flex: 1,
    marginRight: 16,
  },
  optionTitle: {
    fontSize: 17,
    color: "#fff",
    marginBottom: 4,
  },
  optionDescription: {
    fontSize: 15,
    color: "#8E8E93",
  },
  radioContainer: {
    width: 24,
    height: 24,
    justifyContent: "center",
    alignItems: "center",
  },
  radioSelected: {
    width: 24,
    height: 24,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: "#fff",
    alignItems: "center",
    justifyContent: "center",
  },
  radioInner: {
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: "#fff",
  },
  radioUnselected: {
    width: 24,
    height: 24,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: "#8E8E93",
  },
});
