import React from "react";
import { View, Text, StyleSheet, TouchableOpacity, Modal } from "react-native";
import { Ionicons } from "@expo/vector-icons";

interface ScaleSelectorSheetProps {
  isVisible: boolean;
  onClose: () => void;
  onSelect: (scale: string) => void;
  scales: string[];
  selectedScale: string;
  title?: string;
}

export function ScaleSelectorSheet({
  isVisible,
  onClose,
  onSelect,
  scales,
  selectedScale,
  title = "Select Scale",
}: ScaleSelectorSheetProps) {
  return (
    <Modal
      animationType="slide"
      transparent={true}
      visible={isVisible}
      onRequestClose={onClose}
    >
      <View style={styles.modalOverlay}>
        <View style={styles.modalContent}>
          <View style={styles.header}>
            <Text style={styles.headerTitle}>{title}</Text>
            <TouchableOpacity onPress={onClose} style={styles.closeButton}>
              <Ionicons name="close" size={24} color="#fff" />
            </TouchableOpacity>
          </View>

          <View style={styles.optionsList}>
            {scales.map((scale) => (
              <TouchableOpacity
                key={scale}
                style={styles.option}
                onPress={() => {
                  onSelect(scale);
                  onClose();
                }}
              >
                <View style={styles.iconContainer}>
                  {selectedScale === scale ? (
                    <Ionicons name="checkmark-circle" size={24} color="#fff" />
                  ) : (
                    <Ionicons name="radio-button-off" size={24} color="#fff" />
                  )}
                </View>
                <View style={styles.optionContent}>
                  <Text style={styles.optionTitle}>{scale}</Text>
                </View>
              </TouchableOpacity>
            ))}
          </View>
        </View>
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    justifyContent: "flex-end",
  },
  modalContent: {
    backgroundColor: "#1C1C1E",
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    padding: 16,
    minHeight: 400,
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    marginBottom: 24,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: "600",
    color: "#fff",
  },
  closeButton: {
    padding: 8,
  },
  optionsList: {
    flex: 1,
  },
  option: {
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: "#2C2C2E",
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: "#2C2C2E",
    alignItems: "center",
    justifyContent: "center",
    marginRight: 16,
  },
  optionContent: {
    flex: 1,
  },
  optionTitle: {
    fontSize: 17,
    color: "#fff",
  },
});
