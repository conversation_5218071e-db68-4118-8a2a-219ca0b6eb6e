import React from "react";
import { View, Text, StyleSheet, FlatList } from "react-native";
import { useQuery } from "convex/react";
import { api } from "../../convex/_generated/api";
import LiveStreamCard from "../live-stream-card";
import { useRouter } from "expo-router";
import { Doc, Id } from "../../convex/_generated/dataModel";

// Define the shape of the data we're expecting
interface BookmarkedStreamData {
  _id: Id<"streams">;
  title: string;
  category: string;
  subcategory: string;
  thumbnail: string | null;
  viewerCount: number;
  streamer: {
    username: string | undefined;
    profileImage: string | null;
    color: string;
  };
}

export default function BookmarkList() {
  const router = useRouter();
  const bookmarkedStreamsData = useQuery(api.bookmarks.getBookmarkedStreams);

  // Safely cast the data with proper type checking
  const bookmarkedStreams = bookmarkedStreamsData as
    | BookmarkedStreamData[]
    | undefined;

  if (!bookmarkedStreams || bookmarkedStreams.length === 0) {
    return (
      <View style={styles.emptyStateContainer}>
        <Text style={styles.emptyStateTitle}>No bookmarked streams yet</Text>
        <Text style={styles.emptyStateSubtitle}>
          Bookmark your favorite streams to watch them later
        </Text>
      </View>
    );
  }

  return (
    <FlatList
      data={bookmarkedStreams}
      style={styles.list}
      contentContainerStyle={styles.listContent}
      keyExtractor={(item) => item._id.toString()}
      renderItem={({ item }) => (
        <LiveStreamCard
          username={item.streamer.username || "Anonymous"}
          title={item.title || "Untitled Stream"}
          category={item.category || "Uncategorized"}
          subcategory={item.subcategory || "General"}
          viewerCount={item.viewerCount || 0}
          thumbnailUrl={
            item.thumbnail ||
            "https://placehold.co/600x400/333/white?text=No+Image"
          }
          profileImageUrl={item.streamer.profileImage || null}
          userColor={item.streamer.color || "#2C2C2E"}
          streamId={item._id}
          isBookmarked={true}
          onPress={() => router.push(`/screens/stream/${item._id}`)}
        />
      )}
    />
  );
}

const styles = StyleSheet.create({
  list: {
    flex: 1,
    width: "100%",
  },
  listContent: {
    padding: 16,
  },
  emptyStateContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 20,
  },
  emptyStateTitle: {
    color: "#fff",
    fontSize: 18,
    fontWeight: "bold",
    textAlign: "center",
    marginBottom: 8,
  },
  emptyStateSubtitle: {
    color: "#8E8E93",
    fontSize: 14,
    textAlign: "center",
  },
});
