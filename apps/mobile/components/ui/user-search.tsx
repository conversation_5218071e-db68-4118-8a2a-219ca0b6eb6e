import React, { useState, useCallback, useRef, useEffect } from "react";
import {
  View,
  TextInput,
  StyleSheet,
  ActivityIndicator,
  FlatList,
  Text,
  Image,
  TouchableOpacity,
  Modal,
  SafeAreaView,
  Pressable,
  TextInputProps,
} from "react-native";
import { useQuery } from "convex/react";
import { api } from "../../convex/_generated/api";
import debounce from "lodash/debounce";
import { Ionicons } from "@expo/vector-icons";
import { useRouter } from "expo-router";
import { Id } from "../../convex/_generated/dataModel";
import UserSearchSkeleton from "../skeletons/user-search-skeleton";
import UserAvatar from "./user-avatar";
import { getImageUrl } from "../../lib/utils";

export interface UserSearchProps extends TextInputProps {
  onSelectUser?: (user: any) => void;
  placeholder?: string;
  style?: any;
  initialActive?: boolean;
  initialQuery?: string;
  onClose?: () => void;
  onFocus?: () => void;
  onBlur?: () => void;
}

const stringToColor = (str: string) => {
  let hash = 0;
  for (let i = 0; i < str.length; i++) {
    hash = str.charCodeAt(i) + ((hash << 5) - hash);
  }
  const hue = Math.abs(hash % 360);
  return `hsl(${hue}, 70%, 40%)`;
};

const getInitials = (name: string) => {
  if (!name) return "?";
  return name.charAt(0).toUpperCase();
};

export default function UserSearch({
  onSelectUser,
  placeholder = "Search users...",
  style,
  initialActive = false,
  initialQuery = "",
  onClose,
  onFocus,
  onBlur,
}: UserSearchProps) {
  const router = useRouter();
  const inputRef = useRef<TextInput>(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [debouncedQuery, setDebouncedQuery] = useState("");
  const [isActive, setIsActive] = useState(initialActive);

  useEffect(() => {
    setIsActive(initialActive);
    if (initialActive && initialQuery) {
      setSearchQuery(initialQuery);
      setDebouncedQuery(initialQuery);
    }
  }, [initialActive, initialQuery]);

  const debouncedSetQuery = useCallback(
    debounce((query: string) => {
      setDebouncedQuery(query);
    }, 300),
    [],
  );

  const searchResults = useQuery(api.users.searchUsers, {
    searchQuery: debouncedQuery,
    paginationOpts: {
      numItems: 10,
      cursor: null,
    },
  });

  const handleSearch = (text: string) => {
    setSearchQuery(text);
    debouncedSetQuery(text);
  };

  const handleFocus = () => {
    setSearchQuery("");
    setDebouncedQuery("");
    setIsActive(true);
  };

  const handleClose = (shouldSaveSearch = false) => {
    setIsActive(false);
    if (!shouldSaveSearch) {
      setSearchQuery("");
      setDebouncedQuery("");
    }
    if (onClose) {
      onClose();
    }
  };

  const handleUserSelect = (user: any) => {
    if (onSelectUser) {
      onSelectUser(user);
      handleClose(false);
    } else {
      if (user._id) {
        const userId = user._id as Id<"users">;
        handleClose(true);
        router.push({
          pathname: "/(tabs)/user/[username]",
          params: { username: user.username },
        });
      }
    }
  };

  const renderAvatar = (user: any) => {
    return (
      <UserAvatar
        image={getImageUrl(user.image)}
        username={user.username}
        color={user.color}
      />
    );
  };

  // Render each user item
  const renderUserItem = ({ item }: { item: any }) => (
    <TouchableOpacity
      style={styles.userItem}
      onPress={() => handleUserSelect(item)}
    >
      {renderAvatar(item)}
      <View style={styles.userInfo}>
        <Text style={styles.userName}>{item.username || "Unnamed User"}</Text>
        <View style={styles.userMetaInfo}>
          {/* <Text style={styles.userFollowers}>
            <Text style={{ color: "#fff" }}>{item.followers || 0} </Text>
            follower{(item.followers || 0) === 1 ? "" : "s"}
          </Text> */}
        </View>
      </View>
    </TouchableOpacity>
  );

  const renderContent = () => {
    // Show skeleton when we have a search query and results are undefined
    if (debouncedQuery && searchResults === undefined) {
      return <UserSearchSkeleton />;
    }

    // If we have results (even empty) or no query, show the list
    return (
      <FlatList
        data={searchResults?.users || []}
        renderItem={renderUserItem}
        keyExtractor={(item) => item._id}
        style={styles.resultsList}
        keyboardShouldPersistTaps="handled"
        ListEmptyComponent={
          <Text style={styles.noResults}>
            {debouncedQuery ? "No users found" : "Start typing to search users"}
          </Text>
        }
      />
    );
  };

  return (
    <View style={[styles.container, style]}>
      <Pressable onPress={() => handleFocus()} style={styles.searchButton}>
        <View style={styles.searchBar}>
          <Ionicons
            name="search"
            size={20}
            color="#8E8E93"
            style={styles.searchIcon}
          />
          <Text style={styles.searchPlaceholder}>{placeholder}</Text>
        </View>
      </Pressable>

      <Modal
        visible={isActive}
        animationType="fade"
        presentationStyle="fullScreen"
      >
        <SafeAreaView style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <View style={styles.activeSearchBar}>
              <Ionicons
                name="search"
                size={20}
                color="#8E8E93"
                style={styles.searchIcon}
              />
              <TextInput
                ref={inputRef}
                style={styles.searchInput}
                placeholder={placeholder}
                placeholderTextColor="#8E8E93"
                value={searchQuery}
                onChangeText={handleSearch}
                autoFocus
                autoCapitalize="none"
                autoCorrect={false}
                returnKeyType="search"
                onFocus={onFocus}
                onBlur={onBlur}
              />
              {searchQuery.length > 0 && (
                <TouchableOpacity
                  onPress={() => {
                    setSearchQuery("");
                    setDebouncedQuery("");
                    inputRef.current?.focus();
                  }}
                  style={styles.clearButton}
                >
                  <Ionicons name="close-circle" size={20} color="#8E8E93" />
                </TouchableOpacity>
              )}
            </View>
            <TouchableOpacity
              onPress={() => handleClose(false)}
              style={styles.cancelButton}
            >
              <Text style={styles.cancelText}>Cancel</Text>
            </TouchableOpacity>
          </View>

          <View style={styles.resultsContainer}>{renderContent()}</View>
        </SafeAreaView>
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    width: "100%",
  },
  searchButton: {
    width: "100%",
  },
  searchBar: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#222",
    borderRadius: 10,
    paddingHorizontal: 10,
    height: 40,
  },
  searchPlaceholder: {
    color: "#8E8E93",
    fontSize: 16,
    fontWeight: "400",
  },
  modalContainer: {
    flex: 1,
    backgroundColor: "#1C1C1E",
  },
  modalHeader: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  activeSearchBar: {
    flex: 1,
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#222",
    borderRadius: 10,
    paddingHorizontal: 10,
    height: 40,
    marginRight: 8,
  },
  searchIcon: {
    marginRight: 6,
    opacity: 0.6,
  },
  searchInput: {
    flex: 1,
    height: 40,
    color: "#fff",
    fontSize: 16,
    backgroundColor: "#222",
    borderRadius: 12,
  },
  clearButton: {
    padding: 4,
  },
  cancelButton: {
    paddingHorizontal: 8,
  },
  cancelText: {
    color: "#fff",
    fontSize: 16,
  },
  tabsContainer: {
    flexDirection: "row",
    paddingHorizontal: 16,
    borderBottomColor: "#2C2C2E",
    borderBottomWidth: 1,
  },
  tabItem: {
    marginRight: 24,
    paddingVertical: 12,
  },
  tabText: {
    color: "#8E8E93",
    fontSize: 16,
  },
  activeTab: {
    color: "#fff",
  },
  activeTabIndicator: {
    position: "absolute",
    bottom: -1,
    left: 0,
    right: 0,
    height: 2,
    backgroundColor: "#fff",
  },
  resultsContainer: {
    flex: 1,
  },
  loader: {
    marginTop: 20,
  },
  resultsList: {
    flex: 1,
  },
  userItem: {
    flexDirection: "row",
    alignItems: "center",
    padding: 12,
    borderBottomWidth: 1,
    borderBottomColor: "#2C2C2E",
  },
  avatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: "#2C2C2E",
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
  },
  avatarFallback: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: "center",
    alignItems: "center",
  },
  avatarText: {
    color: "#FFFFFF",
    fontSize: 18,
    fontWeight: "600",
  },
  userInfo: {
    marginLeft: 12,
    flex: 1,
  },
  userName: {
    color: "#fff",
    fontSize: 16,
    fontWeight: "500",
  },
  userMetaInfo: {
    flexDirection: "row",
    alignItems: "center",
    gap: 8,
    marginTop: 2,
  },
  userHandle: {
    color: "#8E8E93",
    fontSize: 14,
  },
  userFollowers: {
    fontSize: 12,
    color: "#8E8E93",
  },
  userEmail: {
    color: "#8E8E93",
    fontSize: 14,
    marginTop: 2,
  },
  noResults: {
    color: "#8E8E93",
    textAlign: "center",
    marginTop: 20,
    fontSize: 16,
  },
});
