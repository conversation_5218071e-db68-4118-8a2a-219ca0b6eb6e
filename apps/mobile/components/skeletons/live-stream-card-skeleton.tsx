import React from "react";
import { View, StyleSheet, Animated } from "react-native";
import { useEffect, useRef } from "react";

export default function LiveStreamCardSkeleton() {
  const fadeAnim = useRef(new Animated.Value(0.3)).current;

  useEffect(() => {
    const animation = Animated.loop(
      Animated.sequence([
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: true,
        }),
        Animated.timing(fadeAnim, {
          toValue: 0.3,
          duration: 1000,
          useNativeDriver: true,
        }),
      ]),
    );

    animation.start();

    return () => {
      animation.stop();
    };
  }, [fadeAnim]);

  return (
    <View style={styles.container}>
      <Animated.View
        style={[styles.thumbnailContainer, { opacity: fadeAnim }]}
      />

      <View style={styles.infoContainer}>
        <Animated.View style={[styles.profileImage, { opacity: fadeAnim }]} />
        <View style={styles.textContainer}>
          <Animated.View
            style={[styles.usernameSkeleton, { opacity: fadeAnim }]}
          />
          <Animated.View
            style={[styles.titleSkeleton, { opacity: fadeAnim }]}
          />
          <Animated.View
            style={[styles.categorySkeleton, { opacity: fadeAnim }]}
          />
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    width: "100%",
    marginBottom: 16,
    backgroundColor: "#1C1C1E",
    borderRadius: 12,
    overflow: "hidden",
  },
  thumbnailContainer: {
    aspectRatio: 16 / 9,
    backgroundColor: "#2C2C2E",
  },
  infoContainer: {
    flexDirection: "row",
    padding: 12,
  },
  profileImage: {
    width: 40,
    height: 40,
    borderRadius: 20,
    marginRight: 12,
    backgroundColor: "#2C2C2E",
  },
  textContainer: {
    flex: 1,
    gap: 8,
  },
  usernameSkeleton: {
    height: 14,
    width: "40%",
    backgroundColor: "#2C2C2E",
    borderRadius: 4,
  },
  titleSkeleton: {
    height: 16,
    width: "80%",
    backgroundColor: "#2C2C2E",
    borderRadius: 4,
  },
  categorySkeleton: {
    height: 12,
    width: "60%",
    backgroundColor: "#2C2C2E",
    borderRadius: 4,
  },
});
