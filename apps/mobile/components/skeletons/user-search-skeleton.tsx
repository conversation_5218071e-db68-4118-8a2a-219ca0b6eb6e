import React from "react";
import { View, StyleSheet, Animated } from "react-native";
import { useEffect, useRef } from "react";

export default function UserSearchSkeleton() {
  const fadeAnim = useRef(new Animated.Value(0.3)).current;

  useEffect(() => {
    const animation = Animated.loop(
      Animated.sequence([
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: true,
        }),
        Animated.timing(fadeAnim, {
          toValue: 0.3,
          duration: 1000,
          useNativeDriver: true,
        }),
      ]),
    );

    animation.start();

    return () => {
      animation.stop();
    };
  }, [fadeAnim]);

  // Create an array of 5 items for the skeleton list
  const skeletonItems = Array(5).fill(null);

  return (
    <View style={styles.container}>
      {skeletonItems.map((_, index) => (
        <View key={index} style={styles.userItem}>
          {/* Avatar skeleton */}
          <Animated.View style={[styles.avatar, { opacity: fadeAnim }]} />

          {/* User info skeleton */}
          <View style={styles.userInfo}>
            {/* Username skeleton */}
            <Animated.View
              style={[styles.usernameSkeleton, { opacity: fadeAnim }]}
            />

            {/* Followers skeleton */}
            <View style={styles.userMetaInfo}>
              <Animated.View
                style={[styles.followersSkeleton, { opacity: fadeAnim }]}
              />
            </View>
          </View>
        </View>
      ))}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#1C1C1E",
  },
  userItem: {
    flexDirection: "row",
    alignItems: "center",
    padding: 12,
    borderBottomWidth: 1,
    borderBottomColor: "#2C2C2E",
  },
  avatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: "#2C2C2E",
  },
  userInfo: {
    marginLeft: 12,
    flex: 1,
  },
  usernameSkeleton: {
    height: 16,
    width: "40%",
    backgroundColor: "#2C2C2E",
    borderRadius: 4,
    marginBottom: 6,
  },
  userMetaInfo: {
    flexDirection: "row",
    alignItems: "center",
    gap: 8,
    marginTop: 2,
  },
  followersSkeleton: {
    height: 12,
    width: "30%",
    backgroundColor: "#2C2C2E",
    borderRadius: 4,
  },
});
