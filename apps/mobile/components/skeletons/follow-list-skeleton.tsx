import React from "react";
import { View, StyleSheet, Animated, TouchableOpacity } from "react-native";
import { useEffect, useRef } from "react";
import { Ionicons } from "@expo/vector-icons";
import { SafeAreaView } from "react-native-safe-area-context";

export default function FollowListSkeleton() {
  const fadeAnim = useRef(new Animated.Value(0.3)).current;

  useEffect(() => {
    const animation = Animated.loop(
      Animated.sequence([
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: true,
        }),
        Animated.timing(fadeAnim, {
          toValue: 0.3,
          duration: 1000,
          useNativeDriver: true,
        }),
      ]),
    );

    animation.start();

    return () => {
      animation.stop();
    };
  }, [fadeAnim]);

  const renderUserItemSkeleton = () => (
    <View style={styles.userItem}>
      <View style={styles.userInfo}>
        <Animated.View style={[styles.avatar, { opacity: fadeAnim }]} />
        <View style={styles.userDetails}>
          <Animated.View
            style={[
              styles.skeletonText,
              styles.username,
              { opacity: fadeAnim },
            ]}
          />
          <Animated.View
            style={[styles.skeletonText, styles.name, { opacity: fadeAnim }]}
          />
        </View>
      </View>
      <Animated.View style={[styles.followButton, { opacity: fadeAnim }]} />
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity style={styles.backButton}>
          <Ionicons name="chevron-back" size={24} color="#fff" />
        </TouchableOpacity>
        <Animated.View
          style={[
            styles.skeletonText,
            styles.headerTitle,
            { opacity: fadeAnim },
          ]}
        />
        <View style={styles.backButton} />
      </View>

      {/* Tabs */}
      <View style={styles.tabs}>
        {["followers", "following", "mutuals"].map((_, index) => (
          <View key={index} style={styles.tab}>
            <Animated.View
              style={[
                styles.skeletonText,
                styles.tabText,
                { opacity: fadeAnim },
              ]}
            />
          </View>
        ))}
      </View>

      {/* User List */}
      <View style={styles.listContent}>
        {Array(8)
          .fill(null)
          .map((_, index) => (
            <React.Fragment key={index}>
              {renderUserItemSkeleton()}
            </React.Fragment>
          ))}
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#1C1C1E",
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: "#333",
  },
  backButton: {
    width: 40,
    height: 40,
    justifyContent: "center",
    alignItems: "center",
  },
  headerTitle: {
    width: 120,
    height: 20,
    backgroundColor: "#333",
    borderRadius: 4,
  },
  tabs: {
    flexDirection: "row",
    borderBottomWidth: 1,
    borderBottomColor: "#333",
  },
  tab: {
    flex: 1,
    paddingVertical: 16,
    alignItems: "center",
  },
  tabText: {
    width: 80,
    height: 20,
    backgroundColor: "#333",
    borderRadius: 4,
  },
  listContent: {
    padding: 16,
  },
  userItem: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    marginBottom: 16,
  },
  userInfo: {
    flexDirection: "row",
    alignItems: "center",
    flex: 1,
  },
  avatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: "#333",
  },
  userDetails: {
    marginLeft: 12,
    flex: 1,
  },
  skeletonText: {
    backgroundColor: "#333",
    borderRadius: 4,
  },
  username: {
    width: "60%",
    height: 16,
    marginBottom: 8,
  },
  name: {
    width: "40%",
    height: 14,
  },
  followButton: {
    width: 80,
    height: 36,
    backgroundColor: "#333",
    borderRadius: 8,
  },
});
