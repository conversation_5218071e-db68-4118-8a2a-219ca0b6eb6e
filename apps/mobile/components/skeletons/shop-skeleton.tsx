import React from "react";
import { View, StyleSheet, Animated, Dimensions } from "react-native";
import { useEffect, useRef } from "react";

export default function ShopSkeleton() {
  const fadeAnim = useRef(new Animated.Value(0.3)).current;

  useEffect(() => {
    const animation = Animated.loop(
      Animated.sequence([
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: true,
        }),
        Animated.timing(fadeAnim, {
          toValue: 0.3,
          duration: 1000,
          useNativeDriver: true,
        }),
      ]),
    );

    animation.start();

    return () => {
      animation.stop();
    };
  }, [fadeAnim]);

  // Create a grid of 6 product items (3 rows of 2)
  const renderProductSkeleton = () => (
    <Animated.View style={[styles.productCard, { opacity: fadeAnim }]}>
      <View style={styles.productImage} />
      <View style={styles.productInfo}>
        <View style={styles.productName} />
        <View style={styles.productPrice} />
      </View>
    </Animated.View>
  );

  return (
    <View style={styles.container}>
      <View style={styles.productList}>
        <View style={styles.productRow}>
          {renderProductSkeleton()}
          {renderProductSkeleton()}
        </View>
        <View style={styles.productRow}>
          {renderProductSkeleton()}
          {renderProductSkeleton()}
        </View>
        <View style={styles.productRow}>
          {renderProductSkeleton()}
          {renderProductSkeleton()}
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#1C1C1E",
  },
  productList: {
    padding: 8,
  },
  productRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 16,
  },
  productCard: {
    width: (Dimensions.get("window").width - 32) / 2 - 8,
    backgroundColor: "#1C1C1E",
    borderRadius: 12,
    overflow: "hidden",
    borderWidth: 1,
    borderColor: "#2C2C2E",
  },
  productImage: {
    width: "100%",
    height: 200,
    backgroundColor: "#2C2C2E",
  },
  productInfo: {
    padding: 12,
    gap: 8,
  },
  productName: {
    height: 14,
    backgroundColor: "#2C2C2E",
    borderRadius: 4,
    width: "80%",
  },
  productPrice: {
    height: 16,
    backgroundColor: "#2C2C2E",
    borderRadius: 4,
    width: "40%",
  },
});
