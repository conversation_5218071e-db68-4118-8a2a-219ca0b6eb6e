import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Image,
  KeyboardAvoidingView,
  Platform,
  SafeAreaView,
  TouchableWithoutFeedback,
  Keyboard,
  Dimensions,
  Linking,
} from "react-native";
import { useAuthActions } from "@convex-dev/auth/react";
import { useConvexAuth } from "convex/react";
import { useMutation, useQuery } from "convex/react";
import { api } from "../../../convex/_generated/api";
import { z } from "zod";
import { STORAGE_PREFIX } from "../../../constants/storage";
import { router } from "expo-router";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { Ionicons } from "@expo/vector-icons";
import { LinearGradient } from "expo-linear-gradient";
import * as WebBrowser from "expo-web-browser";
import { signInSchema, signUpSchema, emailSchema } from "../../../lib/validators";

const { width, height } = Dimensions.get("window");

export default function LoginScreen() {
  const { signIn } = useAuthActions();
  const { isAuthenticated, isLoading: authLoading } = useConvexAuth();
  
  const [email, setEmail] = useState<string>("");
  const [password, setPassword] = useState<string>("");
  const [confirmPassword, setConfirmPassword] = useState<string>("");
  const [name, setName] = useState<string>("");
  const [flow, setFlow] = useState<"signIn" | "signUp">("signIn");
  const [emailSubmitted, setEmailSubmitted] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [errors, setErrors] = useState<{ [key: string]: string }>({});
  const [showPassword, setShowPassword] = useState<boolean>(false);

  const update = useMutation(api.users.updateUserLoginType);

  const lastLoginType =
    useQuery(api.users.getUserLoginType, {
      email: email.toLowerCase().trim(),
    }) || null;

  const isNewUser = emailSubmitted && !lastLoginType;

  useEffect(() => {
    console.log('🔵 Auth state:', { isAuthenticated, authLoading });
  }, [isAuthenticated, authLoading]);

  // Handle deep links from OAuth callback
  useEffect(() => {
    const handleDeepLink = (url: string) => {
      console.log('🔵 Deep link received:', url);

      if (url.startsWith('liveciety://oauth/success')) {
        const urlObj = new URL(url);
        const code = urlObj.searchParams.get('code');

        if (code) {
          console.log('🔵 Processing OAuth callback with code...');
          handleOAuthCallback(code);
        }
      }
    };

    // Listen for deep links
    const subscription = Linking.addEventListener('url', (event) => {
      handleDeepLink(event.url);
    });

    // Check if app was opened with a deep link
    Linking.getInitialURL().then((url) => {
      if (url) {
        handleDeepLink(url);
      }
    });

    return () => {
      subscription?.remove();
    };
  }, []);

  const handleOAuthCallback = async (code: string) => {
    console.log('🔵 Handling OAuth callback with code:', code.substring(0, 20) + '...');
    setIsLoading(true);
    setErrors({});

    try {
      console.log('🔵 Calling signIn with Google provider...');

      // Complete the OAuth flow with Convex Auth
      const result = await signIn('google', {
        code: code,
        redirectUri: 'https://decisive-perch-342.convex.site/api/auth/mobile/callback/google',
      });

      console.log('🔵 signIn result:', result);
      console.log('🔵 OAuth callback completed successfully!');

      // Check authentication state
      console.log('🔵 Current auth state after signIn:', { isAuthenticated, authLoading });

    } catch (error) {
      console.error('🔴 OAuth callback error:', error);
      console.error('🔴 Error details:', JSON.stringify(error, null, 2));
      setErrors({ general: 'Failed to complete authentication. Please try again.' });
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    const loadSavedEmail = async () => {
      const savedEmail = await AsyncStorage.getItem(
        `${STORAGE_PREFIX}user_email`,
      );
      if (savedEmail) {
        setEmail(savedEmail);
      }
    };
    loadSavedEmail();
  }, []);

  useEffect(() => {
    if (isNewUser) {
      setFlow("signUp");
    } else if (emailSubmitted) {
      setFlow("signIn");
    }
  }, [isNewUser, emailSubmitted]);

  const validateForm = () => {
    setErrors({});
    try {
      if (flow === "signUp") {
        signUpSchema.parse({
          email: email.toLowerCase().trim(),
          password,
          confirmPassword,
          flow,
          loginType: "password",
          name,
          role: "user",
        });
      } else {
        signInSchema.parse({
          email: email.toLowerCase().trim(),
          password,
          flow,
          loginType: "password",
        });
      }
      return true;
    } catch (error) {
      if (error instanceof z.ZodError) {
        const newErrors: { [key: string]: string } = {};
        error.errors.forEach((err) => {
          const path = err.path.join(".");
          newErrors[path] = err.message;
        });
        setErrors(newErrors);
      }
      return false;
    }
  };

  const handleEmailSubmit = async () => {
    try {
      emailSchema.parse(email);
      await AsyncStorage.setItem(`${STORAGE_PREFIX}user_email`, email);
      setEmailSubmitted(true);
      setErrors({});
    } catch (error) {
      if (error instanceof z.ZodError) {
        setErrors({
          email: error.errors[0]?.message || "Invalid email address",
        });
      }
    }
  };

  const handleUseAnotherEmail = async () => {
    setEmailSubmitted(false);
    setEmail("");
    setPassword("");
    setConfirmPassword("");
    setName("");
    await AsyncStorage.removeItem(`${STORAGE_PREFIX}user_email`);
  };

  const handlePasswordSubmit = async () => {
    if (!validateForm()) {
      return;
    }

    setIsLoading(true);

    try {
      const credentials = {
        email: email.toLowerCase().trim(),
        password,
        flow,
        loginType: "password" as const,
        ...(flow === "signUp" ? { name } : {}),
      };

      await signIn("password", credentials);
      await update({
        email: email.toLowerCase().trim(),
        loginType: "password",
      });

      // If this is a new signup, redirect to username selection
      if (flow === "signUp") {
        router.replace("/screens/auth/choose-username");
      } else {
        router.replace("/");
      }
    } catch (error) {
      if (error instanceof Error) {
        setErrors({ password: "Invalid password" });
      } else {
        setErrors({
          general:
            flow === "signIn"
              ? "Could not sign in, did you mean to sign up?"
              : "Could not sign up, did you mean to sign in?",
        });
      }
    } finally {
      setIsLoading(false);
    }
  };

  const handleGoogleSignIn = async () => {
    console.log('🔵 Google button clicked!');
    setIsLoading(true);
    setErrors({});
    try {
      console.log('🔵 Starting mobile-optimized Google OAuth flow...');

      // Use our custom mobile callback URL that will redirect to the mobile app
      const redirectUri = 'https://decisive-perch-342.convex.site/api/auth/mobile/callback/google';
      const clientId = '************-60qvqlm5rhonrrtk9ca31rgd2echr288.apps.googleusercontent.com';

      // Add a state parameter to identify this as a mobile request
      const state = 'mobile_app_' + Math.random().toString(36).substring(7);

      // Build Google OAuth URL
      const googleOAuthUrl = `https://accounts.google.com/o/oauth2/v2/auth?` +
        `client_id=${clientId}&` +
        `redirect_uri=${encodeURIComponent(redirectUri)}&` +
        `response_type=code&` +
        `scope=${encodeURIComponent('openid profile email')}&` +
        `access_type=offline&` +
        `prompt=consent&` +
        `state=${encodeURIComponent(state)}`;

      console.log('🔵 Opening Google OAuth URL:', googleOAuthUrl);

      // Open OAuth flow in WebBrowser and wait for completion
      const result = await WebBrowser.openAuthSessionAsync(
        googleOAuthUrl,
        'liveciety://' // This will catch any redirect to our app scheme
      );

      console.log('🔵 OAuth result:', result);

      if (result.type === 'success') {
        console.log('🔵 OAuth completed, checking authentication status...');

        // Check if we got a deep link URL with success
        if (result.url && result.url.includes('liveciety://oauth/success')) {
          console.log('🔵 Got deep link URL:', result.url);

          // Parse the URL to check for success
          const url = new URL(result.url);
          const success = url.searchParams.get('success');

          if (success === 'true') {
            console.log('🔵 OAuth completed successfully on backend!');
            // Wait a moment for the session to be processed and stored
            await new Promise(resolve => setTimeout(resolve, 2000));
            console.log('🔵 Checking authentication state...');
          } else {
            console.error('🔴 OAuth failed on backend');
            setErrors({ general: 'Authentication failed on server. Please try again.' });
          }
        } else {
          console.log('🔵 No deep link URL, waiting for session...');
          // Wait a moment for the session to be processed
          await new Promise(resolve => setTimeout(resolve, 2000));
        }

        console.log('🔵 OAuth flow completed successfully!');
      } else if (result.type === 'cancel') {
        console.log('🔵 User cancelled OAuth flow');
        return;
      } else {
        throw new Error('OAuth flow failed');
      }
    } catch (error) {
      console.error('🔴 Google sign-in error:', error);
      setErrors({ general: 'An error occurred during Google sign in. Please try again.' });
    } finally {
      console.log('🔵 Setting loading to false');
      setIsLoading(false);
    }
  };

  const handleAppleSignIn = async () => {
    setIsLoading(true);
    setErrors({});
    try {
      await signIn('apple');
      router.replace('/');
    } catch (error) {
      setErrors({ general: 'An error occurred during Apple sign in. Please try again.' });
      console.error('Apple sign-in error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      {/* Background gradients */}
      <View style={styles.gradientContainer}>
        <LinearGradient
          colors={[
            "rgba(255,255,255,0.08)",
            "rgba(255,255,255,0.02)",
            "transparent",
          ]}
          style={[styles.gradient, { transform: [{ rotate: "-45deg" }] }]}
          start={{ x: 0.55, y: 0.31 }}
          end={{ x: 0.5, y: 1 }}
        />
        <LinearGradient
          colors={[
            "rgba(255,255,255,0.06)",
            "rgba(255,255,255,0.02)",
            "transparent",
          ]}
          style={[
            styles.gradient,
            { transform: [{ rotate: "-45deg" }], left: "5%" },
          ]}
          start={{ x: 0.5, y: 0 }}
          end={{ x: 0.5, y: 1 }}
        />
      </View>

      <KeyboardAvoidingView
        behavior={Platform.OS === "ios" ? "padding" : "height"}
        style={styles.keyboardAvoidingView}
      >
        <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
            <View style={styles.content}>
              <View style={styles.header}>
                <Image
                  style={styles.logo}
                  source={require("@icon.png")}
                  resizeMode="contain"
                />
              </View>

              <View style={styles.formContainer}>
                <View style={styles.titleContainer}>
                {isNewUser ? (
                  <Text style={styles.title}>Sign up for an account</Text>
                ) : (
                  <Text style={styles.title}>Login to Liveciety</Text>
                )}
                </View>
                <View style={styles.form}>
                  {!emailSubmitted ? (
                    <>
                      <TextInput
                        style={styles.input}
                        placeholder="Enter your email"
                        placeholderTextColor="#6B7280"
                        value={email}
                        onChangeText={setEmail}
                        autoCapitalize="none"
                        keyboardType="email-address"
                        autoComplete="email"
                      />
                      {errors.email && (
                        <Text style={styles.errorText}>{errors.email}</Text>
                      )}
                      <TouchableOpacity
                        style={styles.button}
                        onPress={handleEmailSubmit}
                        disabled={isLoading}
                      >
                        <Text style={styles.buttonText}>Continue</Text>
                      </TouchableOpacity>
                    </>
                  ) : (
                    <>
                      {isNewUser && (
                        <>
                          <TextInput
                            style={styles.input}
                            placeholder="Your name (required)*"
                            placeholderTextColor="#6B7280"
                            value={name}
                            onChangeText={setName}
                            autoCapitalize="none"
                          />
                          {errors.name && (
                            <Text style={styles.errorText}>{errors.name}</Text>
                          )}
                        </>
                      )}

                      <View style={styles.passwordInputContainer}>
                        <TextInput
                          style={[styles.input, styles.passwordInput]}
                          placeholder={
                            isNewUser
                              ? "Create a password"
                              : "Enter your password"
                          }
                          placeholderTextColor="#6B7280"
                          value={password}
                          onChangeText={setPassword}
                          secureTextEntry={!showPassword}
                          autoComplete={isNewUser ? "new-password" : "password"}
                        />
                        <TouchableOpacity
                          style={styles.eyeIcon}
                          onPress={() => setShowPassword(!showPassword)}
                        >
                          <Ionicons
                            name={showPassword ? "eye-off" : "eye"}
                            size={24}
                            color="#6B7280"
                          />
                        </TouchableOpacity>
                      </View>
                      {errors.password && (
                        <Text style={styles.errorText}>{errors.password}</Text>
                      )}

                      {isNewUser && (
                        <>
                          <View style={styles.passwordInputContainer}>
                            <TextInput
                              style={[styles.input, styles.passwordInput]}
                              placeholder="Confirm password"
                              placeholderTextColor="#6B7280"
                              value={confirmPassword}
                              onChangeText={setConfirmPassword}
                              secureTextEntry={!showPassword}
                              autoComplete="new-password"
                            />
                            <TouchableOpacity
                              style={styles.eyeIcon}
                              onPress={() => setShowPassword(!showPassword)}
                            >
                              <Ionicons
                                name={showPassword ? "eye-off" : "eye"}
                                size={24}
                                color="#6B7280"
                              />
                            </TouchableOpacity>
                          </View>
                          {errors.confirmPassword && (
                            <Text style={styles.errorText}>
                              {errors.confirmPassword}
                            </Text>
                          )}
                          <Text style={styles.helperText}>
                            Password must be at least 8 characters long
                          </Text>
                        </>
                      )}

                      <View style={styles.buttonContainer}>
                        <TouchableOpacity
                          style={styles.button}
                          onPress={handlePasswordSubmit}
                          disabled={isLoading}
                        >
                          <Text style={styles.buttonText}>
                            {isLoading
                              ? "Loading..."
                              : isNewUser
                                ? "Create Account"
                                : "Sign In"}
                          </Text>
                        </TouchableOpacity>
                        {lastLoginType === "password" && !isNewUser && (
                          <View
                            style={styles.lastUsedBadge}
                          >
                            <Text style={styles.lastUsedText}>Last used</Text>
                          </View>
                        )}
                      </View>

                      <TouchableOpacity
                        style={styles.linkButton}
                        onPress={handleUseAnotherEmail}
                      >
                        <Text style={styles.linkText}>← Use another email</Text>
                      </TouchableOpacity>

                      <View style={styles.divider}>
                        <View style={styles.dividerLine} />
                        <Text style={styles.dividerText}>or</Text>
                        <View style={styles.dividerLine} />
                      </View>

                      <View style={styles.socialButtonsContainer}>
                        <View style={styles.socialButtonWrapper}>
                          <TouchableOpacity
                            style={styles.socialButton}
                            onPress={handleGoogleSignIn}
                            disabled={isLoading}
                          >
                            <Ionicons name="logo-google" size={20} color="#fff" style={styles.socialIcon} />
                            <Text style={styles.socialButtonText}>
                              {isNewUser ? "Register with Google" : "Continue with Google"}
                            </Text>
                          </TouchableOpacity>
                          {lastLoginType === 'oauth' && !isNewUser && (
                            <View style={[styles.lastUsedBadge, styles.socialLastUsedBadge]}>
                              <Text style={styles.lastUsedText}>Last used</Text>
                            </View>
                          )}
                        </View>
                        <View style={styles.socialButtonWrapper}>
                          <TouchableOpacity
                            style={styles.socialButton}
                            onPress={handleAppleSignIn}
                            disabled={isLoading}
                          >
                            <Ionicons name="logo-apple" size={20} color="#fff" style={styles.socialIcon} />
                            <Text style={styles.socialButtonText}>
                              {isNewUser ? "Register with Apple" : "Continue with Apple"}
                            </Text>
                          </TouchableOpacity>
                        </View>
                      </View>
                    </>
                  )}
                </View>
              </View>

              <View style={styles.footer}>
                <Text style={styles.footerText}>
                  By clicking continue, you agree to our{" "}
                  <Text style={styles.link}>Terms of Service</Text> and{" "}
                  <Text style={styles.link}>Privacy Policy</Text>
                </Text>
              </View>
            </View>
        </TouchableWithoutFeedback>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#18181B",
    height: "100%",
    display: "flex",
    flexDirection: "column",
    justifyContent: "space-between",
  },
  keyboardAvoidingView: {
    flex: 1,
    display: "flex",
    flexDirection: "column",
    justifyContent: "space-between",
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
    paddingBottom: 20,
    display: "flex",
    alignItems: "center",
    flexDirection: "column",
    justifyContent: "space-between",
    height: "100%",
  },
  header: {
    alignItems: "center",
    marginBottom: 24,
    marginTop: 24,
  },
  logo: {
    width: 64,
    height: 64,
    marginBottom: 24,
  },
  titleContainer: {
    marginBottom: 24,
  },
  title: {
    fontSize: 28,
    fontWeight: "bold",
    color: "#fff",
    textAlign: "center",
  },
  formContainer: {
    width: "100%",
    maxWidth: 400,
    marginBottom: 20,
    marginTop: 24,
  },
  form: {
    gap: 4,
  },
  gradientContainer: {
    position: "absolute",
    width: width,
    height: height,
    opacity: 0.5,
  },
  gradient: {
    position: "absolute",
    width: width * 1.4,
    height: height * 1.4,
    top: -height * 0.2,
    borderRadius: width,
  },
  input: {
    height: 40,
    backgroundColor: "#1A1A1A",
    borderWidth: 1,
    borderColor: "#333",
    borderRadius: 8,
    paddingHorizontal: 16,
    fontSize: 16,
    color: "#fff",
    width: "100%",
  },
  passwordInputContainer: {
    position: "relative",
    width: "100%",
  },
  passwordInput: {
    paddingRight: 40,
  },
  eyeIcon: {
    position: "absolute",
    right: 12,
    top: 8,
  },
  buttonContainer: {
    position: "relative",
    width: "100%",
  },
  button: {
    height: 40,
    backgroundColor: "#fff",
    borderRadius: 8,
    alignItems: "center",
    justifyContent: "center",
    marginTop: 8,
  },
  buttonText: {
    color: "#000",
    fontSize: 16,
    fontWeight: "600",
  },
  lastUsedBadge: {
    position: "absolute",
    top: -8,
    right: -8,
    borderRadius: 4,
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderWidth: 1,
    borderColor: "#175c9c",
    backgroundColor: "#1a90cb",
  },
  lastUsedText: {
    color: "#fff",
    fontSize: 12,
  },
  socialLastUsedBadge: {
    backgroundColor: "#fff",
  },
  linkButton: {
    alignSelf: "flex-start",
    paddingVertical: 8,
  },
  linkText: {
    color: "#6B7280",
    fontSize: 14,
  },
  errorText: {
    color: "#EF4444",
    fontSize: 12,
  },
  helperText: {
    color: "#6B7280",
    fontSize: 12,
  },
  divider: {
    flexDirection: "row",
    alignItems: "center",
    marginVertical: 16,
  },
  dividerLine: {
    flex: 1,
    height: 1,
    backgroundColor: "#333",
  },
  dividerText: {
    color: "#6B7280",
    fontSize: 14,
    marginHorizontal: 8,
  },
  socialButtonsContainer: {
    flexDirection: "column",
    gap: 12,
    width: "100%",
  },
  socialButtonWrapper: {
    position: "relative",
    width: "100%",
  },
  socialButton: {
    width: "100%",
    height: 40,
    borderWidth: 1,
    borderColor: "#333",
    borderRadius: 8,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: "transparent",
    paddingHorizontal: 16,
  },
  socialIcon: {
    marginRight: 12,
  },
  socialButtonText: {
    color: "#fff",
    fontSize: 14,
    fontWeight: "500",
    textAlign: "center",
  },
  footer: {
    width: "100%",
    maxWidth: 400,
    marginTop: "auto",
  },
  footerText: {
    textAlign: "center",
    color: "#6B7280",
    fontSize: 10,
  },
  link: {
    color: "#6B7280",
    textDecorationLine: "underline",
  },
});
