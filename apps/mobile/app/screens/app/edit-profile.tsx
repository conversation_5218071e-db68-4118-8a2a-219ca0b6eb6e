import React, { useState } from "react";
import {
  View,
  Text,
  TextInput,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  StatusBar,
  Alert,
  KeyboardAvoidingView,
  Platform,
  TouchableWithoutFeedback,
  Keyboard,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { useRouter } from "expo-router";
import { Image } from "expo-image";
import * as ImagePicker from "expo-image-picker";
import { Camera } from "react-native-feather";
import { useMutation, useQuery } from "convex/react";
import { api } from "../../../convex/_generated/api";
import { Id } from "../../../convex/_generated/dataModel";
import { Ionicons } from "@expo/vector-icons";

const EditProfile = () => {
  const router = useRouter();
  const user = useQuery(api.users.viewer);
  const [name, setName] = useState(user?.name || "");
  const [username, setUsername] = useState(user?.username || "");
  const [bio, setBio] = useState(user?.bio || "");
  const [storageId, setStorageId] = useState<Id<"_storage"> | undefined>(
    user?.image as Id<"_storage"> | undefined,
  );
  const [coverStorageId, setCoverStorageId] = useState<
    Id<"_storage"> | undefined
  >(user?.coverImage as Id<"_storage"> | undefined);
  const [uploading, setUploading] = useState(false);

  const generateUploadUrl = useMutation(api.files.generateUploadUrl);
  const updateProfile = useMutation(api.users.updateProfile);

  const handleBack = () => {
    router.back();
  };

  const handleSave = async () => {
    try {
      if (uploading) return;

      await updateProfile({
        name,
        username,
        bio: bio || undefined,
        image: storageId,
        coverImage: coverStorageId,
      });

      Alert.alert("Success", "Profile updated successfully");
      router.back();
    } catch (error) {
      console.error("Error updating profile:", error);
      Alert.alert(
        "Error",
        error instanceof Error ? error.message : "Failed to update profile",
      );
    }
  };

  const uploadImage = async (uri: string, isCover: boolean = false) => {
    try {
      setUploading(true);
      // Get the upload URL from Convex
      const uploadUrl = await generateUploadUrl();

      if (!uploadUrl) {
        throw new Error("Failed to generate upload URL");
      }

      // Get the image data
      const response = await fetch(uri);
      const blob = await response.blob();

      // Upload the file to Convex storage
      const result = await fetch(uploadUrl, {
        method: "POST",
        body: blob,
        headers: {
          "Content-Type": blob.type || "application/octet-stream",
        },
      });

      if (!result.ok) {
        throw new Error(`Upload failed: ${result.status} ${result.statusText}`);
      }

      // Get the storage ID from the response JSON
      const { storageId } = await result.json();

      if (!storageId) {
        throw new Error("Failed to get storage ID from upload response");
      }

      // Set the appropriate storage ID based on whether it's a cover image
      if (isCover) {
        setCoverStorageId(storageId as Id<"_storage">);
      } else {
        setStorageId(storageId as Id<"_storage">);
      }

      return storageId;
    } catch (error) {
      console.error("Error uploading image:", error);
      Alert.alert(
        "Error",
        error instanceof Error
          ? error.message
          : "Failed to upload image. Please try again.",
      );
      return null;
    } finally {
      setUploading(false);
    }
  };

  const handleImagePick = async (isCover: boolean = false) => {
    try {
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: isCover ? [16, 9] : [1, 1],
        quality: 1,
      });

      if (!result.canceled && result.assets[0]?.uri) {
        await uploadImage(result.assets[0].uri, isCover);
      }
    } catch (error) {
      console.error("Error picking image:", error);
      Alert.alert(
        "Error",
        error instanceof Error
          ? error.message
          : "Failed to pick image. Please try again.",
      );
    }
  };

  return (
    <SafeAreaView style={styles.container} edges={["top"]}>
      <StatusBar barStyle="light-content" />

      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={handleBack} style={styles.backButton}>
          <Ionicons name="chevron-back" size={24} color="#fff" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Edit Profile</Text>
        <TouchableOpacity onPress={handleSave} disabled={uploading}>
          <Text style={[styles.saveButton, uploading && styles.disabledButton]}>
            {uploading ? "Uploading..." : "Save"}
          </Text>
        </TouchableOpacity>
      </View>

      <KeyboardAvoidingView
        behavior={Platform.OS === "ios" ? "padding" : "height"}
        style={styles.keyboardAvoidingView}
        keyboardVerticalOffset={Platform.OS === "ios" ? 0 : 25}
      >
        <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
          <ScrollView
            style={styles.scrollView}
            showsVerticalScrollIndicator={false}
            contentContainerStyle={styles.scrollViewContent}
          >
            {/* Cover Image & Profile Section */}
            <View style={styles.coverContainer}>
              <TouchableOpacity
                onPress={() => handleImagePick(true)}
                style={styles.coverImageContainer}
                disabled={uploading}
              >
                {user?.coverImageUrl ? (
                  <Image
                    source={{ uri: user.coverImageUrl }}
                    style={styles.coverImage}
                    contentFit="cover"
                  />
                ) : (
                  <View
                    style={[styles.coverImage, styles.placeholderCoverImage]}
                  >
                    <Camera width={40} height={40} color="#666" />
                    <Text style={styles.placeholderText}>Add Cover Photo</Text>
                  </View>
                )}
              </TouchableOpacity>

              <View style={styles.profileOverlay}>
                <View style={styles.profileContent}>
                  <TouchableOpacity
                    onPress={() => handleImagePick(false)}
                    style={styles.imageContainer}
                    disabled={uploading}
                  >
                    {user?.avatarUrl ? (
                      <Image
                        source={{ uri: user.avatarUrl }}
                        style={styles.profileImage}
                        contentFit="cover"
                      />
                    ) : (
                      <View style={styles.placeholderImage}>
                        <Camera width={40} height={40} color="#fff" />
                      </View>
                    )}
                    {uploading && (
                      <View style={styles.uploadingOverlay}>
                        <Text style={styles.uploadingText}>Uploading...</Text>
                      </View>
                    )}
                  </TouchableOpacity>
                </View>
              </View>
            </View>

            {/* Personal Details Section */}
            <View style={styles.detailsSection}>
              <Text style={styles.sectionTitle}>Personal Details</Text>

              <View style={styles.inputContainer}>
                <Text style={styles.label}>
                  Name<Text style={styles.required}>*</Text>
                </Text>
                <TextInput
                  style={styles.input}
                  value={name}
                  onChangeText={setName}
                  placeholder="Enter your name"
                  placeholderTextColor="#666"
                />
              </View>

              <View style={styles.inputContainer}>
                <Text style={styles.label}>
                  Username<Text style={styles.required}>*</Text>
                </Text>
                <TextInput
                  style={styles.input}
                  value={username}
                  onChangeText={setUsername}
                  placeholder="Enter username"
                  placeholderTextColor="#666"
                  maxLength={30}
                />
                <Text style={styles.charCount}>{username.length}/30</Text>
              </View>

              <View style={styles.inputContainer}>
                <Text style={styles.label}>Bio</Text>
                <TextInput
                  style={[styles.input, styles.bioInput]}
                  value={bio}
                  onChangeText={setBio}
                  placeholder="Write something about yourself"
                  placeholderTextColor="#666"
                  multiline
                  numberOfLines={4}
                />
              </View>
            </View>
          </ScrollView>
        </TouchableWithoutFeedback>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#000",
  },
  scrollView: {
    flex: 1,
  },
  scrollViewContent: {
    flexGrow: 1,
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: 16,
    paddingVertical: 8,
    backgroundColor: "#000",
    borderBottomWidth: 0,
  },
  backButton: {
    padding: 8,
    marginLeft: -8,
  },
  headerTitle: {
    color: "#fff",
    fontSize: 17,
    fontWeight: "600",
    letterSpacing: -0.5,
  },
  saveButton: {
    color: "#007AFF",
    fontSize: 17,
    fontWeight: "400",
  },
  coverContainer: {
    height: 150,
    position: "relative",
    backgroundColor: "#1C1C1E",
    marginBottom: 80, // Space for profile picture overflow
  },
  coverImageContainer: {
    width: "100%",
    height: "100%",
    backgroundColor: "#1C1C1E",
  },
  coverImage: {
    width: "100%",
    height: "100%",
  },
  placeholderCoverImage: {
    width: "100%",
    height: "100%",
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "#1C1C1E",
  },
  placeholderText: {
    color: "#666",
    marginTop: 8,
    fontSize: 16,
    fontWeight: "500",
  },
  profileOverlay: {
    position: "absolute",
    bottom: -60,
    left: 0,
    right: 0,
    alignItems: "center",
    backgroundColor: "transparent",
  },
  profileContent: {
    alignItems: "center",
    justifyContent: "center",
  },
  imageContainer: {
    width: 120,
    height: 120,
    borderRadius: 60,
    backgroundColor: "#1C1C1E",
    justifyContent: "center",
    alignItems: "center",
    borderWidth: 4,
    borderColor: "#000",
    overflow: "hidden",
  },
  profileImage: {
    width: "100%",
    height: "100%",
    borderRadius: 60,
  },
  placeholderImage: {
    justifyContent: "center",
    alignItems: "center",
    width: "100%",
    height: "100%",
    backgroundColor: "#1C1C1E",
  },
  uploadingOverlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: "rgba(0, 0, 0, 0.7)",
    justifyContent: "center",
    alignItems: "center",
  },
  uploadingText: {
    color: "#fff",
    fontSize: 14,
    fontWeight: "600",
  },
  detailsSection: {
    padding: 16,
    flex: 1,
    paddingBottom: Platform.OS === "ios" ? 40 : 20,
  },
  sectionTitle: {
    color: "#fff",
    fontSize: 28,
    fontWeight: "700",
    marginBottom: 24,
    letterSpacing: -0.5,
  },
  inputContainer: {
    marginBottom: 24,
  },
  label: {
    color: "#fff",
    fontSize: 17,
    fontWeight: "400",
    marginBottom: 8,
    opacity: 0.9,
  },
  required: {
    color: "#FF453A",
    marginLeft: 4,
  },
  input: {
    backgroundColor: "#1C1C1E",
    borderRadius: 12,
    padding: 16,
    color: "#fff",
    fontSize: 17,
    fontWeight: "400",
  },
  bioInput: {
    height: 120,
    textAlignVertical: "top",
    paddingTop: 16,
  },
  charCount: {
    color: "#666",
    fontSize: 13,
    textAlign: "right",
    marginTop: 8,
  },
  disabledButton: {
    opacity: 0.5,
  },
  keyboardAvoidingView: {
    flex: 1,
  },
});

export default EditProfile;
