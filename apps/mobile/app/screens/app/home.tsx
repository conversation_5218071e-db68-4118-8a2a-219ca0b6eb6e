import React, { useState } from "react";
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Share,
} from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { SafeAreaView } from "react-native-safe-area-context";
import { router } from "expo-router";
import UserSearch from "../../../components/ui/user-search";
import { useQuery } from "convex/react";
import { api } from "../../../convex/_generated/api";
import LiveStreamCard from "../../../components/live-stream-card";
import LiveStreamCardSkeleton from "../../../components/skeletons/live-stream-card-skeleton";
import { categories as allCategories } from "../../../lib/constants";
import { getImageUrl } from "../../../lib/utils";

export default function HomeScreen() {
  const [isSearchActive, setIsSearchActive] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState("For You");

  const streams = useQuery(api.streams.listActiveStreams, {});
  const scheduledResult = useQuery(api.streams.listByCategory, {
    category: undefined,
    paginationOpts: { numItems: 10, cursor: null },
  });
  const scheduledStreams = (scheduledResult?.page || []).filter(
    (s: any) => s.status === "scheduled" && !s.isLive,
  );

  const currentUser = useQuery(api.users.viewer, {});
  const followedUsers =
    useQuery(
      api.users.getFollowing,
      currentUser?._id ? { userId: currentUser._id } : "skip",
    ) || [];

  const followedSellers = (Array.isArray(followedUsers) ? followedUsers : []).filter(
    (u: any) => u.role === "seller",
  );

  // Get followed categories from user preferences
  const followedCategoryIds = currentUser?.preferences?.categories || [];
  // Map category ids to display names
  const followedCategories = followedCategoryIds
    .map((catId: string) => allCategories.find((c) => c.id === catId)?.title)
    .filter(Boolean);

  // Compose the display categories
  const displayCategories = [
    "For You",
    ...(followedSellers.length > 0 ? ["Followed Sellers"] : []),
    ...followedCategories,
  ];

  // Loading states
  const isLiveStreamsLoading = streams === undefined;
  const isScheduledStreamsLoading = scheduledResult === undefined;

  // Event handlers
  const handleShare = async () => {
    try {
      const result = await Share.share({
        message: "Check out Liveciety!",
        url: "https://liveciety.com",
      });
    } catch (error) {
      console.error(error);
    }
  };

  const handleNotificationsPress = () => {
    router.push("/screens/app/notifications");
  };

  // Skeleton array for loading state
  const skeletonArray = Array(3).fill(null);

  // Helper: get category id from title
  const getCategoryIdByTitle = (title: string) => {
    const cat = allCategories.find((c) => c.title === title);
    return cat ? cat.id : undefined;
  };

  // Filter streams based on selected tab
  let filteredLiveStreams = streams || [];
  let filteredScheduledStreams = scheduledStreams || [];

  if (selectedCategory === "Followed Sellers") {
    const followedSellerIds = new Set(followedSellers.map((s: any) => s._id));
    filteredLiveStreams = filteredLiveStreams.filter((stream: any) =>
      followedSellerIds.has(stream.userId),
    );
    filteredScheduledStreams = filteredScheduledStreams.filter((stream: any) =>
      followedSellerIds.has(stream.userId),
    );
  } else if (selectedCategory !== "For You") {
    const selectedCategoryId = getCategoryIdByTitle(selectedCategory);
    if (selectedCategoryId) {
      filteredLiveStreams = filteredLiveStreams.filter(
        (stream: any) => stream.category === selectedCategoryId,
      );
      filteredScheduledStreams = filteredScheduledStreams.filter(
        (stream: any) => stream.category === selectedCategoryId,
      );
    }
  }

  return (
    <SafeAreaView edges={["top"]} style={styles.container}>
      <View style={styles.header}>
        <View style={styles.searchContainer}>
          <UserSearch
            placeholder="Search Liveciety"
            style={styles.userSearch}
          />
        </View>
        {!isSearchActive && (
          <View style={styles.headerIcons}>
            <TouchableOpacity
              style={styles.iconButton}
              onPress={handleNotificationsPress}
            >
              <View>
                <Ionicons name="notifications-outline" size={24} color="#fff" />
              </View>
            </TouchableOpacity>
            <TouchableOpacity style={styles.iconButton} onPress={handleShare}>
              <Ionicons name="share-social-outline" size={24} color="#fff" />
            </TouchableOpacity>
          </View>
        )}
      </View>

      {!isSearchActive && (
        <ScrollView
          style={styles.content}
          contentContainerStyle={styles.contentContainer}
          showsVerticalScrollIndicator={false}
        >
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            style={styles.categoriesScroll}
          >
            {displayCategories.map((category) => (
              <TouchableOpacity
                key={category}
                style={styles.categoryButton}
                onPress={() => setSelectedCategory(category || "")}
              >
                <Text
                  style={
                    selectedCategory === category
                      ? styles.categoryButtonTextActive
                      : styles.categoryButtonText
                  }
                >
                  {category}
                </Text>
              </TouchableOpacity>
            ))}
          </ScrollView>

          <View style={styles.liveStreamsGrid}>
            {/* Live Streams Section */}
            <View>
              {isLiveStreamsLoading
                ? skeletonArray.map((_, index) => (
                    <LiveStreamCardSkeleton key={`live-skeleton-${index}`} />
                  ))
                : filteredLiveStreams.length > 0
                  ? filteredLiveStreams.map((stream: any) => (
                      <LiveStreamCard
                        key={stream._id}
                        streamId={stream._id}
                        isBookmarked={stream.isBookmarked || false}
                        username={stream.streamer?.username || ""}
                        title={stream.title}
                        category={stream.category || ""}
                        subcategory={stream.subcategory || ""}
                        viewerCount={
                          typeof stream.viewerCount === "number"
                            ? stream.viewerCount
                            : 0
                        }
                        thumbnailUrl={stream.thumbnail}
                        profileImageUrl={getImageUrl(stream.streamer?.image || "")}
                        userColor={stream.userColor || "#2C2C2E"}
                        onPress={() => router.push(`/screens/stream/${stream._id}`)}
                      />
                    ))
                  : null}
            </View>

            {/* Scheduled Streams Section */}
            {isScheduledStreamsLoading ? (
              <View>
                {skeletonArray.map((_, index) => (
                  <LiveStreamCardSkeleton key={`scheduled-skeleton-${index}`} />
                ))}
              </View>
            ) : filteredScheduledStreams.length > 0 ? (
              <View>
                {filteredScheduledStreams.map((stream: any) => (
                  <LiveStreamCard
                    key={stream._id}
                    streamId={stream._id}
                    isBookmarked={stream.isBookmarked || false}
                    username={stream.username || ""}
                    title={stream.title}
                    category={stream.category || ""}
                    subcategory={stream.subcategory || ""}
                    viewerCount={
                      typeof stream.viewerCount === "number"
                        ? stream.viewerCount
                        : 0
                    }
                    thumbnailUrl={stream.thumbnail || ""}
                    profileImageUrl={stream.userAvatar || ""}
                    userColor={stream.userColor || "#2C2C2E"}
                    onPress={() => router.push(`/screens/stream/${stream._id}`)}
                  />
                ))}
              </View>
            ) : null}

            {/* Fallback if no streams and not loading */}
            {!isLiveStreamsLoading &&
              !isScheduledStreamsLoading &&
              filteredLiveStreams.length === 0 &&
              filteredScheduledStreams.length === 0 && (
                <View style={styles.noStreamsContainer}>
                  <Text style={styles.noStreamsText}>No current streams</Text>
                </View>
              )}
          </View>
        </ScrollView>
      )}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#1C1C1E",
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 16,
    paddingVertical: 8,
    zIndex: 1,
  },
  searchContainer: {
    flex: 1,
    height: 40,
  },
  headerIcons: {
    flexDirection: "row",
    marginLeft: 8,
  },
  userSearch: {
    flex: 1,
  },
  iconButton: {
    marginLeft: 16,
    padding: 4,
  },
  content: {
    flex: 1,
  },
  contentContainer: {
    paddingBottom: 16,
  },
  categoriesScroll: {
    paddingHorizontal: 16,
    marginVertical: 8,
  },
  categoryButton: {
    marginRight: 16,
    paddingVertical: 8,
  },
  categoryButtonText: {
    color: "#8E8E93",
    fontSize: 16,
    fontWeight: "600",
  },
  categoryButtonTextActive: {
    color: "#fff",
    fontSize: 16,
    fontWeight: "600",
    borderBottomWidth: 2,
    borderBottomColor: "#fff",
    paddingBottom: 8,
  },
  liveStreamsGrid: {
    padding: 16,
  },
  noStreamsContainer: {
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 32,
  },
  noStreamsText: {
    color: "#8E8E93",
    fontSize: 16,
    fontWeight: "500",
  },
});
