import React, { useEffect, useState, useCallback, useRef } from "react";
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
  ImageBackground,
  SafeAreaView,
  Animated,
  PanResponder,
  TextInput,
  KeyboardAvoidingView,
  Platform,
  Dimensions,
  StatusBar,
  FlatList
} from "react-native";
import { Stack, useLocalSearchParams, useRouter } from "expo-router";
import { Ionicons } from "@expo/vector-icons";
import { useUser } from "../../../hooks/use-user";
import { useQuery, useMutation, useAction } from "convex/react";
import { api } from "../../../convex/_generated/api";
import { Id } from "../../../convex/_generated/dataModel";
import StreamSkeleton from "../../../components/skeletons/stream-skeleton";
import { getImageUrl } from "../../../lib/utils";
import {
  LiveKitRoom,
  useTracks,
  VideoTrack,
  AudioSession,
  isTrackReference,
} from "@livekit/react-native";
import { Track } from "livekit-client";

const LIVEKIT_WS_URL = process.env.EXPO_PUBLIC_LIVEKIT_URL;
const { height } = Dimensions.get("window");

// Heart animation component
const AnimatedHeart = ({ id, onCompleteAnimation }: { id: string; onCompleteAnimation: (id: string) => void }) => {
  const position = useRef(new Animated.Value(0)).current;
  const opacity = useRef(new Animated.Value(1)).current;
  const scale = useRef(new Animated.Value(1)).current;

  useEffect(() => {
    Animated.parallel([
      Animated.timing(position, {
        toValue: -150,
        duration: 2000,
        useNativeDriver: true,
      }),
      Animated.timing(opacity, {
        toValue: 0,
        duration: 2000,
        useNativeDriver: true,
      }),
      Animated.sequence([
        Animated.timing(scale, {
          toValue: 1.2,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(scale, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }),
      ]),
    ]).start(() => {
      onCompleteAnimation(id);
    });
  }, []);

  return (
    <Animated.View
      style={[
        styles.heartIcon,
        {
          transform: [
            { translateY: position },
            { scale: scale },
          ],
          opacity: opacity,
        },
      ]}
    >
      <Image
        source={require("@icon.png")}
        style={{ width: 25, height: 25, tintColor: "#FF3B30" }}
      />
    </Animated.View>
  );
};

const getUniqueID = () => Math.random().toString(36).substring(2, 9);

const StreamScreen = () => {
  const params = useLocalSearchParams<{ streamId: string }>();
  const streamId = params.streamId;
  const { user } = useUser();
  const router = useRouter();
  const [message, setMessage] = useState("");

  // Heart animation state
  const [hearts, setHearts] = useState<{ id: string }[]>([]);
  const countAnimatedValue = useRef(new Animated.Value(0)).current;
  const timeout = useRef<NodeJS.Timeout | null>(null);
  let interval;

  const chatAreaRef = React.useRef<View>(null);
  const [uiVisible, setUiVisible] = useState(true);
  const fadeAnim = React.useRef(new Animated.Value(1)).current;
  const overlayOpacity = React.useRef(new Animated.Value(0.5)).current;
  const [showSwipeIndicator, setShowSwipeIndicator] = useState(false);
  const [isTouchingChatArea, setIsTouchingChatArea] = useState(false);
  const [starting, setStarting] = useState(false);


  const [livekitToken, setLivekitToken] = useState<string | null>(null);
  const [isHost, setIsHost] = useState(false);
  const [connecting, setConnecting] = useState(false);
  const [livekitError, setLivekitError] = useState<string | null>(null);
  const [ingestInfo, setIngestInfo] = useState<{ ingestUrl: string; streamKey: string } | null>(null);

  const toggleBookmark = useMutation(api.bookmarks.toggleBookmark);
  const [optimisticBookmarked, setOptimisticBookmarked] = useState<boolean | null>(null);
  const startStream = useMutation(api.streams.goLive);
  const sendMessage = useMutation(api.streams.sendMessage);
  const generateHostToken = useAction(api.integration.livekit.generateHostToken);
  const generateViewerToken = useAction(api.integration.livekit.generateViewerToken);
  const generateIngestInfo = useAction(api.integration.livekit.generateIngestInfo);

  // Heart animation callback
  const handleCompleteAnimation = useCallback((id: string) => {
    setHearts((oldHearts) => {
      return oldHearts.filter((heart) => heart.id !== id);
    });
  }, []);

  const stream = useQuery(
    api.streams.getStream,
    streamId ? { streamId: streamId as Id<"streams"> } : "skip",
  );

  const chatMessages = useQuery(
    api.streams.listMessages,
    streamId ? { streamId: streamId as Id<"streams"> } : "skip",
  );

  // Determine if user is host
  useEffect(() => {
    if (user && stream) {
      setIsHost(stream.hostId === user._id);
    }
  }, [user, stream]);

  // Fetch LiveKit token
  useEffect(() => {
    if (!streamId || !user || !stream) return;
    setLivekitToken(null);
    setLivekitError(null);
    setConnecting(true);
    if (stream.status === "live" || (isHost && stream.status !== "ended")) {
      const fn = isHost ? generateHostToken : generateViewerToken;
      fn({ streamId: streamId as Id<"streams"> })
        .then((token) => {
          setLivekitToken(token);
          setConnecting(false);
        })
        .catch((err) => {
          setLivekitError("Failed to get LiveKit token: " + (err?.message || err));
          setConnecting(false);
        });
    } else {
      setConnecting(false);
    }
  }, [streamId, user, stream, isHost]);

  // Fetch ingest info for OBS if host
  useEffect(() => {
    if (isHost && stream && !ingestInfo && stream.status !== "live") {
      generateIngestInfo({ streamId: streamId as Id<"streams"> })
        .then(setIngestInfo)
        .catch(() => {});
    }
  }, [isHost, stream, ingestInfo, streamId, generateIngestInfo]);

  useEffect(() => {
    if (!livekitToken) return;
    AudioSession.startAudioSession();
    return () => {
      AudioSession.stopAudioSession();
    };
  }, [livekitToken]);

  // PanResponder and UI logic (unchanged)
  const panResponder = React.useRef(
    PanResponder.create({
      onStartShouldSetPanResponder: () => {
        if (chatAreaRef.current) {
          return false;
        }
        return true;
      },
      onMoveShouldSetPanResponder: (_, gestureState) => {
        if (
          isTouchingChatArea &&
          Math.abs(gestureState.dy) > Math.abs(gestureState.dx)
        ) {
          return false;
        }
        return (
          Math.abs(gestureState.dx) > 20 &&
          Math.abs(gestureState.dx) > Math.abs(gestureState.dy)
        );
      },
      onPanResponderTerminationRequest: () => true,
      onPanResponderGrant: () => {},
      onPanResponderMove: () => {},
      onPanResponderRelease: (_, gestureState) => {
        if (Math.abs(gestureState.dx) < 5 && Math.abs(gestureState.dy) < 5) {
          if (!uiVisible) {
            setUiVisible(true);
            setShowSwipeIndicator(false);
            Animated.parallel([
              Animated.timing(fadeAnim, {
                toValue: 1,
                duration: 200,
                useNativeDriver: true,
              }),
              Animated.timing(overlayOpacity, {
                toValue: 0.5,
                duration: 200,
                useNativeDriver: false,
              }),
            ]).start();
          }
          return true;
        } else if (gestureState.dx < -40 && uiVisible) {
          setUiVisible(false);
          setShowSwipeIndicator(true);
          Animated.parallel([
            Animated.timing(fadeAnim, {
              toValue: 0,
              duration: 200,
              useNativeDriver: true,
            }),
            Animated.timing(overlayOpacity, {
              toValue: 0.2,
              duration: 200,
              useNativeDriver: false,
            }),
          ]).start();
        } else if (gestureState.dx > 40) {
          setUiVisible(true);
          setShowSwipeIndicator(false);
          Animated.parallel([
            Animated.timing(fadeAnim, {
              toValue: 1,
              duration: 200,
              useNativeDriver: true,
            }),
            Animated.timing(overlayOpacity, {
              toValue: 0.5,
              duration: 200,
              useNativeDriver: false,
            }),
          ]).start();
        }
        return true;
      },
      onPanResponderTerminate: () => true,
    }),
  ).current;

  // Chat and other UI logic (unchanged)
  const handleToggleBookmark = async () => {
    if (!streamId) return;
    setOptimisticBookmarked((prev) => !(prev ?? false));
    try {
      const result = await toggleBookmark({
        streamId: streamId as Id<"streams">,
      });
      setOptimisticBookmarked(result.bookmarked);
    } catch (error) {
      console.error("Error toggling bookmark:", error);
    }
  };

  const handleSendMessage = async () => {
    if (!message.trim() || !streamId || !user) return;
    try {
      await sendMessage({
        streamId: streamId as Id<"streams">,
        text: message.trim(),
      });
      setMessage("");
    } catch (error) {
      console.error("Error sending message:", error);
    }
  };

  // Video rendering using LiveKitRoom and VideoTrack
  const VideoArea = useCallback(() => {
    if (!livekitToken || !LIVEKIT_WS_URL || !stream?.roomName) {
      return (
        <View style={{ width: '100%', height: 300, backgroundColor: '#000', alignItems: 'center', justifyContent: 'center' }}>
          <Text style={{ color: '#fff' }}>Waiting for stream...</Text>
        </View>
      );
    }
    return (
      <LiveKitRoom
        serverUrl={LIVEKIT_WS_URL}
        token={livekitToken}
        connect={true}
        audio={true}
        video={true}
        options={{ adaptiveStream: { pixelDensity: 'screen' } }}
      >
        <RoomView />
      </LiveKitRoom>
    );
  }, [livekitToken, LIVEKIT_WS_URL, stream?.roomName, isHost, user?._id]);


  // RoomView for rendering video tracks
  const RoomView = () => {
    const tracks = useTracks([Track.Source.Camera]);
    // Host: show own camera; Viewer: show host's camera
    return (
      <View style={{ flex: 1 }}>
        <FlatList
          data={tracks}
          renderItem={({ item }) =>
            isTrackReference(item) ? (
              <VideoTrack trackRef={item} style={{ height: 300 }} />
            ) : (
              <View style={{ height: 300, backgroundColor: '#000' }} />
            )
          }
          keyExtractor={(item) => (isTrackReference(item) ? item.sid : String(item))}
        />
      </View>
    );
  };

  if (!streamId) {
    return (
      <SafeAreaView style={styles.safeArea}>
        <View style={styles.container}>
          <Text style={{ color: "#fff", textAlign: "center", marginTop: 40 }}>
            No stream ID provided.
          </Text>
        </View>
      </SafeAreaView>
    );
  }
  if (stream === undefined) {
    return <StreamSkeleton />;
  }
  if (!stream) {
    return (
      <SafeAreaView style={styles.safeArea}>
        <View style={styles.container}>
          <Text style={{ color: "#fff", textAlign: "center", marginTop: 40 }}>
            Stream not found.
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  const logoSource = stream.thumbnail
    ? { uri: stream.thumbnail }
    : require("@icon.png");

  const isModerator = (userId: Id<"users">) => {
    return stream?.moderatorIds?.includes(userId) || false;
  };

  const sortedMessages = chatMessages
    ? [...chatMessages].sort((a, b) => b._creationTime - a._creationTime)
    : [];

  const filteredMessages: any[] = [];
  const seenJoinedUsers = new Set();
  for (const msg of sortedMessages) {
    if (msg.type === "system" && msg.user?._id) {
      if (seenJoinedUsers.has(msg.user._id)) {
        continue;
      }
      seenJoinedUsers.add(msg.user._id);
    }
    filteredMessages.push(msg);
  }

  // Comment list component
  const commentAndList = () => {
    return (
      <View
        style={{
          flexDirection: "row",
          height: height / 2.9,
        }}
      >
        <View style={{ flex: 1 }}>
          <FlatList
            inverted={true}
            data={filteredMessages}
            renderItem={renderItemComment}
            keyExtractor={(item) => item._id}
            showsVerticalScrollIndicator={false}
          />
        </View>

        <View style={{ justifyContent: "flex-end", bottom: -30 }}>
          {hearts.map(({ id }) => (
            <AnimatedHeart
              key={id}
              id={id}
              onCompleteAnimation={handleCompleteAnimation}
            />
          ))}
        </View>
      </View>
    );
  };

  // Comment input and like button
  const commentAndLike = () => {
    return (
      <View
        style={{
          flexDirection: "row",
          ...styles.commentAndLikeViewStyle,
        }}
      >
        <View
          style={{
            flexDirection: "row",
            marginRight: 12,
            ...styles.commentViewStyle,
          }}
        >
          <TextInput
            value={message}
            onChangeText={setMessage}
            placeholder="Say something..."
            placeholderTextColor="#999"
            selectionColor="#007AFF"
            style={{
              textAlign: "left",
              marginRight: 12,
              ...styles.textInputStyle,
            }}
          />
          <TouchableOpacity
            disabled={!message}
            onPress={handleSendMessage}
          >
            <Ionicons
              name="send"
              size={20}
              color="#fff"
            />
          </TouchableOpacity>
        </View>

        <TouchableOpacity
          activeOpacity={0.9}
          style={styles.hearTouchableStyle}
          onPress={() => {
            setHearts((oldHearts) => [...oldHearts, { id: getUniqueID() }]);
          }}
        >
          <Image
            source={require("@icon.png")}
            style={{ width: 20, height: 20, resizeMode: "contain", tintColor: "#FF3B30" }}
          />
        </TouchableOpacity>
      </View>
    );
  };

  // Render comment item
  const renderItemComment = ({ item }: { item: any }) => {
    return (
      <View
        style={{
          flexDirection: "row",
          alignItems: "center",
          marginBottom: 16,
          marginHorizontal: 22,
        }}
      >
        <View style={styles.userBorderStyle}>
          <Image
            source={
              item.user?.image
                ? { uri: getImageUrl(item.user.image) }
                : require("@icon.png")
            }
            style={styles.imageStyle}
          />
        </View>
        <View
          style={{
            flex: 1,
            alignItems: "flex-start",
            marginHorizontal: 12,
          }}
        >
          <Text numberOfLines={1} style={styles.commentUsername}>
            {item.user?.username || "Anonymous"}
          </Text>
          <Text
            numberOfLines={2}
            style={{
              textAlign: "left",
              ...styles.commentText,
            }}
          >
            {item.text}
          </Text>
        </View>
      </View>
    );
  };


  // Status bar component
  const statusBar = () => {
    return (
      <StatusBar
        translucent
        backgroundColor="transparent"
        barStyle="light-content"
      />
    );
  };

  // Header with streamer info and close button
  const detailAndClose = () => {
    return (
      <View>
        <SafeAreaView />
        <View
          style={{
            flexDirection: "row",
            alignItems: "center",
            marginTop: (StatusBar.currentHeight || 0) + 16,
            marginHorizontal: 16,
          }}
        >
          <View
            style={{
              flex: 1,
              flexDirection: "row",
              alignItems: "center",
            }}
          >
            <View style={styles.userBorderStyle}>
              <Image
                source={
                  stream?.streamer?.image
                    ? { uri: getImageUrl(stream.streamer.image) }
                    : require("@icon.png")
                }
                style={styles.imageStyle}
              />
            </View>
            <View
              style={{
                flex: 1,
                alignItems: "flex-start",
                marginHorizontal: 12,
              }}
            >
              <Text numberOfLines={1} style={styles.streamerName}>
                {stream?.streamer?.username || stream?.streamer?.name || "User"}
              </Text>
              <Text numberOfLines={1} style={styles.liveText}>
                {stream?.status === "live" ? "LIVE" : "Scheduled"}
              </Text>
            </View>
          </View>
          <TouchableOpacity onPress={() => router.back()}>
            <Ionicons name="close-sharp" size={24} color="#fff" />
          </TouchableOpacity>
        </View>
      </View>
    );
  };

  return (
    <>
      <Stack.Screen
        options={{
          headerShown: false,
        }}
      />
      <View style={{ flex: 1 }}>
        {statusBar()}
        <KeyboardAvoidingView
          behavior={Platform.OS === "ios" ? "height" : undefined}
          style={{ flex: 1 }}
        >
          <ImageBackground
            style={{ flex: 1, justifyContent: "space-between" }}
            source={
              stream?.status === "live"
                ? { uri: "https://via.placeholder.com/400x800/000000/FFFFFF?text=LIVE+STREAM" }
                : logoSource
            }
          >
            {detailAndClose()}
            <View>
              {commentAndList()}
              {commentAndLike()}
            </View>
          </ImageBackground>
        </KeyboardAvoidingView>
      </View>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#1C1C1E",
    position: "relative",
  },
  safeArea: {
    flex: 1,
    position: "relative",
  },
  gestureContainer: {
    flex: 1,
    position: "relative",
    zIndex: 2,
  },
  backgroundImage: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    width: "100%",
    height: "100%",
    zIndex: 0,
  },
  overlayGradient: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: "rgba(28, 28, 30, 0.5)",
    zIndex: 1,
    pointerEvents: "none", // Ensure the overlay doesn't block touches
  },
  contentContainer: {
    flex: 1,
    backgroundColor: "transparent",
    paddingHorizontal: 0,
    justifyContent: "flex-start",
    zIndex: 2,
  },
  scheduledBanner: {
    backgroundColor: "rgba(34, 34, 34, 0.8)",
    borderRadius: 20,
    margin: 16,
    padding: 20,
    alignItems: "center",
    borderWidth: 1,
    borderColor: "#3A3A3C",
  },
  bannerSubtitle: {
    color: "#8E8E93",
    fontSize: 15,
    marginBottom: 8,
    textAlign: "center",
  },
  bannerTitle: {
    color: "#fff",
    fontSize: 28,
    fontWeight: "700",
    marginBottom: 16,
    textAlign: "center",
  },
  saveYellowButton: {
    backgroundColor: "#1a96da",
    borderRadius: 30,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    paddingHorizontal: 24,
    paddingVertical: 12,
    width: "100%",
    marginBottom: 8,
  },
  saveYellowButtonText: {
    color: "#fff",
    fontWeight: "700",
    fontSize: 16,
    marginLeft: 8,
  },
  shareButtonWhite: {
    backgroundColor: "#FFFFFF",
    borderRadius: 30,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    paddingHorizontal: 24,
    paddingVertical: 12,
    width: "100%",
  },
  shareButtonWhiteText: {
    color: "#000",
    fontWeight: "700",
    fontSize: 16,
    marginLeft: 8,
  },
  pollContainer: {
    backgroundColor: "rgba(40, 40, 40, 0.9)",
    borderRadius: 16,
    marginHorizontal: 16,
    marginBottom: 16,
    padding: 16,
  },
  pollHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 12,
  },
  pollQuestion: {
    color: "#fff",
    fontSize: 18,
    fontWeight: "600",
  },
  pollOption: {
    backgroundColor: "rgba(60, 60, 60, 0.8)",
    borderRadius: 25,
    padding: 16,
    marginBottom: 8,
    borderWidth: 1,
    borderColor: "#5A5A5C",
  },
  pollOptionText: {
    color: "#fff",
    fontSize: 16,
    textAlign: "center",
  },
  chatMainContainer: {
    backgroundColor: "transparent",
    marginBottom: 8,
    height: Dimensions.get("window").height * 0.33,
    position: "absolute",
    left: 0,
    right: 0,
    bottom: 0,
  },
  chatScrollView: {
    marginBottom: 8,
  },
  chatMessagesContent: {
    paddingHorizontal: 16,
    paddingBottom: 8,
    paddingTop: 8,
  },
  hostMessageContainer: {
    flexDirection: "row",
    alignItems: "flex-start",
    marginBottom: 12,
  },
  hostAvatarContainer: {
    marginRight: 8,
  },
  hostMessageContent: {
    flex: 1,
  },
  hostNameRow: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 4,
  },
  messageHeaderRow: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 4,
  },
  hostUsername: {
    color: "#fff",
    fontSize: 15,
    fontWeight: "600",
    marginRight: 8,
  },
  hostBadge: {
    backgroundColor: "#E93560",
    borderRadius: 4,
    paddingHorizontal: 6,
    paddingVertical: 2,
    marginLeft: 8,
  },
  hostBadgeText: {
    color: "#fff",
    fontSize: 12,
    fontWeight: "600",
  },
  modBadge: {
    backgroundColor: "#4A90E2",
    borderRadius: 4,
    paddingHorizontal: 6,
    paddingVertical: 2,
    marginLeft: 8,
  },
  modBadgeText: {
    color: "#fff",
    fontSize: 12,
    fontWeight: "600",
  },
  hostMessageText: {
    color: "#fff",
    fontSize: 15,
  },
  joinedMessageContainer: {
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: 4,
  },
  joinedMessageContent: {
    marginLeft: 8,
    flexDirection: "row",
    alignItems: "center",
  },
  joinedUsername: {
    color: "#fff",
    fontSize: 15,
    fontWeight: "600",
    marginRight: 4,
  },
  joinedText: {
    color: "#fff",
    fontSize: 15,
  },
  chatMessageRow: {
    flexDirection: "row",
    alignItems: "flex-start",
    marginBottom: 12,
  },
  chatMessageContent: {
    flex: 1,
    marginLeft: 8,
  },
  chatMessageUsername: {
    color: "#fff",
    fontSize: 15,
    fontWeight: "600",
  },
  chatMessageText: {
    color: "#fff",
    fontSize: 15,
  },
  reactionText: {
    fontSize: 20,
  },
  productInfoContainer: {
    backgroundColor: "rgba(0, 0, 0, 0.7)",
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: "rgba(255, 255, 255, 0.1)",
  },
  productTitle: {
    color: "#fff",
    fontSize: 16,
    fontWeight: "700",
  },
  productPrice: {
    color: "#fff",
    fontSize: 20,
    fontWeight: "700",
    marginTop: 4,
  },
  chatInputContainer: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderTopWidth: 0.5,
    borderTopColor: "rgba(100, 100, 100, 0.3)",
    // backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  chatInput: {
    flex: 1,
    backgroundColor: "rgba(34, 34, 34, 0.8)",
    borderRadius: 20,
    color: "#fff",
    fontSize: 16,
    paddingHorizontal: 16,
    paddingVertical: 10,
    marginRight: 8,
    borderWidth: 1,
    borderColor: "rgba(100, 100, 100, 0.3)",
  },
  sendButton: {
    backgroundColor: "#007AFF",
    borderRadius: 20,
    paddingHorizontal: 20,
    paddingVertical: 10,
    alignItems: "center",
    justifyContent: "center",
  },
  sendButtonDisabled: {
    backgroundColor: "#333",
  },
  sendButtonText: {
    color: "#fff",
    fontSize: 16,
    fontWeight: "500",
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: 16,
    paddingTop: 24,
    paddingBottom: 8,
  },
  streamName: {
    color: "#fff",
    fontSize: 18,
    fontWeight: "700",
  },
  liveIndicator: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#222",
    borderRadius: 12,
    paddingHorizontal: 10,
    paddingVertical: 4,
  },
  liveText: {
    color: "#fff",
    fontSize: 16,
    marginLeft: 6,
  },
  shareButton: {
    backgroundColor: "#1a96d2",
    borderRadius: 30,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    paddingHorizontal: 24,
    paddingVertical: 10,
    width: "100%",
    marginTop: 8,
  },
  shareButtonText: {
    color: "#fff",
    fontWeight: "700",
    fontSize: 16,
    marginLeft: 8,
  },
  explicitBanner: {
    backgroundColor: "#D7263D",
    paddingVertical: 12,
    paddingHorizontal: 16,
    alignItems: "center",
    justifyContent: "center",
    borderRadius: 12,
    zIndex: 101,
    minWidth: 260,
    maxWidth: "80%",
    alignSelf: "center",
  },
  explicitBannerContainer: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: "center",
    alignItems: "center",
    zIndex: 101,
    pointerEvents: "box-none",
  },
  explicitBannerText: {
    color: "#fff",
    fontWeight: "bold",
    fontSize: 16,
    textAlign: "center",
  },
  topSection: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: 16,
    backgroundColor: "transparent",
    zIndex: 10,
  },
  topLeftRow: {
    flexDirection: "row",
    alignItems: "center",
    flex: 1,
    minWidth: 0,
  },
  backButton: {
    marginRight: 8,
    padding: 4,
  },
  userAvatarContainer: {
    marginRight: 8,
  },
  topUsername: {
    color: "#fff",
    fontWeight: "700",
    fontSize: 18,
    maxWidth: 180,
  },
  topRightRow: {
    flexDirection: "row",
    alignItems: "center",
  },
  chatUserRow: {
    flexDirection: "row",
    alignItems: "center",
    gap: 10,
  },
  swipeIndicator: {
    position: "absolute",
    left: 20,
    top: "50%",
    backgroundColor: "rgba(0,0,0,0.5)",
    paddingVertical: 10,
    paddingHorizontal: 15,
    borderRadius: 20,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
  },
  swipeText: {
    color: "#fff",
    marginLeft: 5,
    fontSize: 14,
  },
  startStreamButton: {
    backgroundColor: "#1a96da",
    borderRadius: 30,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    paddingHorizontal: 32,
    paddingVertical: 16,
    width: "90%",
    alignSelf: "center",
    marginTop: 8,
    marginBottom: 8,
  },
  startStreamButtonText: {
    color: "#fff",
    fontWeight: "700",
    fontSize: 18,
    marginLeft: 8,
  },
  // LiveScreen styles
  userBorderStyle: {
    justifyContent: "center",
    alignItems: "center",
    width: 50,
    height: 50,
    borderWidth: 1,
    borderColor: "#fff",
    borderRadius: 10,
  },
  imageStyle: {
    width: 42,
    height: 42,
    borderRadius: 8,
  },
  streamerName: {
    color: "#fff",
    fontSize: 16,
    fontWeight: "600",
  },
  commentViewStyle: {
    flex: 1,
    alignItems: "center",
    paddingVertical: 12,
    paddingHorizontal: 14,
    backgroundColor: "rgba(34, 34, 34, 0.8)",
    borderRadius: 10,
    borderWidth: 1,
    borderColor: "rgba(148, 148, 148, 0.5)",
  },
  commentAndLikeViewStyle: {
    alignItems: "center",
    marginBottom: 16,
    marginTop: 8,
    marginHorizontal: 16,
  },
  hearTouchableStyle: {
    justifyContent: "center",
    alignItems: "center",
    width: 45,
    height: 45,
    borderRadius: 22.5,
    borderWidth: 1,
    borderColor: "rgba(148, 148, 148, 0.5)",
    backgroundColor: "rgba(34, 34, 34, 0.8)",
  },
  textInputStyle: {
    flex: 1,
    color: "#fff",
    fontSize: 14,
  },
  heartIcon: {
    position: "absolute",
    resizeMode: "contain",
    width: 25,
    height: 25,
  },
  commentUsername: {
    color: "#fff",
    fontSize: 16,
    fontWeight: "600",
  },
  commentText: {
    color: "#ccc",
    fontSize: 14,
  },
});

export default StreamScreen;
