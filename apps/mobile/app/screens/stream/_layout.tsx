import { Stack } from "expo-router";

const StreamLayout = () => {
  return (
    <Stack
      screenOptions={{
        headerShown: false,
        animation: "slide_from_right",
        animationDuration: 200,
      }}
    >
      <Stack.Screen name="[streamId]" options={{ headerShown: false }} />
      <Stack.Screen name="new-listing" options={{ headerShown: false }} />
      <Stack.Screen name="schedule" options={{ headerShown: false }} />
    </Stack>
  );
};

export default StreamLayout;
