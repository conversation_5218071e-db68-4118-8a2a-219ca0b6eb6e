import { View, Text, StyleSheet, TouchableOpacity, Alert } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { useRouter } from "expo-router";
import { useState } from "react";
import { Ionicons } from "@expo/vector-icons";
import { useMutation } from "convex/react";
import { api } from "../../../../convex/_generated/api";
import { useUser } from "../../../../hooks/use-user";

export default function DeleteAccountScreen() {
  const router = useRouter();
  const [confirmationStep, setConfirmationStep] = useState(1);
  const { user } = useUser();
  const deleteAccount = useMutation(api.users.deleteAccount);

  const handleDeleteAccount = async () => {
    try {
      if (!user) {
        throw new Error("User not found");
      }

      await deleteAccount({ id: user._id });

      Alert.alert(
        "Account Deleted",
        "Your account has been successfully deleted. We're sorry to see you go.",
        [
          {
            text: "OK",
            onPress: () => {
              router.replace("./(unauthenticated)/login");
            },
          },
        ],
      );
    } catch (error) {
      console.error("Failed to delete account:", error);
      Alert.alert(
        "Error",
        "Failed to delete your account. Please try again later or contact support.",
      );
    }
  };

  const renderStep1 = () => (
    <View style={styles.content}>
      <Ionicons name="warning" size={64} color="#FF3B30" />
      <Text style={styles.title}>Delete Account</Text>
      <Text style={styles.description}>
        Are you sure you want to delete your account? This action cannot be
        undone and you will lose:
      </Text>
      <View style={styles.bulletPoints}>
        <Text style={styles.bulletPoint}>• All your profile information</Text>
        <Text style={styles.bulletPoint}>• Your posts and comments</Text>
        <Text style={styles.bulletPoint}>• Your connections and messages</Text>
        <Text style={styles.bulletPoint}>• All other account data</Text>
      </View>
      <TouchableOpacity
        style={styles.deleteButton}
        onPress={() => setConfirmationStep(2)}
      >
        <Text style={styles.deleteButtonText}>I understand, continue</Text>
      </TouchableOpacity>
      <TouchableOpacity
        style={styles.cancelButton}
        onPress={() => router.back()}
      >
        <Text style={styles.cancelButtonText}>Cancel</Text>
      </TouchableOpacity>
    </View>
  );

  const renderStep2 = () => (
    <View style={styles.content}>
      <Ionicons name="alert-circle" size={64} color="#FF3B30" />
      <Text style={styles.title}>Final Confirmation</Text>
      <Text style={styles.description}>
        Please confirm that you want to permanently delete your account. This
        action cannot be reversed.
      </Text>
      <TouchableOpacity
        style={styles.finalDeleteButton}
        onPress={handleDeleteAccount}
      >
        <Text style={styles.deleteButtonText}>Yes, delete my account</Text>
      </TouchableOpacity>
      <TouchableOpacity
        style={styles.cancelButton}
        onPress={() => setConfirmationStep(1)}
      >
        <Text style={styles.cancelButtonText}>Go back</Text>
      </TouchableOpacity>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      {confirmationStep === 1 ? renderStep1() : renderStep2()}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#1C1C1E",
  },
  content: {
    flex: 1,
    alignItems: "center",
    padding: 24,
    paddingTop: 48,
  },
  title: {
    fontSize: 24,
    fontWeight: "bold",
    color: "#FF3B30",
    marginTop: 24,
    marginBottom: 16,
  },
  description: {
    fontSize: 16,
    color: "#fff",
    textAlign: "center",
    marginBottom: 24,
    opacity: 0.8,
  },
  bulletPoints: {
    alignSelf: "stretch",
    marginBottom: 32,
  },
  bulletPoint: {
    fontSize: 16,
    color: "#fff",
    marginBottom: 12,
    opacity: 0.8,
  },
  deleteButton: {
    backgroundColor: "rgba(255, 59, 48, 0.1)",
    paddingVertical: 16,
    paddingHorizontal: 32,
    borderRadius: 12,
    width: "100%",
    alignItems: "center",
    borderWidth: 1,
    borderColor: "#FF3B30",
  },
  finalDeleteButton: {
    backgroundColor: "#FF3B30",
    paddingVertical: 16,
    paddingHorizontal: 32,
    borderRadius: 12,
    width: "100%",
    alignItems: "center",
  },
  deleteButtonText: {
    color: "#fff",
    fontSize: 18,
    fontWeight: "600",
  },
  cancelButton: {
    marginTop: 16,
    paddingVertical: 16,
    paddingHorizontal: 32,
    borderRadius: 12,
    width: "100%",
    alignItems: "center",
  },
  cancelButtonText: {
    color: "#8E8E93",
    fontSize: 18,
    fontWeight: "600",
  },
});
