import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Sc<PERSON>View,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { useAuthActions } from "@convex-dev/auth/react";
import { Ionicons } from "@expo/vector-icons";
import Constants from "expo-constants";
import { useRouter } from "expo-router";
import { useUser } from "../../../../hooks/use-user";
import { Image as ExpoImage } from "expo-image";
import { LinearGradient } from "expo-linear-gradient";

interface MenuItemProps {
  icon: keyof typeof Ionicons.glyphMap;
  title: string;
  showExternalIcon?: boolean;
  onPress?: () => void;
}

const MenuItem = ({
  icon,
  title,
  showExternalIcon = false,
  onPress,
}: MenuItemProps) => (
  <TouchableOpacity style={styles.menuItem} onPress={onPress}>
    <View style={styles.menuItemLeft}>
      <View style={styles.iconContainer}>
        <Ionicons name={icon} size={24} color="#fff" />
      </View>
      <Text style={styles.menuItemTitle}>{title}</Text>
    </View>
    {showExternalIcon ? (
      <Ionicons name="open-outline" size={24} color="#8E8E93" />
    ) : (
      <Ionicons name="chevron-forward" size={24} color="#8E8E93" />
    )}
  </TouchableOpacity>
);

export default function AccountScreen() {
  const { signOut } = useAuthActions();
  const { user } = useUser();
  const router = useRouter();

  return (
    <SafeAreaView style={styles.container} edges={["top"]}>
      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
      >
        {/* Cover Image & Profile Section */}
        <View style={styles.coverContainer}>
          {user?.coverImageUrl ? (
            <ExpoImage
              source={{ uri: user.coverImageUrl }}
              style={styles.coverImage}
              contentFit="cover"
            />
          ) : (
            <LinearGradient
              colors={["#4A90E200", "#4A90E2"]}
              style={styles.coverImage}
            />
          )}
          <View style={styles.profileOverlay}>
            <View style={styles.profileContent}>
              {user?.avatarUrl ? (
                <ExpoImage
                  source={{ uri: user.avatarUrl }}
                  style={styles.avatarImage}
                  contentFit="cover"
                />
              ) : (
                <View style={styles.avatarContainer}>
                  <Text style={styles.avatarText}>
                    {user?.username?.charAt(0)}
                  </Text>
                </View>
              )}
              <View style={styles.profileInfo}>
                <View style={styles.usernameContainer}>
                  <Text style={styles.username}>{user?.username}</Text>
                  {user?.isVerified && (
                    <Ionicons
                      name="checkmark-circle"
                      size={24}
                      color="#6C5CE7"
                    />
                  )}
                </View>
                <TouchableOpacity
                  style={styles.viewProfileButton}
                  onPress={() => router.push(`/user/${user?.username}`)}
                >
                  <Text style={styles.viewProfileText}>View Profile</Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </View>

        {/* Preferences Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Account</Text>
          <MenuItem
            icon="settings"
            title="Preferences"
            onPress={() => router.push("/account/preferences")}
          />
          <MenuItem
            icon="notifications"
            title="Notifications"
            onPress={() => router.push("/account/notifications")}
          />
        </View>

        {/* Help & Legal Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Help & Legal</Text>
          <MenuItem
            icon="chatbubble-ellipses"
            title="Contact Us"
            onPress={async () => {
              try {
                const canOpen = await Linking.canOpenURL(
                  "mailto:<EMAIL>",
                );
                if (canOpen) {
                  await Linking.openURL("mailto:<EMAIL>");
                } else {
                  Alert.alert(
                    "Error",
                    "No email app is available on your device",
                  );
                }
              } catch (error) {
                Alert.alert("Error", "Could not open email client");
              }
            }}
          />
          <MenuItem
            icon="warning"
            title="User Reports"
            onPress={() => router.push("/account/reports")}
          />
          <MenuItem
            icon="shield"
            title="Legal"
            onPress={() => router.push("/account/legal")}
          />
          <MenuItem
            icon="document-text"
            title="Privacy Policy"
            showExternalIcon={true}
            onPress={() =>
              Linking.openURL("https://liveciety.com/legal/privacy-policy")
            }
          />
          <MenuItem
            icon="document-text"
            title="Terms of Service"
            showExternalIcon={true}
            onPress={() =>
              Linking.openURL("https://liveciety.com/legal/terms-of-service")
            }
          />
        </View>

        {/* Danger Zone Section */}
        <View style={[styles.section, styles.dangerSection]}>
          <Text style={[styles.sectionTitle, styles.dangerTitle]}>
            Danger Zone
          </Text>
          <MenuItem
            icon="alert-circle"
            title="Delete Account"
            onPress={() => router.push("/account/delete")}
          />
        </View>

        {/* Sign Out Button */}
        <TouchableOpacity
          style={styles.signOutButton}
          onPress={() => void signOut()}
        >
          <Ionicons name="log-out-outline" size={24} color="#fff" />
          <Text style={styles.signOutText}>Sign Out</Text>
        </TouchableOpacity>

        {/* Version Info */}
        <View style={styles.versionInfo}>
          <Text style={styles.versionText}>
            v{Constants.expoConfig?.version} (
            {Constants.expoConfig?.ios?.buildNumber})
          </Text>
          <Text style={styles.copyrightText}>
            © {new Date().getFullYear()} Liveciety
          </Text>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#1C1C1E",
  },
  scrollView: {
    flex: 1,
    height: "100%",
  },
  coverContainer: {
    height: 200,
    position: "relative",
  },
  coverImage: {
    width: "100%",
    height: "100%",
  },
  profileOverlay: {
    position: "absolute",
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: "rgba(0,0,0,0.5)",
    paddingTop: 60,
  },
  profileContent: {
    flexDirection: "row",
    alignItems: "flex-end",
    padding: 16,
  },
  avatarImage: {
    width: 80,
    height: 80,
    borderRadius: 40,
    borderWidth: 3,
    borderColor: "#fff",
  },
  avatarContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: "#6C5CE7",
    justifyContent: "center",
    alignItems: "center",
    borderWidth: 3,
    borderColor: "#fff",
  },
  avatarText: {
    color: "#fff",
    fontSize: 32,
    fontWeight: "bold",
  },
  profileInfo: {
    marginLeft: 16,
    flex: 1,
  },
  usernameContainer: {
    flexDirection: "row",
    alignItems: "center",
    gap: 8,
  },
  username: {
    color: "#fff",
    fontSize: 24,
    fontWeight: "bold",
    textShadowColor: "rgba(0,0,0,0.75)",
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 4,
  },
  viewProfileButton: {
    backgroundColor: "#1a96d2",
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    marginTop: 8,
    alignSelf: "flex-start",
  },
  viewProfileText: {
    color: "#fff",
    fontWeight: "600",
  },
  section: {
    marginTop: 32,
  },
  sectionTitle: {
    color: "#fff",
    fontSize: 20,
    fontWeight: "bold",
    marginHorizontal: 16,
    marginBottom: 8,
    opacity: 0.9,
  },
  menuItem: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingVertical: 16,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    borderBottomColor: "rgba(255,255,255,0.1)",
  },
  menuItemLeft: {
    flexDirection: "row",
    alignItems: "center",
    gap: 16,
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: "#2C2C2E",
    justifyContent: "center",
    alignItems: "center",
  },
  menuItemTitle: {
    color: "#fff",
    fontSize: 16,
    fontWeight: "500",
  },
  signOutButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: "#FF3B30",
    marginHorizontal: 16,
    marginTop: 32,
    marginBottom: 16,
    padding: 16,
    borderRadius: 12,
    gap: 8,
  },
  signOutText: {
    color: "#fff",
    fontSize: 18,
    fontWeight: "600",
  },
  versionInfo: {
    alignItems: "center",
    paddingTop: 8,
    paddingBottom: 16,
  },
  versionText: {
    color: "#8E8E93",
    fontSize: 14,
  },
  copyrightText: {
    color: "#8E8E93",
    fontSize: 14,
    marginTop: 4,
  },
  dangerSection: {
    marginTop: 32,
    // borderRadius: 12,
    // marginHorizontal: 16,
    // backgroundColor: 'rgba(255, 59, 48, 0.1)', // Red with opacity
    // padding: 8,
  },
  dangerTitle: {
    color: "#FF3B30", // Red color
    marginHorizontal: 8,
  },
});
