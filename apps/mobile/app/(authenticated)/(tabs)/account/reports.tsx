import React from "react";
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  ActivityIndicator,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { Ionicons } from "@expo/vector-icons";
import { useRouter } from "expo-router";
import { useQuery } from "convex/react";
import { api } from "../../../../convex/_generated/api";
import { useState } from "react";
import { formatDistanceToNow } from "date-fns";

export default function ReportsScreen() {
  const router = useRouter();
  const [filter, setFilter] = useState<"all" | "pending" | "closed">("all");
  const reports = useQuery(api.reports.getReports, { filter });

  const FilterButton = ({
    title,
    value,
  }: {
    title: string;
    value: typeof filter;
  }) => (
    <TouchableOpacity
      style={[
        styles.filterButton,
        filter === value && styles.filterButtonActive,
      ]}
      onPress={() => setFilter(value)}
    >
      <Text
        style={[
          styles.filterButtonText,
          filter === value && styles.filterButtonTextActive,
        ]}
      >
        {title}
      </Text>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={styles.container} edges={["top"]}>
      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
      >
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => router.back()}
          >
            <Ionicons name="chevron-back" size={28} color="#fff" />
          </TouchableOpacity>
          <Text style={styles.title}>My Reports</Text>
        </View>

        <View style={styles.filterContainer}>
          <FilterButton title="All" value="all" />
          <FilterButton title="Pending" value="pending" />
          <FilterButton title="Closed" value="closed" />
        </View>

        {reports === undefined ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color="#6C5CE7" />
          </View>
        ) : reports === null ? (
          <View style={styles.content}>
            <View style={styles.iconContainer}>
              <Ionicons name="warning" size={64} color="#6C5CE7" />
            </View>
            <Text style={styles.errorTitle}>Error Loading Reports</Text>
            <Text style={styles.description}>
              There was a problem loading your reports. Please try again later.
            </Text>
          </View>
        ) : reports.length === 0 ? (
          <View style={styles.content}>
            <View style={styles.iconContainer}>
              <Ionicons name="information-circle" size={64} color="#6C5CE7" />
            </View>
            <Text style={styles.emptyTitle}>No Reports Found</Text>
            <Text style={styles.description}>
              You haven't submitted any reports{" "}
              {filter !== "all" ? `that are ${filter}` : "yet"}.
            </Text>
          </View>
        ) : (
          <View style={styles.reportsContainer}>
            {reports.map((report) => (
              <View key={report._id} style={styles.reportCard}>
                <View style={styles.reportHeader}>
                  <Text style={styles.reportType}>
                    Non-compliant {report.contentType}
                  </Text>
                  <Text
                    style={[
                      styles.reportStatus,
                      report.status === "closed"
                        ? styles.statusClosed
                        : styles.statusPending,
                    ]}
                  >
                    {report.status === "closed" ? "Closed" : "Pending"}
                  </Text>
                </View>
                <Text style={styles.reportDescription}>
                  {report.reason}
                  {report.additionalDetails && (
                    <>
                      {"\n\n"}
                      <Text style={styles.additionalDetails}>
                        {report.additionalDetails}
                      </Text>
                    </>
                  )}
                </Text>
                <View style={styles.reportFooter}>
                  <Text style={styles.reportUsername}>
                    Reported User: {report.reportedUser?.username || "Unknown"}
                  </Text>
                  <Text style={styles.reportTime}>
                    {formatDistanceToNow(report.updatedAt, { addSuffix: true })}
                  </Text>
                </View>
              </View>
            ))}
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#1C1C1E",
  },
  scrollView: {
    flex: 1,
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 16,
    marginTop: 16,
    marginBottom: 24,
  },
  backButton: {
    marginRight: 8,
  },
  title: {
    fontSize: 34,
    fontWeight: "bold",
    color: "#fff",
  },
  filterContainer: {
    flexDirection: "row",
    paddingHorizontal: 16,
    marginBottom: 20,
  },
  filterButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: "#2C2C2E",
    marginRight: 8,
  },
  filterButtonActive: {
    backgroundColor: "#6C5CE7",
  },
  filterButtonText: {
    color: "#8E8E93",
    fontSize: 14,
    fontWeight: "600",
  },
  filterButtonTextActive: {
    color: "#fff",
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    marginTop: 100,
  },
  content: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
    padding: 24,
    marginTop: 60,
  },
  iconContainer: {
    width: 120,
    height: 120,
    borderRadius: 60,
    backgroundColor: "#2C2C2E",
    justifyContent: "center",
    alignItems: "center",
    marginBottom: 24,
  },
  errorTitle: {
    color: "#fff",
    fontSize: 24,
    fontWeight: "bold",
    marginBottom: 12,
    textAlign: "center",
  },
  emptyTitle: {
    color: "#fff",
    fontSize: 24,
    fontWeight: "bold",
    marginBottom: 12,
    textAlign: "center",
  },
  description: {
    color: "#8E8E93",
    fontSize: 16,
    textAlign: "center",
    maxWidth: "80%",
  },
  reportsContainer: {
    padding: 16,
  },
  reportCard: {
    backgroundColor: "#2C2C2E",
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
  },
  reportHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 12,
  },
  reportType: {
    color: "#fff",
    fontSize: 16,
    fontWeight: "600",
  },
  reportStatus: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
    overflow: "hidden",
    fontSize: 12,
    fontWeight: "600",
  },
  statusPending: {
    backgroundColor: "#FF9F0A",
    color: "#1C1C1E",
  },
  statusClosed: {
    backgroundColor: "#34C759",
    color: "#1C1C1E",
  },
  reportDescription: {
    color: "#8E8E93",
    fontSize: 14,
    marginBottom: 12,
  },
  additionalDetails: {
    color: "#8E8E93",
    fontSize: 12,
    fontStyle: "italic",
  },
  reportFooter: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  reportUsername: {
    color: "#8E8E93",
    fontSize: 12,
  },
  reportTime: {
    color: "#8E8E93",
    fontSize: 12,
  },
});
