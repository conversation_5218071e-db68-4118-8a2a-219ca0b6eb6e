import React, { useState } from "react";
import { Tabs, useRouter } from "expo-router";
import { Ionicons } from "@expo/vector-icons";
import { BottomSheetModalProvider } from "@gorhom/bottom-sheet";
import BecomeSellerSheet from "../../../components/ui/become-seller-sheet";
import StreamOptionsSheet from "../../../components/ui/stream-options-sheet";
import { useUser } from "../../../hooks/use-user";

export default function TabLayout() {
  const router = useRouter();
  const { user } = useUser();
  const [showSellerSheet, setShowSellerSheet] = useState(false);
  const [showStreamOptions, setShowStreamOptions] = useState(false);

  const handleSellPress = () => {
    if (user?.role !== "seller") {
      setShowSellerSheet(true);
      return;
    }

    setShowStreamOptions(true);
  };

  const handleGetStarted = () => {
    setShowSellerSheet(false);
    router.push("/(screens)/onboarding/seller" as any);
  };

  return (
    <BottomSheetModalProvider>
      <Tabs
        screenOptions={{
          tabBarStyle: {
            backgroundColor: "#111",
          },
          tabBarActiveTintColor: "#fff",
          tabBarInactiveTintColor: "#8E8E93",
          headerShown: false,
          tabBarHideOnKeyboard: true,
        }}
      >
        <Tabs.Screen
          name="index"
          options={{
            title: "Home",
            tabBarIcon: ({ color, size }) => (
              <Ionicons name="home" size={size} color={color} />
            ),
          }}
        />
        <Tabs.Screen
          name="categories"
          options={{
            title: "Categories",
            tabBarIcon: ({ color, size }) => (
              <Ionicons name="grid" size={size} color={color} />
            ),
          }}
        />
        <Tabs.Screen
          name="sell"
          options={{
            title: "Sell",
            tabBarIcon: ({ color, size }) => (
              <Ionicons name="add-circle" size={size} color={color} />
            ),
          }}
          listeners={{
            tabPress: (e) => {
              e.preventDefault();
              handleSellPress();
            },
          }}
        />
        <Tabs.Screen
          name="activity"
          options={{
            title: "Activity",
            tabBarIcon: ({ color, size }) => (
              <Ionicons name="notifications" size={size} color={color} />
            ),
          }}
        />
        <Tabs.Screen
          name="account"
          options={{
            title: "Account",
            tabBarIcon: ({ color, size }) => (
              <Ionicons name="person" size={size} color={color} />
            ),
          }}
        />
        <Tabs.Screen
          name="user"
          options={{
            tabBarButton: () => null,
            tabBarItemStyle: { display: "none" },
          }}
        />
      </Tabs>

      <BecomeSellerSheet
        isOpen={showSellerSheet}
        onClose={() => setShowSellerSheet(false)}
        onGetStarted={handleGetStarted}
      />

      <StreamOptionsSheet
        isOpen={showStreamOptions}
        onClose={() => setShowStreamOptions(false)}
      />
    </BottomSheetModalProvider>
  );
}
