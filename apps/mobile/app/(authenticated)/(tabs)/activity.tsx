import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { useState, useEffect } from "react";
import ChatList from "../../../components/ui/chat-list";
import BookmarkList from "../../../components/ui/bookmark-list";
import { useLocalSearchParams } from "expo-router";
import BidsEmptyState from "../../../components/ui/bids-empty-state";

type Tab =
  'purchases' |
  'bids' |
  'offers' |
  "messages" | "bookmarks";

export default function ActivityScreen() {
  const { tab } = useLocalSearchParams<{ tab?: Tab }>();
  const [activeTab, setActiveTab] = useState<Tab>("messages");

  useEffect(() => {
    if (
      tab &&
      [
        'purchases',
        'bids',
        'offers',
        "messages",
        "bookmarks",
      ].includes(tab)
    ) {
      setActiveTab(tab as Tab);
    }
  }, [tab]);

  const renderContent = () => {
    switch (activeTab) {
      case 'bids':
        return <BidsEmptyState />;
      case "messages":
        return <ChatList />;
      case "bookmarks":
        return <BookmarkList />;
      default:
        return (
          <View style={styles.emptyStateContainer}>
            <Text style={styles.emptyStateTitle}>
              There's nothing here{"\n"}at the moment!
            </Text>
            <Text style={styles.emptyStateSubtitle}>
              Get started by making your first purchase!
            </Text>
          </View>
        );
    }
  };

  return (
    <SafeAreaView style={styles.container} edges={["top"]}>
      <View style={styles.header}>
        <Text style={styles.title}>Activity</Text>
        {/* TODO: Add find friends button and create new page */}
        {/* <TouchableOpacity style={styles.findFriendsButton}>
          <Ionicons name="people" size={20} color="#fff" />
          <Text style={styles.findFriendsText}>Find Friends</Text>
        </TouchableOpacity> */}
      </View>

      <View style={styles.tabsContainer}>
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          {/* <TouchableOpacity 
            style={[styles.tab, activeTab === 'purchases' && styles.activeTab]}
            onPress={() => setActiveTab('purchases')}
          >
            <Text style={[styles.tabText, activeTab === 'purchases' && styles.activeTabText]}>Purchases</Text>
          </TouchableOpacity>
          <TouchableOpacity 
            style={[styles.tab, activeTab === 'bids' && styles.activeTab]}
            onPress={() => setActiveTab('bids')}
          >
            <Text style={[styles.tabText, activeTab === 'bids' && styles.activeTabText]}>Bids</Text>
          </TouchableOpacity>
          <TouchableOpacity 
            style={[styles.tab, activeTab === 'offers' && styles.activeTab]}
            onPress={() => setActiveTab('offers')}
          >
            <Text style={[styles.tabText, activeTab === 'offers' && styles.activeTabText]}>Offers</Text>
          </TouchableOpacity> */}
          <TouchableOpacity
            style={[styles.tab, activeTab === "messages" && styles.activeTab]}
            onPress={() => setActiveTab("messages")}
          >
            <Text
              style={[
                styles.tabText,
                activeTab === "messages" && styles.activeTabText,
              ]}
            >
              Messages
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.tab, activeTab === "bookmarks" && styles.activeTab]}
            onPress={() => setActiveTab("bookmarks")}
          >
            <Text
              style={[
                styles.tabText,
                activeTab === "bookmarks" && styles.activeTabText,
              ]}
            >
              Bookmarks
            </Text>
          </TouchableOpacity>
        </ScrollView>
      </View>

      <View style={styles.content}>{renderContent()}</View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#1C1C1E",
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  title: {
    fontSize: 34,
    fontWeight: "bold",
    color: "#fff",
  },
  findFriendsButton: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#2C2C2E",
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 20,
    gap: 6,
  },
  findFriendsText: {
    color: "#fff",
    fontSize: 15,
    fontWeight: "500",
  },
  tabsContainer: {
    borderBottomWidth: 1,
    borderBottomColor: "#2C2C2E",
  },
  tab: {
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  activeTab: {
    borderBottomWidth: 2,
    borderBottomColor: "#fff",
  },
  tabText: {
    color: "#8E8E93",
    fontSize: 16,
    fontWeight: "500",
  },
  activeTabText: {
    color: "#fff",
  },
  content: {
    flex: 1,
    justifyContent: "flex-start",
    alignItems: "center",
  },
  emptyStateContainer: {
    alignItems: "center",
    padding: 20,
  },
  mascotContainer: {
    width: 120,
    height: 120,
    justifyContent: "center",
    alignItems: "center",
    marginBottom: 20,
  },
  mascotEmoji: {
    fontSize: 80,
  },
  emptyStateTitle: {
    color: "#fff",
    fontSize: 18,
    fontWeight: "bold",
    textAlign: "center",
    marginBottom: 8,
  },
  emptyStateSubtitle: {
    color: "#8E8E93",
    fontSize: 12,
    textAlign: "center",
  },
});
