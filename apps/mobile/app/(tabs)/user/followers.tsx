import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  FlatList,

} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { Ionicons } from "@expo/vector-icons";
import { useRouter, useLocalSearchParams } from "expo-router";
import { useMutation } from "convex/react";
import { api } from "../../../convex/_generated/api";
import { Id } from "../../../convex/_generated/dataModel";
import { Image as ExpoImage } from "expo-image";
import { useUser } from "../../../hooks/use-user";
import { useUserFollowData } from "../../../hooks/use-user-follow-data";
import FollowListSkeleton from "../../../components/skeletons/follow-list-skeleton";
import { User, TabType } from "../../../types";

export default function FollowersScreen() {
  const router = useRouter();
  const { id, initialTab = "followers" } = useLocalSearchParams<{
    id: Id<"users">;
    initialTab?: TabType;
  }>();
  const { user: currentUser } = useUser();
  const followUser = useMutation(api.users.followUser);
  const unfollowUser = useMutation(api.users.unfollowUser);

  const {
    profileUser,
    followStats,
    followers,
    following,
    mutuals,
    activeTab,
    setActiveTab,
    activeList
  } = useUserFollowData(id, initialTab as TabType);

  const isOwnProfile = currentUser?._id === id;

  const handleBack = () => {
    router.back();
  };

  const handleFollowToggle = async (userId: Id<"users">) => {
    try {
      if (!currentUser) return;

      let userToUpdate;
      switch (activeTab) {
        case "followers":
          userToUpdate = followers?.find((u) => u?._id === userId);
          break;
        case "following":
          userToUpdate = following?.find((u) => u?._id === userId);
          break;
        case "mutuals":
          userToUpdate = mutuals?.find((u) => u?._id === userId);
          break;
      }

      if (!userToUpdate) {
        return;
      }

      const isCurrentlyFollowing = userToUpdate.following;

      try {
        if (isCurrentlyFollowing) {
          await unfollowUser({ targetUserId: userId });
        } else {
          await followUser({ targetUserId: userId });
        }
      } catch (error) {
        console.error("Error during follow/unfollow mutation:", error);
      }
    } catch (error) {
      console.error("Error in handleFollowToggle:", error);
    }
  };

  const renderUserItem = ({ item }: { item: User }) => (
    <View style={styles.userItem}>
      <TouchableOpacity
        style={styles.userInfo}
        onPress={() => router.push(`/user/${item._id}`)}
      >
        {item.image ? (
          <ExpoImage
            source={{ uri: item.image }}
            style={styles.avatar}
            contentFit="cover"
          />
        ) : (
          <View style={styles.avatarFallback}>
            <Text style={styles.avatarText}>
              {item.name?.[0]?.toUpperCase() || "U"}
            </Text>
          </View>
        )}
        <View style={styles.userDetails}>
          <Text style={styles.username}>{item.username}</Text>
          <Text style={styles.name}>{item.name}</Text>
        </View>
      </TouchableOpacity>
      {currentUser?._id !== item._id && (
        <TouchableOpacity
          style={[
            styles.followButton,
            item.following && styles.followingButton,
          ]}
          onPress={() => handleFollowToggle(item._id)}
        >
          <Text
            style={[
              styles.followButtonText,
              item.following && styles.followingButtonText,
            ]}
          >
            {item.following ? "Unfollow" : "Follow"}
          </Text>
        </TouchableOpacity>
      )}
    </View>
  );

  if (!profileUser) {
    return <FollowListSkeleton />;
  }

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={handleBack} style={styles.backButton}>
          <Ionicons name="chevron-back" size={24} color="#fff" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>{profileUser.username}</Text>
        <View style={styles.backButton} />
      </View>

      {/* Tabs */}
      <View style={styles.tabs}>
        <TouchableOpacity
          style={[styles.tab, activeTab === "followers" && styles.activeTab]}
          onPress={() => setActiveTab("followers")}
        >
          <Text
            style={[
              styles.tabText,
              activeTab === "followers" && styles.activeTabText,
            ]}
          >
            {followStats?.followers || 0} Followers
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.tab, activeTab === "following" && styles.activeTab]}
          onPress={() => setActiveTab("following")}
        >
          <Text
            style={[
              styles.tabText,
              activeTab === "following" && styles.activeTabText,
            ]}
          >
            {followStats?.following || 0} Following
          </Text>
        </TouchableOpacity>
        {!isOwnProfile && (
          <TouchableOpacity
            style={[styles.tab, activeTab === "mutuals" && styles.activeTab]}
            onPress={() => setActiveTab("mutuals")}
          >
            <Text
              style={[
                styles.tabText,
                activeTab === "mutuals" && styles.activeTabText,
              ]}
            >
              {mutuals?.length || 0} Mutual
            </Text>
          </TouchableOpacity>
        )}
      </View>

      {/* User List */}
      <FlatList
        data={activeList as User[]}
        renderItem={renderUserItem}
        keyExtractor={(item) => item._id}
        contentContainerStyle={styles.listContent}
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <Text style={styles.emptyText}>
              {activeTab === "followers"
                ? "No followers yet"
                : activeTab === "following"
                  ? "Not following anyone yet"
                  : "No mutual followers yet"}
            </Text>
          </View>
        }
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#1C1C1E",
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: "#333",
  },
  backButton: {
    width: 40,
    height: 40,
    justifyContent: "center",
    alignItems: "center",
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: "#fff",
  },
  tabs: {
    flexDirection: "row",
    borderBottomWidth: 1,
    borderBottomColor: "#333",
  },
  tab: {
    flex: 1,
    paddingVertical: 16,
    alignItems: "center",
  },
  activeTab: {
    borderBottomWidth: 2,
    borderBottomColor: "#1a96d2",
  },
  tabText: {
    color: "#8E8E93",
    fontSize: 16,
    fontWeight: "500",
  },
  activeTabText: {
    color: "#fff",
  },
  listContent: {
    padding: 16,
  },
  userItem: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    marginBottom: 16,
  },
  userInfo: {
    flexDirection: "row",
    alignItems: "center",
    flex: 1,
  },
  avatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
  },
  avatarFallback: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: "#333",
    justifyContent: "center",
    alignItems: "center",
  },
  avatarText: {
    color: "#fff",
    fontSize: 20,
    fontWeight: "bold",
  },
  userDetails: {
    marginLeft: 12,
  },
  username: {
    color: "#fff",
    fontSize: 16,
    fontWeight: "600",
    marginBottom: 2,
  },
  name: {
    color: "#8E8E93",
    fontSize: 14,
  },
  followButton: {
    backgroundColor: "#1a96d2",
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
    minWidth: 80,
    alignItems: "center",
  },
  followingButton: {
    backgroundColor: "#333",
  },
  followButtonText: {
    color: "#000",
    fontSize: 14,
    fontWeight: "600",
  },
  followingButtonText: {
    color: "#fff",
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "#000",
  },
  emptyContainer: {
    padding: 32,
    alignItems: "center",
  },
  emptyText: {
    color: "#8E8E93",
    fontSize: 16,
    textAlign: "center",
  },
});
