import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { Ionicons } from "@expo/vector-icons";
import { useRouter } from "expo-router";

interface LegalLinkProps {
  icon: keyof typeof Ionicons.glyphMap;
  title: string;
  url: string;
}

const LegalLink = ({ icon, title, url }: LegalLinkProps) => {
  const handlePress = async () => {
    try {
      const canOpen = await Linking.canOpenURL(url);
      if (canOpen) {
        await Linking.openURL(url);
      } else {
        Alert.alert("Error", "Cannot open this link");
      }
    } catch (error) {
      console.error("Failed to open link:", error);
      Alert.alert("Error", "Could not open the link");
    }
  };

  return (
    <TouchableOpacity style={styles.menuItem} onPress={handlePress}>
      <View style={styles.menuItemLeft}>
        <View style={styles.iconContainer}>
          <Ionicons name={icon} size={24} color="#fff" />
        </View>
        <Text style={styles.menuItemTitle}>{title}</Text>
      </View>
      <Ionicons name="open-outline" size={24} color="#8E8E93" />
    </TouchableOpacity>
  );
};

const LegalScreen = () => {
  const router = useRouter();

  return (
    <SafeAreaView style={styles.container} edges={["top"]}>
      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
      >
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => router.back()}
          >
            <Ionicons name="chevron-back" size={28} color="#fff" />
          </TouchableOpacity>
          <Text style={styles.title}>Legal</Text>
        </View>

        <View style={styles.section}>
          <LegalLink
            icon="document-text"
            title="Buyer Policy"
            url="https://liveciety.com/legal/buyer-policy"
          />
          <LegalLink
            icon="people"
            title="Community Guidelines"
            url="https://liveciety.com/legal/community-guidelines"
          />
          <LegalLink
            icon="refresh-circle"
            title="Return & Refund Policy"
            url="https://liveciety.com/legal/return-refund-cancellation"
          />
          <LegalLink
            icon="cart"
            title="Seller Policy"
            url="https://liveciety.com/legal/seller-policy"
          />
          <LegalLink
            icon="document-text"
            title="Seller Terms of Service"
            url="https://liveciety.com/legal/seller-terms-of-service"
          />
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

export default LegalScreen;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#1C1C1E",
  },
  scrollView: {
    flex: 1,
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 16,
    marginTop: 16,
    marginBottom: 24,
  },
  backButton: {
    marginRight: 8,
  },
  title: {
    fontSize: 34,
    fontWeight: "bold",
    color: "#fff",
  },
  section: {
    marginTop: 16,
  },
  menuItem: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingVertical: 16,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    borderBottomColor: "rgba(255,255,255,0.1)",
  },
  menuItemLeft: {
    flexDirection: "row",
    alignItems: "center",
    gap: 16,
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: "#2C2C2E",
    justifyContent: "center",
    alignItems: "center",
  },
  menuItemTitle: {
    color: "#fff",
    fontSize: 16,
    fontWeight: "500",
  },
});
