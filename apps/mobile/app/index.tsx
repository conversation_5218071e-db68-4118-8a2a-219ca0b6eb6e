import React, { useEffect } from "react";
import {
  View,
  Text,
  TextInput,
  StyleSheet,
  TouchableOpacity,
  ActivityIndicator,
  Dimensions,
  Image,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { StatusBar } from "expo-status-bar";
import { Authenticated, AuthLoading, Unauthenticated } from "convex/react";
import { router } from "expo-router";
import LoginScreen from "./screens/auth/login";
import { useUser } from "../hooks/use-user";
import { registerGlobals } from '@livekit/react-native';
import { PreloadedDataProvider } from "../hooks/preloaded-data-provider";

registerGlobals();

export default function App() {
  return (
    <SafeAreaView style={styles.container}>
      <StatusBar style="light" />

      <View style={styles.content}>
        <AuthLoading>
          <ActivityIndicator size="large" color="#fff" />
        </AuthLoading>

        <Unauthenticated>
          <LoginScreen />
        </Unauthenticated>

        <Authenticated>
          <AuthenticatedApp />
        </Authenticated>
      </View>
    </SafeAreaView>
  );
}

function AuthenticatedApp() {
  const { user, isLoading } = useUser();

  useEffect(() => {
    if (isLoading) return;

    if (user?.finishedSignUp !== true) {
      router.replace("/screens/auth/login");
    } else {
      router.replace("/(tabs)");
    }
  }, [user, isLoading]);

  if (isLoading) {
    return <ActivityIndicator size="large" color="#fff" />;
  }

  return (
    <PreloadedDataProvider>
      {null}
    </PreloadedDataProvider>
  );
}

const { width, height } = Dimensions.get("window");

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#18181B",
  },
  gradientContainer: {
    position: "absolute",
    width: width,
    height: height,
    opacity: 0.5,
  },
  gradient: {
    position: "absolute",
    width: width * 1.4,
    height: height * 1.4,
    top: -height * 0.2,
    borderRadius: width,
  },
  content: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
    paddingHorizontal: 20,
  },
  title: {
    fontSize: 32,
    fontWeight: "600",
    textAlign: "center",
    marginBottom: 16,
    color: "#fff",
  },
  subtitle: {
    fontSize: 18,
    textAlign: "center",
    marginBottom: 32,
    color: "#A1A1AA", // zinc-400
  },
  formContainer: {
    width: "100%",
    maxWidth: 400,
    marginTop: 24,
  },
  inputContainer: {
    flexDirection: "row",
    backgroundColor: "#27272A", // zinc-800
    borderRadius: 12,
    borderWidth: 1,
    borderColor: "#3F3F46", // zinc-700
    padding: 4,
  },
  input: {
    flex: 1,
    height: 48,
    paddingHorizontal: 16,
    fontSize: 16,
    color: "#fff",
    backgroundColor: "transparent",
  },
  button: {
    backgroundColor: "#fff",
    borderRadius: 8,
    paddingHorizontal: 20,
    height: 40,
    alignItems: "center",
    justifyContent: "center",
    alignSelf: "center",
  },
  buttonText: {
    color: "#18181B",
    fontSize: 16,
    fontWeight: "600",
  },
  messageContainer: {
    alignItems: "center",
    marginTop: 24,
  },
  successText: {
    fontSize: 16,
    color: "#22c55e",
    marginBottom: 16,
  },
  errorText: {
    fontSize: 16,
    color: "#ef4444",
    marginBottom: 16,
  },
  backButton: {
    fontSize: 14,
    color: "#A1A1AA",
    textDecorationLine: "underline",
  },
  icon: {
    width: 100,
    height: 100,
    marginBottom: 24,
    borderRadius: 20,
  },
});

export function HomeScreen() {
  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.content}>
        <Text>Home Screen</Text>
      </View>
    </SafeAreaView>
  );
}

const homeScreenStyles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#18181B",
  },
  content: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
  },
  text: {
    color: "#fff",
    fontSize: 20,
  },
});
