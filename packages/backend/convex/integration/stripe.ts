import { action } from "../_generated/server";
import { v } from "convex/values";
import { getCurrentUser } from "../helpers/utils";
import <PERSON><PERSON> from "stripe";
import { internal } from "../_generated/api";
import { Doc, Id } from "../_generated/dataModel";
import type { ActionCtx } from "../_generated/server";

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: "2025-02-24.acacia",
});

export const createConnectAccountLink = action({
  args: {},
  handler: async (ctx: any) => {
    const user = await ctx.runQuery(internal.users.INTERNAL_getUser, {});
    if (!user) throw new Error("Not authenticated");

    let stripeAccountId = user.stripeAccountId;
    if (!stripeAccountId) {
      const account = await stripe.accounts.create({
        type: "express",
        email: user.email,
      });
      stripeAccountId = account.id;
      await ctx.runMutation(internal.users.setStripeAccountId, {
        userId: user._id,
        stripeAccountId,
      });
    }

    const accountLink = await stripe.accountLinks.create({
      account: stripeAccountId,
      refresh_url: "https://decisive-perch-342.convex.site/stripe/refresh",
      return_url: "https://decisive-perch-342.convex.site/stripe/return",
      type: "account_onboarding",
    });

    return { url: accountLink.url };
  },
});

export const createSetupIntent = action({
  args: {},
  handler: async (ctx): Promise<{ clientSecret: string | null }> => {
    try {
      const user = await ctx.runQuery(internal.users.INTERNAL_getUser, {}) as Doc<"users">;
      if (!user) {
        throw new Error("Authentication required");
      }

      let stripeCustomerId = user.stripeCustomerId;
      if (!stripeCustomerId) {
        const customer = await stripe.customers.create({
          name: user.name || undefined,
          email: user.email,
          metadata: { userId: user._id },
        });
        stripeCustomerId = customer.id;
        await ctx.runMutation(internal.users.INTERNAL_updateUserStripeCustomerId, {
          userId: user._id,
          stripeCustomerId,
        });
      }

      const setupIntent = await stripe.setupIntents.create({
        customer: stripeCustomerId,
        payment_method_types: ['card'],
        usage: 'off_session',
      });

      return { clientSecret: setupIntent.client_secret };
    } catch (error) {
      console.error("Error creating setup intent:", error);
      throw new Error(`Failed to create setup intent: ${error instanceof Error ? error.message : String(error)}`);
    }
  },
});

export const getPaymentMethodDetails = action({
  args: {
    paymentMethodId: v.string()
  },
  handler: async (ctx, args): Promise<{
    id: string;
    last4: string;
    brand: string;
    expMonth: number;
    expYear: number;
  }> => {
    try {
      const user = await ctx.runQuery(internal.users.INTERNAL_getUser, {}) as Doc<"users">;
      if (!user) {
        throw new Error("Authentication required");
      }

      const paymentMethod = await stripe.paymentMethods.retrieve(args.paymentMethodId);
      
      if (!paymentMethod.card) {
        throw new Error("Payment method is not a card");
      }
      
      return {
        id: paymentMethod.id,
        last4: paymentMethod.card.last4 || '0000',
        brand: paymentMethod.card.brand || 'unknown',
        expMonth: paymentMethod.card.exp_month || 1,
        expYear: paymentMethod.card.exp_year || 2025,
      };
    } catch (error) {
      console.error("Error retrieving payment method details:", error);
      throw new Error(`Failed to retrieve payment method details: ${error instanceof Error ? error.message : String(error)}`);
    }
  },
});

export const detachPaymentMethod = action({
  args: {
    paymentMethodId: v.string(),
  },
  handler: async (ctx, args) => {
    try {
      const user = await ctx.runQuery(internal.users.INTERNAL_getUser, {}) as Doc<"users">;
      if (!user) {
        throw new Error("Authentication required");
      }
      
      const result = await stripe.paymentMethods.detach(args.paymentMethodId);
      return { success: true, result };
    } catch (error) {
      console.error("Error detaching payment method:", error);
      throw new Error(`Failed to detach payment method: ${error instanceof Error ? error.message : String(error)}`);
    }
  },
});

export const updateCustomerBillingAddress = action({
  args: {
    address: v.object({
      line1: v.string(),
      line2: v.optional(v.string()),
      city: v.string(),
      state: v.string(),
      country: v.string(),
      postal_code: v.string(),
      name: v.optional(v.string()),
    }),
  },
  handler: async (ctx, args) => {
    const user = await ctx.runQuery(internal.users.INTERNAL_getUser, {}) as Doc<"users">;
    if (!user) {
      throw new Error("Authentication required");
    }
    
    let stripeCustomerId = user.stripeCustomerId;
    if (!stripeCustomerId) {
      const customer = await stripe.customers.create({
        name: user.name || undefined,
        email: user.email,
        metadata: { userId: user._id },
        address: {
          line1: args.address.line1,
          line2: args.address.line2,
          city: args.address.city,
          state: args.address.state,
          country: args.address.country,
          postal_code: args.address.postal_code,
        },
      });
      stripeCustomerId = customer.id;
      await ctx.runMutation(internal.users.INTERNAL_updateUserStripeCustomerId, {
        userId: user._id,
        stripeCustomerId,
      });
      return { success: true };
    }
    
    await stripe.customers.update(stripeCustomerId, {
      address: {
        line1: args.address.line1,
        line2: args.address.line2,
        city: args.address.city,
        state: args.address.state,
        country: args.address.country,
        postal_code: args.address.postal_code,
      },
      name: args.address.name,
    });
    return { success: true };
  },
});

export const getConnectAccountStatus = action({
  args: {},
  handler: async (ctx: ActionCtx): Promise<{
    exists: boolean;
    details_submitted?: boolean;
    requirements?: Stripe.Account.Requirements;
    charges_enabled?: boolean;
    payouts_enabled?: boolean;
    email?: string | null;
    type?: string;
    status?: Stripe.Account.Capabilities;
  }> => {
    const user = await ctx.runQuery(internal.users.INTERNAL_getUser, {}) as Doc<"users"> | null;
    if (!user) throw new Error("Not authenticated");
    if (!user.stripeAccountId) {
      return { exists: false };
    }
    const account = await stripe.accounts.retrieve(user.stripeAccountId) as Stripe.Account;
    return {
      exists: true,
      details_submitted: account.details_submitted,
      requirements: account.requirements,
      charges_enabled: account.charges_enabled,
      payouts_enabled: account.payouts_enabled,
      email: account.email,
      type: account.type,
      status: account.capabilities,
    };
  },
});

export const disconnectStripeAccount = action({
  args: {},
  handler: async (ctx) => {
    const user = await ctx.runQuery(internal.users.INTERNAL_getUser, {});
    if (!user) throw new Error("Not authenticated");
    await ctx.runMutation(internal.users.setStripeAccountId, {
      userId: user._id,
      stripeAccountId: "",
    });
    return { success: true };
  },
});