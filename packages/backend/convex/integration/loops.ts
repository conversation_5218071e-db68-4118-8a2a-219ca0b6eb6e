import { LoopsClient, APIError } from "loops";

const loops = new LoopsClient(process.env.LOOPS_API_KEY as string);

export async function testCreateContact(email: string) {
  try {
    const resp = await loops.createContact(email);
    return resp;
  } catch (error) {
    if (error instanceof APIError) {
      console.error(error.json);
      console.error(error.statusCode);
      throw error;
    } else {
      throw error;
    }
  }
}