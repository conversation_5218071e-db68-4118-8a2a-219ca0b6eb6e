"use node";

import { v } from "convex/values";
import { action } from "../_generated/server";
import OpenAI from "openai";
import { api } from "../_generated/api";

const apiKey = process.env.OPENAI_API_KEY;
const apiModel = "gpt-4.1";

if (!apiKey) {
  throw new Error(
    "OPENAI_API_KEY environment variable is not set. Please set it in your Convex deployment.",
  );
}

const openai = new OpenAI({
  apiKey,
});

export const generateUsernameSuggestions = action({
  args: {
    name: v.string(),
  },
  handler: async (ctx, args) => {
    try {
      const firstName = args.name.split(" ")[0];

      const response = await openai.chat.completions.create({
        messages: [
          {
            role: "system",
            content:
              "You are a helpful assistant that generates username suggestions based on first names. Generate usernames that are fun, memorable, and appropriate. Only use alphanumeric characters, no spaces or special characters. Consider incorporating elements that reflect personality traits commonly associated with the name or creative variations of the name.",
          },
          {
            role: "user",
            content: `Please generate 8 unique username suggestions based on this first name: ${firstName}. Return them as a JSON array of strings. Make them fun, creative, and personal but appropriate. You can include numbers but don't just append random numbers - make them meaningful if used.`,
          },
        ],
        model: apiModel,
        temperature: 0.7,
        max_tokens: 150,
      });

      const content = response.choices[0]?.message.content;
      if (!content) throw new Error("No suggestions generated");

      const suggestions = JSON.parse(content);
      if (!Array.isArray(suggestions))
        throw new Error("Invalid suggestions format");

      const availableSuggestions = [];
      for (const suggestion of suggestions) {
        const existingUser = await ctx.runQuery(
          api.users.checkUsernameAvailable,
          { username: suggestion },
        );
        if (existingUser) {
          availableSuggestions.push(suggestion);
        }
        if (availableSuggestions.length >= 5) break;
      }

      if (availableSuggestions.length < 5) {
        const baseName = firstName?.toLowerCase().replace(/[^a-z0-9]/g, "") ?? "";
        let attempts = 0;
        while (availableSuggestions.length < 5 && attempts < 10) {
          const randomNum = Math.floor(Math.random() * 9999);
          const suggestion = `${baseName}${randomNum}`;
          const isAvailable = await ctx.runQuery(
            api.users.checkUsernameAvailable,
            { username: suggestion },
          );
          if (isAvailable) {
            availableSuggestions.push(suggestion);
          }
          attempts++;
        }
      }

      return availableSuggestions.slice(0, 5);
    } catch (error) {
      console.error("OpenAI API error:", error);
      const firstName = args.name.split(" ")[0];
      const baseName = firstName?.toLowerCase().replace(/[^a-z0-9]/g, "");
      const fallbackSuggestions = [];
      let attempts = 0;
      while (fallbackSuggestions.length < 5 && attempts < 10) {
        const randomNum = Math.floor(Math.random() * 9999);
        const suggestion = `${baseName}${randomNum}`;
        const isAvailable = await ctx.runQuery(
          api.users.checkUsernameAvailable,
          { username: suggestion },
        );
        if (isAvailable) {
          fallbackSuggestions.push(suggestion);
        }
        attempts++;
      }
      return fallbackSuggestions;
    }
  },
});

export const chat = action({
  args: {
    messages: v.array(
      v.object({
        content: v.string(),
        role: v.union(
          v.literal("system"),
          v.literal("user"),
          v.literal("assistant"),
        ),
      }),
    ),
  },
  handler: async (ctx, args) => {
    try {
      const response = await openai.chat.completions.create({
        messages: args.messages,
        model: apiModel,
      });

      return response.choices[0]?.message;
    } catch (error) {
      console.error("OpenAI API error:", error);
      throw new Error(`Failed to get a response from OpenAI: ${error}`);
    }
  },
});

export const streamChat = action({
  args: {
    messages: v.array(
      v.object({
        content: v.string(),
        role: v.union(
          v.literal("system"),
          v.literal("user"),
          v.literal("assistant"),
        ),
      }),
    ),
  },
  handler: async (ctx, args) => {
    try {
      const completion = await openai.chat.completions.create({
        messages: args.messages,
        model: "gpt-3.5-turbo",
        stream: true,
      });

      return completion;
    } catch (error) {
      console.error("OpenAI API error:", error);
      throw new Error(
        `Failed to get a streaming response from OpenAI: ${error}`,
      );
    }
  },
});
