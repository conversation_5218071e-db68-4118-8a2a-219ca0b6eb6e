import { v } from "convex/values";
import { mutation, query } from "./_generated/server";
import { getCurrentUser } from "./helpers/utils";
import { Id } from "./_generated/dataModel";

/**
 * @name list
 * @description List notifications for the current user
 */
export const list = query({
  args: {},
  handler: async (ctx) => {
    const user = await getCurrentUser(ctx);
    if (!user) {
      throw new Error("User not found");
    }

    const notifications = await ctx.db
      .query("adminNotifications")
      .filter((q) => q.eq(q.field("userId"), user._id))
      .filter((q) => q.eq(q.field("archived"), false))
      .collect();

    const unreadCount = notifications.filter((n) => !n.read).length;

    const archivedNotifications = await ctx.db
      .query("adminNotifications")
      .filter((q) => q.eq(q.field("userId"), user._id))
      .filter((q) => q.eq(q.field("archived"), true))
      .collect();

    return {
      notifications: notifications || [],
      archived: archivedNotifications || [],
      unreadCount: unreadCount || 0,
    };
  },
});

/**
 * @name create
 * @description Create a new notification
 */
export const create = mutation({
  args: {
    message: v.string(),
    type: v.string(),
    userId: v.id("users"),
    metadata: v.optional(
      v.object({
        expiresAt: v.optional(v.number()),
        link: v.optional(v.string()),
        createdBy: v.optional(v.union(v.id("users"), v.literal("system"))),
        actionRequired: v.optional(v.boolean()),
        status: v.optional(
          v.union(v.literal("accepted"), v.literal("rejected")),
        ),
      }),
    ),
  },
  handler: async (ctx, args) => {
    const user = await getCurrentUser(ctx);
    if (!user) {
      throw new Error("Not authenticated");
    }

    const existingNotification = await ctx.db
      .query("adminNotifications")
      .filter((q) =>
        q.and(
          q.eq(q.field("type"), args.type),
          q.eq(q.field("userId"), args.userId),
          q.eq(q.field("handled"), false),
        ),
      )
      .first();

    if (existingNotification) {
      return existingNotification;
    }

    const defaultMetadata = {
      createdBy: user._id,
      actionRequired: false,
      status: undefined as undefined | "accepted" | "rejected",
      expiresAt: undefined,
      link: undefined,
    };

    const notification = await ctx.db.insert("adminNotifications", {
      message: args.message,
      type: args.type,
      userId: args.userId,
      metadata: {
        ...defaultMetadata,
        ...args.metadata,
      },
      read: false,
      handled: false,
      archived: false,
      updatedAt: Date.now(),
    });

    return notification;
  },
});

/**
 * @name updateStatus
 * @description Update the status of a notification
 */
export const updateStatus = mutation({
  args: {
    notificationId: v.id("adminNotifications"),
    status: v.union(v.literal("accepted"), v.literal("rejected")),
  },
  handler: async (ctx, args) => {
    const user = await getCurrentUser(ctx);
    if (!user) {
      throw new Error("Not authenticated");
    }

    const notification = await ctx.db.get(args.notificationId);
    if (!notification) {
      throw new Error("Notification not found");
    }

    const currentMetadata = notification.metadata || {};
    await ctx.db.patch(args.notificationId, {
      metadata: {
        ...currentMetadata,
        status: args.status,
      },
      handled: true,
      updatedAt: Date.now(),
    });
  },
});

/**
 * @name markAsRead
 * @description Mark a notification as read
 */
export const markAsRead = mutation({
  args: {
    notificationId: v.id("adminNotifications"),
  },
  handler: async (ctx, args) => {
    const user = await getCurrentUser(ctx);
    if (!user) {
      throw new Error("Not authenticated");
    }

    await ctx.db.patch(args.notificationId, {
      read: true,
      updatedAt: Date.now(),
    });
  },
});
