import {
  action,
  internalMutation,
  internalQuery,
  mutation,
  query,
  MutationCtx,
} from "./_generated/server";
import { ConvexError, v } from "convex/values";
import { getCurrentUser } from "./helpers/utils";
import { Id } from "./_generated/dataModel";
import { internal } from "./_generated/api";

/**
 * @name generateUploadUrl
 * @description Generate a signed URL for file uploads from _storage
 * @returns {string} The signed URL for the file upload
 */
export const generateUploadUrl = mutation({
  args: {},
  handler: async (ctx) => {
    const user = await getCurrentUser(ctx);

    if (!user) {
      throw new ConvexError("you must be logged in to upload a file");
    }

    const result = await ctx.storage.generateUploadUrl();

    return result;
  },
});

/**
 * @name getTypeById
 * @description Get the type of an object by its ID
 * @param {string} id - The ID of the object
 * @returns {string} The type of the object
 */
export const getTypeById = internalQuery({
  args: {
    id: v.any(),
  },
  handler: async (ctx, args) => {
    return await ctx.db.get(args.id);
  },
});

/**
 * @name deleteFileById
 * @description Delete a file by its ID from _storage
 * @param {string} storageId - The ID of the file to delete
 */
export const deleteFileById = internalMutation({
  args: {
    storageId: v.id("_storage"),
  },
  handler: async (ctx, args) => {
    try {
      return await ctx.storage.delete(args.storageId as Id<"_storage">);
    } catch (error) {
      if (error instanceof Error && error.message.includes("not found")) {
        return true;
      }
      throw error;
    }
  },
});

/**
 * @name updateTypeById
 * @description Update the type of an object by its ID
 * @param {string} id - The ID of the object
 * @param {string} image - The image of the object
 */
export const updateTypeById = internalMutation({
  args: {
    id: v.any(),
    image: v.string(),
  },
  handler: async (ctx, args) => {
    return await ctx.db.patch(args.id, { image: args.image });
  },
});

/**
 * @name deleteById
 * @description Delete an object by its ID
 * @param {string} id - The ID of the object
 * @param {string} storageId - The ID of the file to delete
 */
export const deleteById = action({
  args: {
    storageId: v.id("_storage"),
  },
  handler: async (ctx, args) => {
    const { storageId } = args;

    if (storageId) {
      try {
        await ctx.runMutation(internal.files.deleteFileById, {
          storageId: storageId as Id<"_storage">,
        });
      } catch (error) {
        console.error(`File with ID ${storageId} not found or already deleted`);
      }
    }

    return { success: true };
  },
});

/**
 * @name getImageUrl
 * @description Get the URL for a stored image
 * @param {Id<"_storage">} storageId - The storage ID of the image
 * @returns {string | null} The URL of the image
 */
export const getImageUrl = query({
  args: {
    storageId: v.optional(v.id("_storage")),
  },
  handler: async (ctx, args) => {
    if (!args.storageId) return null;
    return await ctx.storage.getUrl(args.storageId);
  },
});
