import { v } from "convex/values";
import { mutation, query } from "./_generated/server";
import { getCurrentUser } from "./helpers/utils";
import { Id } from "./_generated/dataModel";

export const submitReport = mutation({
  args: {
    contentType: v.string(),
    contentId: v.string(),
    reportedUserId: v.id("users"),
    reason: v.string(),
    additionalDetails: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const user = await getCurrentUser(ctx);

    if (!user) {
      throw new Error("User not found");
    }

    const report = await ctx.db.insert("reports", {
      contentType: args.contentType,
      contentId: args.contentId,
      reportedUserId: args.reportedUserId,
      reportedByUserId: user._id,
      reason: args.reason,
      additionalDetails: args.additionalDetails,
      status: "pending",
      updatedAt: new Date().getTime(),
    });

    return report;
  },
});

export const blockUser = mutation({
  args: {
    userId: v.id("users"),
  },
  handler: async (ctx, args) => {
    const user = await getCurrentUser(ctx);

    if (!user) {
      throw new Error("User not found");
    }

    const existingBlock = await ctx.db
      .query("blocks")
      .withIndex("by_both", (q) =>
        q.eq("blockerId", user._id).eq("blockedId", args.userId),
      )
      .first();

    if (existingBlock) {
      return existingBlock;
    }

    const block = await ctx.db.insert("blocks", {
      blockerId: user._id,
      blockedId: args.userId,
    });

    return block;
  },
});

export const isUserBlocked = mutation({
  args: {
    userId: v.id("users"),
  },
  handler: async (ctx, args) => {
    const user = await getCurrentUser(ctx);

    if (!user) {
      throw new Error("User not found");
    }

    const block = await ctx.db
      .query("blocks")
      .withIndex("by_both", (q) =>
        q.eq("blockerId", user._id).eq("blockedId", args.userId),
      )
      .first();

    return !!block;
  },
});

// Helper to check if a user is host or moderator
async function isHostOrModerator(ctx: any, streamId: Id<"streams">, userId: Id<"users">) {
  const stream = await ctx.db.get(streamId);
  if (!stream) throw new Error("Stream not found");
  return stream.hostId === userId || (stream.moderatorIds?.includes(userId));
}

export const addModerator = mutation({
  args: { streamId: v.id("streams"), userIdToAdd: v.id("users") },
  handler: async (ctx, args) => {
      const currentUser = await getCurrentUser(ctx);
      if (!currentUser) throw new Error("Authentication required.");

      const stream = await ctx.db.get(args.streamId);
      if (!stream) throw new Error("Stream not found.");
      if (stream.hostId !== currentUser._id) throw new Error("Only the host can add moderators.");
      if (args.userIdToAdd === stream.hostId) throw new Error("Host cannot be added as a moderator.");

      const currentModerators = stream.moderatorIds || [];
      if (currentModerators.includes(args.userIdToAdd)) {
          return; 
      }
      await ctx.db.patch(args.streamId, { moderatorIds: [...currentModerators, args.userIdToAdd] });
      return null;
  }
});

export const removeModerator = mutation({
  args: { streamId: v.id("streams"), userIdToRemove: v.id("users") },
  handler: async (ctx, args) => {
      const currentUser = await getCurrentUser(ctx);
      if (!currentUser) throw new Error("Authentication required.");

      const stream = await ctx.db.get(args.streamId);
      if (!stream) throw new Error("Stream not found.");
      if (stream.hostId !== currentUser._id) throw new Error("Only the host can remove moderators.");

      const currentModerators = stream.moderatorIds || [];
      await ctx.db.patch(args.streamId, { moderatorIds: currentModerators.filter(id => id !== args.userIdToRemove) });
      return null;
  }
});

export const blockUserFromChat = mutation({
  args: { streamId: v.id("streams"), userIdToBlock: v.id("users") },
  handler: async (ctx, args) => {
      const currentUser = await getCurrentUser(ctx);
      if (!currentUser) throw new Error("Authentication required.");
      if (!(await isHostOrModerator(ctx, args.streamId, currentUser._id))) {
          throw new Error("Only host or moderators can block users.");
      }
      
      const stream = await ctx.db.get(args.streamId);
      if(!stream) throw new Error("Stream not found");
      if(args.userIdToBlock === stream.hostId) throw new Error("Cannot block the host.");
      if(stream.moderatorIds?.includes(args.userIdToBlock)) throw new Error("Cannot block a moderator. Remove moderator status first.");


      const currentBlocked = stream.blockedUserIds || [];
      if (currentBlocked.includes(args.userIdToBlock)) {
          return; 
      }
      await ctx.db.patch(args.streamId, { blockedUserIds: [...currentBlocked, args.userIdToBlock] });
      return null;
  }
});

export const unblockUserFromChat = mutation({
  args: { streamId: v.id("streams"), userIdToUnblock: v.id("users") },
  handler: async (ctx, args) => {
      const currentUser = await getCurrentUser(ctx);
      if (!currentUser) throw new Error("Authentication required.");
       if (!(await isHostOrModerator(ctx, args.streamId, currentUser._id))) {
          throw new Error("Only host or moderators can unblock users.");
      }
      const stream = await ctx.db.get(args.streamId);
      if(!stream) throw new Error("Stream not found");

      const currentBlocked = stream.blockedUserIds || [];
      await ctx.db.patch(args.streamId, { blockedUserIds: currentBlocked.filter(id => id !== args.userIdToUnblock) });
      return null;
  }
});

export const getStreamModerationData = query({
  args: { streamId: v.id("streams") },
  handler: async (ctx, args) => {
      const stream = await ctx.db.get(args.streamId);
      if (!stream) return null;

      const currentUser = await getCurrentUser(ctx);
      if (!currentUser) throw new Error("Authentication required.");
      const isCurrentUserHost = stream.hostId === currentUser._id;
      const isCurrentUserMod = stream.moderatorIds?.includes(currentUser._id) ?? false;

      const moderators = stream.moderatorIds ? await Promise.all(stream.moderatorIds.map(id => ctx.db.get(id))) : [];
      const blockedUsers = stream.blockedUserIds ? await Promise.all(stream.blockedUserIds.map(id => ctx.db.get(id))) : [];

      return {
          hostId: stream.hostId,
          moderatorIds: stream.moderatorIds || [],
          blockedUserIds: stream.blockedUserIds || [],
          moderators: moderators.filter(Boolean).map(u => ({_id: u!._id, username: u!.username ?? u!.email ?? u!._id.toString(), color: u!.color})), 
          blockedUsers: blockedUsers.filter(Boolean).map(u => ({_id: u!._id, username: u!.username ?? u!.email ?? u!._id.toString(), color: u!.color})),
          isCurrentUserHost,
          isCurrentUserMod,
      };
  }
});
