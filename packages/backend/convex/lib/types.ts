import { Profile } from "@auth/core/types";
import { Doc, Id } from "../_generated/dataModel";
import { Infer, v } from "convex/values";
import {
  CURRENCY_VALIDATOR,
  INTERVAL_VALIDATOR,
  LOGIN_TYPE,
  PLAN_KEY_VALIDATOR,
  USER_STATUS,
} from "./validators";
import { PERMISSIONS, ROLES } from "./constants";

export type User = Doc<"users"> & {
  _id: Id<"users">;
  name: string;
  username: string;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  image?: string;
  color: string;
  lastLoginType: LoginType;
  emailVerificationTime?: number;
  phoneVerificationTime?: number;
  isAnonymous?: boolean;
  status?: UserStatus;
  role: UserRole;
  followers?: Doc<"follows">[];
  following?: Doc<"follows">[];
};

export interface ExtendedProfile extends Profile {
  sub?: string;
  name?: string;
  given_name?: string;
  family_name?: string;
  middle_name?: string;
  nickname?: string;
  preferred_username?: string;
  profile?: string;
  picture?: string;
  website?: string;
  gender?: string;
  birthdate?: string;
  zoneinfo?: string;
  locale?: string;
  updated_at?: string;

  email?: string;
  email_verified?: boolean;

  phone_number?: string;
  phone_number_verified?: boolean;

  username?: string;
  loginType?: string;

  address?: {
    formatted?: string;
    street_address?: string;
    locality?: string;
    region?: string;
    postal_code?: string;
    country?: string;
  };

  access_token?: string;
  refresh_token?: string;
  expires_at?: number;
  expires_in?: number;
  token_type?: string;
  id_token?: string;
  scope?: string;

  hd?: string;
  verified_email?: boolean;
  language?: string;
  avatar_url?: string;

  [key: string]: any;
}

export type RoomMember = {
  id: Id<"users">;
  role: UserRole;
  status: UserStatus;
};

export type PresenceData = {
  data: any;
  present: boolean;
  latestJoin: number;
};

export type Currency = Infer<typeof CURRENCY_VALIDATOR>;
export type Interval = Infer<typeof INTERVAL_VALIDATOR>;
export type PlanKey = Infer<typeof PLAN_KEY_VALIDATOR>;
export type LoginType = Infer<typeof LOGIN_TYPE>;
export type UserStatus = Infer<typeof USER_STATUS>;
export type Permission =
  | (typeof PERMISSIONS.ROOM)[keyof typeof PERMISSIONS.ROOM]
  | (typeof PERMISSIONS.USER)[keyof typeof PERMISSIONS.USER];
export type UserRole = (typeof ROLES)[keyof typeof ROLES];

export const TASK_STATUS = v.union(
  v.literal("backlog"),
  v.literal("todo"),
  v.literal("in_progress"),
  v.literal("review"),
  v.literal("done"),
);
export type TaskStatus = Infer<typeof TASK_STATUS>;

export const TASK_PRIORITY = v.union(
  v.literal("no_priority"),
  v.literal("urgent"),
  v.literal("high"),
  v.literal("medium"),
  v.literal("low"),
);
export type TaskPriority = Infer<typeof TASK_PRIORITY>;

export type Task = {
  _id: Id<"tasks">;
  _creationTime: number;
  title: string;
  description?: string;
  createdBy: Id<"users">;
  dueDate?: number;
  status: TaskStatus;
  lastUpdated?: number;
  assignee?: User | null;
  related?: User[] | null;
  priority: TaskPriority;
  position?: number;
  isFavorite?: boolean;
  statusHistory?: Array<{
    status: TaskStatus;
    timestamp: number;
    userId: Id<"users">;
  }>;
};

export const NOTIFICATION_TYPES = {
  TASK_ASSIGNED: "task_assigned",
  TASK_UPDATED: "task_updated",
  TASK_COMPLETED: "task_completed",
} as const;

export const ACTIVITY_TYPES = {
  TASK_CREATED: "task_created",
  TASK_UPDATED: "task_updated",
  TASK_COMPLETED: "task_completed",
} as const;

export type Contact = Doc<"contacts"> & {
  _id: Id<"contacts">;
  firstName?: string;
  lastName?: string;
  email?: string;
  phone?: string;
  city?: string;
  createdBy: Id<"users">;
  title?: string;
  lastUpdated?: number;
  tasks?: Id<"tasks">[];
};

export const FEEDBACK_STATUS = v.union(
  v.literal("new"),
  v.literal("in_progress"),
  v.literal("done"),
  v.literal("rejected")
);
export const FEEDBACK_TYPE = v.union(
  v.literal("bug"),
  v.literal("feature_request")
);
export type FeedbackType = Infer<typeof FEEDBACK_TYPE>;
export type FeedbackStatus = Infer<typeof FEEDBACK_STATUS>;

export type Feedback = {
  _id: Id<"feedback">;
  _creationTime: number;
  userId: Id<"users">;
  type: FeedbackType;
  message: string;
  url?: string;
  metadata?: {
    browser?: string;
    device?: string;
    os?: string;
  };
  status: FeedbackStatus;
  createdAt: number;
  updatedAt?: number;
  updatedBy?: Id<"users">;
  notes?: string;
  position?: number;
  statusHistory?: Array<{
    status: FeedbackStatus;
    timestamp: number;
    userId: Id<"users">;
  }>;
  user: User;
};