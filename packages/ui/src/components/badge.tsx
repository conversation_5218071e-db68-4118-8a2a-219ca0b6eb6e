import * as React from "react";
import { Slot } from "@radix-ui/react-slot";
import { cva, type VariantProps } from "class-variance-authority";

import { cn } from "@workspace/ui/lib/utils";

const badgeVariants = cva(
  "inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",
  {
    variants: {
      variant: {
        default:
          "rounded-sm bg-zinc-100 dark:bg-zinc-900 px-1 py-0 text-xs font-medium border border-input font-mono",
        primary:
          "border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90 focus-visible:ring-primary/20 dark:focus-visible:ring-primary/40 dark:bg-primary/70",
        secondary:
          "border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",
        destructive:
          "border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/70",
        outline:
          "text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground",
      },
    },
    defaultVariants: {
      variant: "default",
    },
  },
);

function Badge({
  className,
  variant,
  asChild = false,
  ...props
}: React.ComponentProps<"span"> &
  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {
  const Comp = asChild ? Slot : "span";

  return (
    <Comp
      data-slot="badge"
      className={cn(badgeVariants({ variant }), className)}
      {...props}
    />
  );
}

interface CustomBadgeProps {
  className?: string;
  variant?:
    | "success"
    | "error"
    | "default"
    | "warning"
    | "proposal"
    | "vacant"
    | "occupied"
    | "host"
    | "mod";
  children?: React.ReactNode;
  circle?:
    | "success"
    | "error"
    | "warning"
    | "proposal"
    | "vacant"
    | "occupied"
    | "default"
    | "host"
    | "mod";
  style?: React.CSSProperties;
}

function CustomBadge({
  className,
  variant = "default",
  circle,
  style,
  ...props
}: CustomBadgeProps) {
  const variantClasses = {
    success: "bg-green-900 border border-green-700",
    error: "bg-red-900 border border-red-700",
    warning: "bg-yellow-900 border border-yellow-700",
    proposal: "bg-blue-900 border border-blue-700",
    vacant: "bg-zinc-300 border-zinc-100",
    default:
      "dark:bg-zinc-800 dark:border-zinc-700 bg-zinc-100 border-zinc-200",
    occupied: "bg-teal-900 border-teal-700",
    host: "bg-blue-900 border-blue-700",
    mod: "bg-yellow-900 border-yellow-700"
  };

  const circleClasses = {
    success: "bg-green-500",
    error: "bg-red-500",
    warning: "bg-yellow-500",
    proposal: "bg-blue-500",
    vacant: "bg-zinc-300",
    occupied: "bg-teal-500",
    default: "bg-zinc-500",
    host: "bg-blue-500 animate-pulse",
    mod: "bg-yellow-500"
  };

  return (
    <div
      className={cn(
        "text-xs w-fit !py-0.25 !px-1 border rounded-md",
        variantClasses[variant],
        {
          "text-zinc-50":
            variant === "success" ||
            variant === "proposal" ||
            variant === "occupied" ||
            variant === "host" ||
            variant === "mod" ||
            variant === "default",
          "text-white": variant === "error",
          "text-zinc-900": variant === "warning" || variant === "vacant",
        },
        className,
        circle ? "flex flex-row items-center space-x-2" : "",
      )}
      style={style}
      {...props}
    >
      {circle && (
        <div
          className={cn(
            "w-2 h-2 rounded-full mr-1", 
            circleClasses[circle]
          )}
        />
      )}
      {props.children}
    </div>
  );
}

export { Badge, CustomBadge, badgeVariants };
