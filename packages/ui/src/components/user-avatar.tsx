import React from "react";
import {
  Avatar,
  AvatarFallback,
  AvatarImage,
} from "@workspace/ui/components/avatar";
import { cn } from "@workspace/ui/lib/utils";

function getAvatarImageUrl(image?: string | null): string | undefined {
  if (!image) return undefined;
  
  if (image.startsWith("http://") || image.startsWith("https://")) {
    return image;
  }
  
  if (image.includes("/getImage?storageId=")) {
    return image;
  }
  
  return `${process.env.NEXT_PUBLIC_SITE_URL}/getImage?storageId=${image}`;
}

export interface UserAvatarProps extends React.HTMLAttributes<HTMLDivElement> {
  user?: {
    image?: string | null;
    name?: string | null;
    color?: string;
    status?: "online" | "idle" | "offline";
  };
  size?: "sm" | "md" | "lg";
  showStatus?: boolean;
  suspended?: boolean;
}

const sizeClasses = {
  sm: "size-6",
  md: "size-8",
  lg: "size-10",
};

const statusColors = {
  online: "bg-green-500",
  idle: "bg-yellow-500",
  offline: "bg-gray-500",
  suspended: "bg-red-400",
};

export const UserAvatar = React.forwardRef<HTMLDivElement, UserAvatarProps>(
  (
    {
      user,
      size = "md",
      showStatus = true,
      className,
      suspended = false,
      ...props
    },
    ref,
  ) => {
    return (
      <div className="relative" ref={ref} {...props}>
        <Avatar className={cn(sizeClasses[size], "relative", className)}>
          {!suspended && (
            <AvatarImage 
              src={getAvatarImageUrl(user?.image)} 
              crossOrigin="anonymous"
              referrerPolicy="no-referrer"
            />
          )}
          <AvatarFallback
            className={cn(
              "text-xs",
              suspended && "bg-red-500/50 border-red-400/90 border",
            )}
            style={{
              backgroundColor: `${user?.color}90`,
              border:
                user?.color && user?.status !== "offline"
                  ? `1px solid ${user.color}`
                  : "none",
            }}
          >
            {user?.name?.charAt(0) || "?"}
          </AvatarFallback>
        </Avatar>
        {showStatus &&
          user?.status &&
          user.status !== "offline" &&
          !suspended && (
            <span
              className={cn(
                "absolute bottom-0 right-0 size-2 rounded-full border",
                statusColors[user.status],
              )}
            />
          )}
      </div>
    );
  },
);

UserAvatar.displayName = "UserAvatar";
