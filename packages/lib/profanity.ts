/**
 * Content Moderation Best Practices
 *
 * Instead of maintaining explicit lists of prohibited terms, consider these approaches:
 */

export const ContentModerationApproaches = {
  /**
   * 1. Use Natural Language Processing (NLP) Services
   * - Services like Perspective API (https://perspectiveapi.com/)
   * - Amazon Comprehend
   * - Azure Content Moderator
   */
  recommendedServices: [
    "Perspective API",
    "Amazon Comprehend",
    "Azure Content Moderator",
    "TensorFlow.js with toxicity model",
  ] as const,

  /**
   * 2. Context-Aware Moderation
   * Consider factors like:
   */
  contextualFactors: [
    "Message intent",
    "Surrounding conversation context",
    "User history",
    "Community guidelines",
    "Cultural context",
  ] as const,

  /**
   * 3. Positive Community Guidelines
   */
  communityGuidelines: [
    "Be respectful and inclusive",
    "Focus on constructive dialogue",
    "Value diverse perspectives",
    "Promote educational discussions",
    "Foster a safe environment",
  ] as const,

  /**
   * 4. Multi-layered Approach
   */
  moderationLayers: [
    "Automated filtering",
    "Community reporting",
    "Human moderation",
    "Appeals process",
    "User education",
  ] as const,
};

/**
 * Example function for context-aware message filtering
 */
export interface MessageContext {
  content: string;
  userId: string;
  channelId: string;
  timestamp: Date;
}

export const shouldModerateMessage = async (
  context: MessageContext,
): Promise<boolean> => {

  return false;
};

/**
 * Example community guidelines checker
 */
export interface CommunityGuidelinesCheck {
  isRespectful: boolean;
  isConstructive: boolean;
  isSafe: boolean;
  recommendedActions?: string[];
}

export const checkCommunityGuidelines = (
  content: string,
): CommunityGuidelinesCheck => {
  return {
    isRespectful: true,
    isConstructive: true,
    isSafe: true,
    recommendedActions: [],
  };
};

/**
 * Resources for implementing better content moderation
 */
export const moderationResources = {
  apis: [
    {
      name: "Perspective API",
      url: "https://perspectiveapi.com/",
      features: ["Toxicity detection", "Threat detection", "Insult detection"],
    },
    {
      name: "Amazon Comprehend",
      url: "https://aws.amazon.com/comprehend/",
      features: [
        "Sentiment analysis",
        "Key phrase extraction",
        "Language detection",
      ],
    },
  ],

  bestPractices: [
    "Implement user reporting systems",
    "Provide clear community guidelines",
    "Offer appeals process",
    "Regular moderator training",
    "Transparent moderation policies",
  ],

  implementationTips: [
    "Use machine learning models for initial screening",
    "Combine automated and human moderation",
    "Consider context and intent",
    "Provide feedback to users",
    "Regular policy reviews and updates",
  ],
} as const;

/**
 * Username Validation Utilities
 */

const PROTECTED_TERMS = [
  "liveciety",
  "admin",
  "moderator",
  "mod",
  "support",
  "help",
  "system",
] as const;

const INAPPROPRIATE_TERMS = [
  "ass",
  "fuck",
  "shit",
  "piss",
  "dick",
  "cock",
  "pussy",
  "cunt",
  "bitch",
  "bastard",
  "whore",
  "slut",
  "penis",
  "vagina",
  "anal",
  "anus",
  "butt",
  "porn",
  "sex",
  "nsfw",
  "xxx",
  "adult",
] as const;

const CHAR_MAP: { [key: string]: string } = {
  "@": "a",
  "4": "a",
  "1": "i",
  "!": "i",
  "0": "o",
  "3": "e",
  $: "s",
  "5": "s",
  "+": "t",
  "7": "t",
};

/**
 * Simplifies text for comparison
 */
const simplifyText = (text: string): string => {
  let result = text.toLowerCase();

  for (const [char, replacement] of Object.entries(CHAR_MAP)) {
    while (result.includes(char)) {
      result = result.split(char).join(replacement);
    }
  }

  result = result
    .split("")
    .filter((char) => {
      return (char >= "a" && char <= "z") || (char >= "0" && char <= "9");
    })
    .join("");

  return result;
};

/**
 * Checks if text contains inappropriate content
 */
const containsInappropriateContent = (text: string): boolean => {
  const simplified = simplifyText(text);

  return INAPPROPRIATE_TERMS.some((term) =>
    simplified.includes(simplifyText(term)),
  );
};

/**
 * Validates a username against basic requirements and protected terms
 */
export const validateUsername = (
  username: string,
): { isValid: boolean; error?: string } => {
  const lowerUsername = username.toLowerCase();
  if (lowerUsername.length < 3) {
    return {
      isValid: false,
      error: "Username must be at least 3 characters long",
    };
  }

  if (lowerUsername.length > 30) {
    return {
      isValid: false,
      error: "Username must be less than 30 characters",
    };
  }

  const validChars =
    "abcdefghijklmnopqrstuvwxyz0123456789_-";
  for (const char of lowerUsername) {
    if (!validChars.includes(char)) {
      return {
        isValid: false,
        error:
          "Username can only contain lowercase letters, numbers, underscores, and hyphens",
      };
    }
  }

  // Disallow 'dog' unless part of 'underdog' or 'underdogs'
  if (
    lowerUsername.includes("dog") &&
    !lowerUsername.includes("underdog") &&
    !lowerUsername.includes("underdogs")
  ) {
    return {
      isValid: false,
      error: "Usernames containing 'dog' are not allowed unless it is 'underdog' or 'underdogs'",
    };
  }

  const simplifiedUsername = simplifyText(lowerUsername);
  for (const term of PROTECTED_TERMS) {
    if (simplifiedUsername.includes(simplifyText(term))) {
      return {
        isValid: false,
        error: "Username contains a protected or reserved term",
      };
    }
  }

  if (containsInappropriateContent(lowerUsername)) {
    return {
      isValid: false,
      error: "Username contains inappropriate content",
    };
  }

  return { isValid: true };
};

/**
 * Sanitizes a username
 */
export const sanitizeUsername = (username: string): string => {
  let sanitized = username
    .toLowerCase()
    .split("")
    .map((char) =>
      "abcdefghijklmnopqrstuvwxyz0123456789_-".includes(char) ? char : "_",
    )
    .join("");

  while (sanitized.includes("__")) {
    sanitized = sanitized.split("__").join("_");
  }

  sanitized = sanitized.replace(/^_+|_+$/g, "");

  if (
    containsInappropriateContent(sanitized) ||
    PROTECTED_TERMS.some((term) =>
      simplifyText(sanitized).includes(simplifyText(term)),
    )
  ) {
    const randomSuffix = Math.floor(Math.random() * 10000)
      .toString()
      .padStart(4, "0");
    sanitized = `user_${randomSuffix}`;
  }

  return sanitized;
};
