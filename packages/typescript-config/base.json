{"$schema": "https://json.schemastore.org/tsconfig", "display": "<PERSON><PERSON><PERSON>", "compilerOptions": {"declaration": true, "declarationMap": true, "esModuleInterop": true, "incremental": false, "isolatedModules": true, "lib": ["es2022", "DOM", "DOM.Iterable"], "module": "ESNext", "moduleDetection": "force", "moduleResolution": "bundler", "noUncheckedIndexedAccess": true, "resolveJsonModule": true, "skipLibCheck": true, "strict": false, "target": "ES2022", "baseUrl": "../..", "paths": {"@workspace/backend/*": ["packages/backend/*"], "@workspace/lib/*": ["packages/lib/*"], "@workspace/assets/*": ["packages/assets/*"]}}}