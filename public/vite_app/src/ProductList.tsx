import { useQuery } from "convex/react";
import { api } from "../convex/_generated/api";

export function ProductList() {
  const products = useQuery(api.products.listAvailableProducts);

  if (products === undefined) {
    return (
      <div className="flex justify-center items-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        <p className="ml-2 text-gray-600">Loading products...</p>
      </div>
    );
  }

  if (products.length === 0) {
    return <p className="text-center text-gray-600 py-8">No products available at the moment.</p>;
  }

  return (
    <div className="space-y-6">
       <h2 className="text-3xl font-bold text-gray-800 mb-6 text-center">Available Products</h2>
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
        {products.map((product) => (
          <div key={product._id} className="border rounded-lg shadow-lg overflow-hidden bg-white hover:shadow-xl transition-shadow duration-300">
            {product.imageUrl ? (
              <img
                src={product.imageUrl}
                alt={product.name}
                className="w-full h-48 object-cover"
              />
            ) : (
              <div className="w-full h-48 bg-gray-200 flex items-center justify-center">
                <span className="text-gray-500">No image</span>
              </div>
            )}
            <div className="p-4">
              <h3 className="text-lg font-semibold text-primary truncate">{product.name}</h3>
              <p className="text-sm text-gray-600 mt-1 h-10 overflow-hidden">{product.description}</p>
              <p className="text-xl font-bold text-gray-800 mt-2">${product.price.toFixed(2)}</p>
              <p className="text-xs text-gray-500 mt-1">Seller: {product.sellerName}</p>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
