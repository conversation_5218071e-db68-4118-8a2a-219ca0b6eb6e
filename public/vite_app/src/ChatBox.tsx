import { FormEvent, useState, useEffect, useRef } from "react";
import { useMutation, useQuery } from "convex/react";
import { api } from "../convex/_generated/api";
import { Id } from "../convex/_generated/dataModel";
import { toast } from "sonner";
import { Ban } from "lucide-react";

export function ChatBox({ streamId }: { streamId: Id<"streams"> }) {
  const messages = useQuery(api.chat.listMessages, { streamId }) || [];
  const sendMessageMutation = useMutation(api.chat.sendMessage);
  const [newMessageText, setNewMessageText] = useState("");
  const loggedInUser = useQuery(api.auth.loggedInUser);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const moderationData = useQuery(api.moderation.getStreamModerationData, streamId ? { streamId } : "skip");
  const blockUserMutation = useMutation(api.moderation.blockUserFromChat);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(scrollToBottom, [messages]);

  const handleSendMessage = async (event: FormEvent) => {
    event.preventDefault();
    if (!newMessageText.trim() || !loggedInUser) {
      setNewMessageText("");
      return;
    }
    try {
      await sendMessageMutation({ streamId, text: newMessageText.trim() });
      setNewMessageText("");
    } catch (error: any) {
      console.error("Failed to send message:", error);
      toast.error(`Failed to send message: ${error.data?.value || error.message}`);
    }
  };

  const handleBlockUser = async (userIdToBlock: Id<"users">) => {
    if (!moderationData || (!moderationData.isCurrentUserHost && !moderationData.isCurrentUserMod)) {
      toast.error("You do not have permission to block users.");
      return;
    }
    if (userIdToBlock === moderationData.hostId) {
        toast.error("Cannot block the host.");
        return;
    }
    if (moderationData.moderatorIds?.includes(userIdToBlock)) {
        toast.error("Cannot block a fellow moderator. Remove moderator status first if needed.");
        return;
    }

    try {
      await blockUserMutation({ streamId, userIdToBlock });
      toast.success("User blocked from chat.");
    } catch (error: any) {
      console.error("Failed to block user:", error);
      toast.error(`Failed to block user: ${error.data?.value || error.message}`);
    }
  };

  return (
    <div className="flex flex-col h-full bg-gray-800 text-white rounded-lg shadow-md overflow-hidden">
      <div className="p-4 border-b border-gray-700">
        <h3 className="text-lg font-semibold text-primary">Live Chat</h3>
      </div>
      <ul className="flex-1 p-4 space-y-2 overflow-y-auto">
        {messages.map((message) => {
          const canBlock = moderationData && 
                           (moderationData.isCurrentUserHost || moderationData.isCurrentUserMod) &&
                           message.userId !== moderationData.hostId && 
                           !moderationData.moderatorIds?.includes(message.userId) &&
                           message.userId !== loggedInUser?._id; // Cannot block self

          return (
            <li key={message._id} className="flex flex-col group">
              <div className="flex justify-between items-center w-full">
                <div className="text-xs text-gray-400 mb-0.5">
                  {message.userName}
                  <span className="ml-2 text-gray-500 text-[10px]">
                    {new Date(message._creationTime).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                  </span>
                </div>
                {canBlock && (
                  <button 
                    onClick={() => handleBlockUser(message.userId)}
                    className="p-1 text-red-500 hover:text-red-400 opacity-0 group-hover:opacity-100 transition-opacity"
                    title="Block user from chat"
                  >
                    <Ban size={14} />
                  </button>
                )}
              </div>
              <div className={`px-3 py-1.5 rounded-lg max-w-[80%] break-words ${message.userId === loggedInUser?._id ? "bg-primary self-end text-white" : "bg-gray-700 self-start"}`}>
                {message.text}
              </div>
            </li>
          );
        })}
        <div ref={messagesEndRef} />
      </ul>
      {loggedInUser && (
        <form onSubmit={handleSendMessage} className="p-4 border-t border-gray-700">
          <div className="flex">
            <input
              type="text"
              value={newMessageText}
              onChange={(e) => setNewMessageText(e.target.value)}
              placeholder="Say something..."
              className="flex-1 px-3 py-2 rounded-l-md bg-gray-700 border border-gray-600 text-gray-200 focus:outline-none focus:ring-1 focus:ring-primary"
              disabled={!loggedInUser || moderationData?.blockedUserIds?.includes(loggedInUser._id)}
            />
            <button
              type="submit"
              className="px-4 py-2 bg-primary hover:bg-primary-hover text-white font-semibold rounded-r-md disabled:opacity-50"
              disabled={!newMessageText.trim() || !loggedInUser || moderationData?.blockedUserIds?.includes(loggedInUser._id)}
            >
              Send
            </button>
          </div>
           {moderationData?.blockedUserIds?.includes(loggedInUser!._id) && (
             <p className="text-xs text-red-400 mt-1 text-center">You are blocked from sending messages in this stream.</p>
           )}
        </form>
      )}
      {!loggedInUser && (
        <div className="p-4 text-center text-sm text-gray-400 border-t border-gray-700">
            Sign in to chat.
        </div>
      )}
    </div>
  );
}
