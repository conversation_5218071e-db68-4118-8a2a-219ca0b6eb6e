import { useQuery } from "convex/react";
import { api } from "../convex/_generated/api";
import { Id } from "../convex/_generated/dataModel";
import { streamCategories } from "../convex/streams"; // Corrected import path
import { useState } from "react";

export interface ViewState { 
  type: string; 
  streamId?: Id<"streams">;
}

export function StreamList({
  setCurrentView,
}: {
  setCurrentView: (view: ViewState) => void;
}) {
  const [selectedCategory, setSelectedCategory] = useState<string>("All");
  const streams = useQuery(api.streams.listActiveStreams, selectedCategory === "All" ? {} : { category: selectedCategory }); 

  if (streams === undefined) {
    return (
      <div className="flex justify-center items-center p-10">
        <div className="animate-spin rounded-full h-10 w-10 border-b-2 border-primary"></div>
        <p className="ml-3 text-gray-600 text-lg">Loading live streams...</p>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex flex-col sm:flex-row justify-between items-center mb-8">
        <h2 className="text-4xl font-extrabold text-gray-800 text-center sm:text-left mb-4 sm:mb-0">
          Live Streams Happening Now
        </h2>
        <div className="flex items-center space-x-2">
            <label htmlFor="category-filter" className="text-sm font-medium text-gray-700">Filter by category:</label>
            <select
                id="category-filter"
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="px-3 py-1.5 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary text-sm bg-white"
            >
                <option value="All">All</option>
                {streamCategories.map((cat: string) => (
                    <option key={cat} value={cat}>{cat}</option>
                ))}
            </select>
        </div>
      </div>

      {streams.length === 0 ? (
         <div className="text-center py-10">
         <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
           <path vectorEffect="non-scaling-stroke" strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.59 14.37a6 6 0 01-5.84 7.38v-4.8m5.84-2.58a14.98 14.98 0 006.16-12.12A14.98 14.98 0 009.63 2.25M15.59 14.37A14.926 14.926 0 01 4.84 2.25L9.63 7.03m0 0A14.926 14.926 0 004.84 17.75l4.79-4.79M9.63 7.03l8.865-8.865m0 0A14.926 14.926 0 009.63 17.75M1.172 8.172a4.5 4.5 0 016.364 0L10 10.606l3.464-3.464a4.5 4.5 0 116.364 6.364L10 20.394l-9.192-9.192a4.5 4.5 0 010-6.364z" />
         </svg>
         <h3 className="mt-2 text-xl font-medium text-gray-900">No active streams {selectedCategory !== "All" ? `in ${selectedCategory}` : ""}</h3>
         <p className="mt-1 text-sm text-gray-500">Check back later or start your own stream!</p>
       </div>
      ) : (
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {streams.map((stream) => (
          <div
            key={stream._id}
            className="bg-white rounded-xl shadow-lg overflow-hidden transform hover:scale-105 transition-transform duration-300 cursor-pointer flex flex-col"
            onClick={() => setCurrentView({ type: "viewStream", streamId: stream._id })}
          >
            <div className="relative h-48 bg-gradient-to-br from-rose-400 to-red-500 flex items-center justify-center">
              <div className="absolute top-2 left-2 bg-red-600 text-white px-2 py-1 text-xs font-bold rounded animate-pulse">
                LIVE
              </div>
              <div className="absolute top-2 right-2 bg-black/50 text-white px-2 py-1 text-xs font-semibold rounded">
                {stream.category}
              </div>
               <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 text-white opacity-80" viewBox="0 0 20 20" fill="currentColor">
                <path d="M2 6a2 2 0 012-2h6a2 2 0 012 2v8a2 2 0 01-2 2H4a2 2 0 01-2-2V6zM14.553 5.106A1 1 0 0014 5v1.586l-1.793-1.793a1 1 0 10-1.414 1.414L12.586 8 10.793 9.793a1 1 0 101.414 1.414L14 9.414V11a1 1 0 001.553.832l3-4a1 1 0 000-1.664l-3-4z" />
              </svg>
            </div>
            <div className="p-5 flex-grow flex flex-col justify-between">
              <div>
                <h3 className="text-xl font-semibold text-primary mb-1 truncate" title={stream.title}>
                  {stream.title}
                </h3>
                <p className="text-sm text-gray-600 mb-2">
                  Hosted by: <span className="font-medium">{stream.hostName}</span>
                </p>
                {stream.productId && stream.productName && (
                  <p className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded inline-block">
                    Featuring: {stream.productName}
                  </p>
                )}
              </div>
              <button
                onClick={(e) => {
                  e.stopPropagation(); 
                  setCurrentView({ type: "viewStream", streamId: stream._id });
                }}
                className="mt-4 w-full bg-primary text-white font-semibold py-2 px-4 rounded-lg hover:bg-primary-hover transition-colors focus:outline-none focus:ring-2 focus:ring-primary focus:ring-opacity-50"
              >
                Watch Stream
              </button>
            </div>
          </div>
        ))}
      </div>
      )}
      
    </div>
  );
}
