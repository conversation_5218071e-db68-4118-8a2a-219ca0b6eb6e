import { useEffect, useState, useRef } from "react";
import { useAction, useQuery } from "convex/react";
import { api } from "../convex/_generated/api";
import { Id } from "../convex/_generated/dataModel";
import {
  Room,
  RoomEvent,
  RemoteParticipant,
  RemoteTrackPublication,
  Track,
  Participant, 
} from "livekit-client";
import { toast } from "sonner";
import { ChatBox } from "./ChatBox"; 
import { Volume2, VolumeX, Maximize, Minimize, Users } from "lucide-react"; 


const LIVEKIT_WS_URL = import.meta.env.VITE_LIVEKIT_WS_URL;

export function StreamView({ streamId }: { streamId: Id<"streams"> }) {
  const streamDetails = useQuery(api.streams.getStream, { streamId }); 
  const generateViewerToken = useAction(api.livekit.generateViewerToken);
  const getParticipantCountAction = useAction(api.livekit.getRoomParticipantsCount); 


  const [room, setRoom] = useState<Room | undefined>();
  const [token, setToken] = useState<string | undefined>();
  const [error, setError] = useState<string | undefined>();
  const [isConnected, setIsConnected] = useState(false);
  const [viewerCount, setViewerCount] = useState<number | null>(null);
  
  const videoContainerRef = useRef<HTMLDivElement>(null); 
  const videoRef = useRef<HTMLVideoElement>(null);
  const audioRef = useRef<HTMLAudioElement>(null); 

  const [isMuted, setIsMuted] = useState(false); 
  const [volume, setVolume] = useState(0.5);
  const [isFullscreen, setIsFullscreen] = useState(false);

  useEffect(() => {
    if (streamDetails?.status === "live" && streamDetails.roomName) {
      getParticipantCountAction({ roomName: streamDetails.roomName })
        .then(setViewerCount)
        .catch(err => console.error("Failed to fetch viewer count:", err));
      
      const intervalId = setInterval(() => {
        if (room?.state === "connected") { 
            getParticipantCountAction({ roomName: streamDetails.roomName })
            .then(setViewerCount)
            .catch(err => console.error("Failed to refresh viewer count:", err));
        }
      }, 30000); 
      return () => clearInterval(intervalId);

    } else {
      setViewerCount(null); 
    }
  }, [streamDetails?.status, streamDetails?.roomName, getParticipantCountAction, room?.state]);


  useEffect(() => {
    if (videoRef.current) {
      videoRef.current.volume = volume;
      videoRef.current.muted = isMuted; 
    }
    if (audioRef.current) {
      audioRef.current.volume = volume;
      audioRef.current.muted = isMuted;
    }
  }, [isMuted, volume]);

  const toggleMute = () => {
    setIsMuted(!isMuted);
  };

  const handleVolumeChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const newVolume = parseFloat(event.target.value);
    setVolume(newVolume);
    if (newVolume > 0 && isMuted) {
      setIsMuted(false);
    } else if (newVolume === 0 && !isMuted) {
      setIsMuted(true);
    }
  };
  
  const toggleFullscreen = () => {
    if (!videoContainerRef.current) return;
    if (!document.fullscreenElement) {
      videoContainerRef.current.requestFullscreen().catch(err => {
        toast.error(`Error attempting to enable full-screen mode: ${err.message} (${err.name})`);
      });
    } else {
      document.exitFullscreen();
    }
  };

  useEffect(() => {
    const handleFullscreenChange = () => {
      setIsFullscreen(!!document.fullscreenElement);
    };
    document.addEventListener('fullscreenchange', handleFullscreenChange);
    return () => document.removeEventListener('fullscreenchange', handleFullscreenChange);
  }, []);


  useEffect(() => {
    if (!LIVEKIT_WS_URL) {
      setError("LiveKit WebSocket URL is not configured. Please set VITE_LIVEKIT_WS_URL.");
      toast.error("LiveKit URL not configured.");
      return;
    }
    if (!streamId || !streamDetails) return;

    if (streamDetails.status !== "live") {
        setError(`Stream is not live. Current status: ${streamDetails.status}`);
        setIsConnected(false);
        if (room?.state === "connected") room.disconnect();
        return;
    }
    setError(undefined); 

    generateViewerToken({ streamId })
      .then(setToken)
      .catch((err) => {
        console.error("Failed to get viewer token:", err);
        setError("Failed to get stream token. Cannot view stream.");
        toast.error("Error fetching stream token.");
      });
  }, [streamId, generateViewerToken, streamDetails]);

  useEffect(() => {
    if (error) return; 
    if (!token || !LIVEKIT_WS_URL || !streamDetails?.roomName || streamDetails.status !== "live") {
        if (room?.state === "connected") room.disconnect();
        return;
    }

    const newRoom = new Room({
        adaptiveStream: true,
    });
    setRoom(newRoom);

    const connectToRoom = async () => {
      try {
        await newRoom.connect(LIVEKIT_WS_URL, token, {autoSubscribe: true}); 
        console.log("Connected to LiveKit room as viewer:", streamDetails.roomName);
        setIsConnected(true);

        newRoom.remoteParticipants.forEach((participant: RemoteParticipant) => {
            if (participant.identity === streamDetails.hostId) {
                for (const publication of participant.trackPublications.values()) { 
                    if (publication.track && publication.isSubscribed) {
                        handleTrackSubscribed(publication.track, participant);
                    }
                }
            }
        });

      } catch (e: any) {
        console.error("Failed to connect to LiveKit room:", e);
        setError(`Failed to connect: ${e.message}`);
        toast.error(`Connection failed: ${e.message}`);
        setIsConnected(false);
      }
    };

    connectToRoom();

    const handleTrackSubscribed = (track: Track, participant: Participant) => { 
        if (participant.identity === streamDetails.hostId) { 
            if (track.kind === Track.Kind.Video) {
                if (videoRef.current) {
                    track.attach(videoRef.current);
                }
            } else if (track.kind === Track.Kind.Audio) {
                 if (audioRef.current) { 
                    track.attach(audioRef.current);
                }
            }
        }
    };

    const handleTrackUnsubscribed = (track: Track, participant: Participant) => { 
        if (participant.identity === streamDetails.hostId) {
            track.detach();
            if (track.kind === Track.Kind.Video && videoRef.current) videoRef.current.srcObject = null;
        }
    };

    newRoom.on(RoomEvent.TrackSubscribed, (track: Track, publication: RemoteTrackPublication, participant: RemoteParticipant) => {
        if (participant.identity === streamDetails.hostId) { 
            handleTrackSubscribed(track, participant);
        }
    });

    newRoom.on(RoomEvent.TrackUnsubscribed, (track: Track, publication: RemoteTrackPublication, participant: RemoteParticipant) => {
        if (participant.identity === streamDetails.hostId) {
            handleTrackUnsubscribed(track, participant);
        }
    });
    
    newRoom.on(RoomEvent.Disconnected, () => {
      setIsConnected(false);
      toast.info("Stream ended or disconnected.");
    });
    newRoom.on(RoomEvent.Reconnecting, () => toast.info("Reconnecting to stream..."));
    newRoom.on(RoomEvent.Reconnected, () => toast.success("Reconnected to stream!"));
    newRoom.on(RoomEvent.ParticipantDisconnected, (participant: RemoteParticipant) => {
        if (streamDetails && participant.identity === streamDetails.hostId) { 
            toast.info("The host has ended the stream.");
            setError("The host has ended the stream.");
            setIsConnected(false);
            if (videoRef.current) videoRef.current.srcObject = null; 
        }
    });

    return () => {
      newRoom.disconnect(true);
      setRoom(undefined);
      setIsConnected(false);
    };
  }, [token, streamDetails?.roomName, streamDetails?.status, streamDetails?.hostId]);

  if (!streamDetails) {
    return <div className="p-4 text-center text-xl">Loading stream details...</div>;
  }
  if (streamDetails.status !== "live" && !error) {
    return <div className="p-4 text-center text-xl">Stream is not live (Status: {streamDetails.status}).</div>;
  }
  if (error) {
    return <div className="text-red-500 p-4 text-center text-xl bg-red-100 border border-red-500 rounded-md">{error}</div>;
  }
  if (!token && streamDetails.status === "live") {
    return <div className="p-4 text-center text-xl">Joining stream...</div>;
  }

  return (
    <div className="flex flex-col lg:flex-row gap-4 p-1 sm:p-4 bg-gray-900 text-white min-h-screen">
        <div className="lg:w-2/3 flex flex-col">
            <h1 className="text-2xl sm:text-3xl font-bold mb-1 sm:mb-2">{streamDetails.title}</h1>
            <div className="flex items-center space-x-4 mb-1">
                <p className="text-xs sm:text-sm text-gray-400">Category: {streamDetails.category || "N/A"}</p>
                {viewerCount !== null && (
                  <div className="flex items-center text-xs sm:text-sm text-gray-400">
                    <Users size={14} className="mr-1"/> {viewerCount}
                  </div>
                )}
            </div>
            <p className="text-xs sm:text-sm text-gray-400 mb-2 sm:mb-4">
                Hosted by: {streamDetails.hostName}
            </p>

            <div ref={videoContainerRef} className="w-full aspect-video bg-black rounded-md shadow-2xl mb-1 relative overflow-hidden group">
                <video ref={videoRef} autoPlay playsInline className="w-full h-full object-contain"></video>
                <audio ref={audioRef} autoPlay playsInline className="hidden"></audio> 
                {isConnected && streamDetails.status === "live" && (
                  <div className="absolute bottom-0 left-0 right-0 p-2 bg-black/30 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <button onClick={toggleMute} className="text-white p-1.5 hover:bg-white/20 rounded-full">{isMuted ? <VolumeX size={20} /> : <Volume2 size={20} />}</button>
                      <input type="range" min="0" max="1" step="0.05" value={isMuted ? 0 : volume} onChange={handleVolumeChange} className="w-20 h-1.5 accent-primary cursor-pointer"/>
                    </div>
                    <button onClick={toggleFullscreen} className="text-white p-1.5 hover:bg-white/20 rounded-full">{isFullscreen ? <Minimize size={20} /> : <Maximize size={20} />}</button>
                  </div>
                )}
                {!isConnected && !error && streamDetails.status === "live" && (
                    <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-50">
                        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-white"></div>
                        <p className="ml-3 text-lg">Connecting...</p>
                    </div>
                )}
                 {isConnected && videoRef.current && !videoRef.current.srcObject && !error && streamDetails.status === "live" && (
                    <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-75">
                        <p className="text-xl text-gray-300">Waiting for host to start video...</p>
                    </div>
                )}
            </div>

            {isConnected && streamDetails.status === "live" ? ( <p className="text-green-400 mb-4">Connected to stream</p> ) : ( !error && streamDetails.status === "live" && <p className="text-yellow-400 mb-4">Attempting to connect...</p> )}
            
            {streamDetails.productId && streamDetails.productName && (
                <div className="mt-4 p-4 bg-gray-800 rounded-lg shadow max-w-md w-full">
                <h3 className="text-md sm:text-lg font-semibold text-primary">Currently Showcasing:</h3>
                <p className="text-lg sm:text-xl">{streamDetails.productName}</p>
                </div>
            )}
        </div>
        {streamDetails.status === "live" && (<div className="lg:w-1/3 lg:max-h-[calc(100vh-6rem)] flex-shrink-0"><ChatBox streamId={streamId} /></div>)}
    </div>
  );
}
