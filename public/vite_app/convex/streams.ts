import { v } from "convex/values";
import { mutation, query } from "./_generated/server";
import { getAuthUserId } from "@convex-dev/auth/server";
import { Doc, Id } from "./_generated/dataModel";
// api import removed as runAction is not used here

export const streamCategories = ["Gaming", "Music", "Art", "Talk Show", "Education", "DIY", "Other"] as const;
export type StreamCategory = typeof streamCategories[number];

export const createStream = mutation({
  args: {
    title: v.string(),
    category: v.string(), 
    productId: v.optional(v.id("products")),
  },
  handler: async (ctx, args): Promise<Id<"streams">> => {
    const userId = await getAuthUserId(ctx);
    if (!userId) throw new Error("User must be logged in to create a stream.");
    if (!streamCategories.includes(args.category as StreamCategory)) throw new Error("Invalid category selected.");

    const tempRoomName = `stream-placeholder-${Date.now()}`;
    const streamId = await ctx.db.insert("streams", {
      hostId: userId,
      title: args.title,
      roomName: tempRoomName,
      category: args.category,
      status: "pending", 
      isActive: false,   
      productId: args.productId,
      moderatorIds: [],
      blockedUserIds: [],
    });
    await ctx.db.patch(streamId, { roomName: `stream-${streamId}` });
    return streamId;
  },
});

export const goLive = mutation({
  args: { streamId: v.id("streams") },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) throw new Error("User must be logged in.");

    const stream = await ctx.db.get(args.streamId);
    if (!stream) throw new Error("Stream not found.");
    if (stream.hostId !== userId) throw new Error("Only the host can start the stream.");
    if (stream.status !== "pending") throw new Error("Stream is not pending and cannot be started.");

    await ctx.db.patch(args.streamId, { status: "live", isActive: true });
    return null;
  },
});

export const endStream = mutation({
  args: { streamId: v.id("streams") },
  handler: async (ctx, args): Promise<null> => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("User must be logged in.");
    }

    const stream = await ctx.db.get(args.streamId);
    if (!stream) {
      throw new Error("Stream not found.");
    }

    if (stream.hostId !== userId) {
      throw new Error("Only the host can end the stream.");
    }

    await ctx.db.patch(args.streamId, { status: "ended", isActive: false });
    return null;
  },
});

// StreamWithDetails type no longer includes viewerCount from backend directly
export type StreamWithDetails = Doc<"streams"> & {
  hostName: string;
  productName?: string | null;
};

export const listActiveStreams = query({
  args: { category: v.optional(v.string()) },
  handler: async (ctx, args): Promise<StreamWithDetails[]> => {
    let streamsQuery;
    if (args.category && args.category !== "All") {
        streamsQuery = ctx.db.query("streams")
            .withIndex("by_category_and_status", q => q.eq("category", args.category!).eq("status", "live"))
            .order("desc");
    } else {
        streamsQuery = ctx.db.query("streams")
            .withIndex("by_status", q => q.eq("status", "live"))
            .order("desc");
    }
    const streams = await streamsQuery.collect();

    const streamsWithDetails: StreamWithDetails[] = await Promise.all(
      streams.map(async (stream) => {
        const host = await ctx.db.get(stream.hostId);
        let product: Doc<"products"> | null = null;
        if (stream.productId) {
          product = await ctx.db.get(stream.productId);
        }
        return {
          ...stream,
          hostName: host?.name ?? host?.email ?? "Unknown Host",
          productName: product?.name,
        };
      })
    );
    return streamsWithDetails;
  },
});

export const getStream = query({
  args: { streamId: v.id("streams") },
  handler: async (ctx, args): Promise<StreamWithDetails | null> => {
    const stream = await ctx.db.get(args.streamId);
    if (!stream) return null;

    const host = await ctx.db.get(stream.hostId);
    let product: Doc<"products"> | null = null;
    if (stream.productId) {
      product = await ctx.db.get(stream.productId);
    }
    return {
      ...stream,
      hostName: host?.name ?? host?.email ?? "Unknown Host",
      productName: product?.name,
    };
  },
});

export const getMyPendingStreams = query({
    args: {},
    handler: async (ctx) => {
        const userId = await getAuthUserId(ctx);
        if (!userId) return [];

        const streams = await ctx.db.query("streams")
            .withIndex("by_hostId", q => q.eq("hostId", userId))
            .filter(q => q.eq(q.field("status"), "pending"))
            .order("desc")
            .collect();
        
        return streams;
    }
});
