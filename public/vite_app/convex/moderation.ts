import { v } from "convex/values";
import { mutation, query } from "./_generated/server";
import { getAuthUserId } from "@convex-dev/auth/server";
import { Id } from "./_generated/dataModel";

// Helper to check if a user is host or moderator
async function isHostOrModerator(ctx: any, streamId: Id<"streams">, userId: Id<"users">) {
    const stream = await ctx.db.get(streamId);
    if (!stream) throw new Error("Stream not found");
    return stream.hostId === userId || (stream.moderatorIds?.includes(userId));
}

export const addModerator = mutation({
    args: { streamId: v.id("streams"), userIdToAdd: v.id("users") },
    handler: async (ctx, args) => {
        const currentUserId = await getAuthUserId(ctx);
        if (!currentUserId) throw new Error("Authentication required.");

        const stream = await ctx.db.get(args.streamId);
        if (!stream) throw new Error("Stream not found.");
        if (stream.hostId !== currentUserId) throw new Error("Only the host can add moderators.");
        if (args.userIdToAdd === stream.hostId) throw new Error("Host cannot be added as a moderator.");

        const currentModerators = stream.moderatorIds || [];
        if (currentModerators.includes(args.userIdToAdd)) {
            return; 
        }
        await ctx.db.patch(args.streamId, { moderatorIds: [...currentModerators, args.userIdToAdd] });
        return null;
    }
});

export const removeModerator = mutation({
    args: { streamId: v.id("streams"), userIdToRemove: v.id("users") },
    handler: async (ctx, args) => {
        const currentUserId = await getAuthUserId(ctx);
        if (!currentUserId) throw new Error("Authentication required.");

        const stream = await ctx.db.get(args.streamId);
        if (!stream) throw new Error("Stream not found.");
        if (stream.hostId !== currentUserId) throw new Error("Only the host can remove moderators.");

        const currentModerators = stream.moderatorIds || [];
        await ctx.db.patch(args.streamId, { moderatorIds: currentModerators.filter(id => id !== args.userIdToRemove) });
        return null;
    }
});

export const blockUserFromChat = mutation({
    args: { streamId: v.id("streams"), userIdToBlock: v.id("users") },
    handler: async (ctx, args) => {
        const currentUserId = await getAuthUserId(ctx);
        if (!currentUserId) throw new Error("Authentication required.");
        if (!(await isHostOrModerator(ctx, args.streamId, currentUserId))) {
            throw new Error("Only host or moderators can block users.");
        }
        
        const stream = await ctx.db.get(args.streamId);
        if(!stream) throw new Error("Stream not found");
        if(args.userIdToBlock === stream.hostId) throw new Error("Cannot block the host.");
        if(stream.moderatorIds?.includes(args.userIdToBlock)) throw new Error("Cannot block a moderator. Remove moderator status first.");


        const currentBlocked = stream.blockedUserIds || [];
        if (currentBlocked.includes(args.userIdToBlock)) {
            return; 
        }
        await ctx.db.patch(args.streamId, { blockedUserIds: [...currentBlocked, args.userIdToBlock] });
        return null;
    }
});

export const unblockUserFromChat = mutation({
    args: { streamId: v.id("streams"), userIdToUnblock: v.id("users") },
    handler: async (ctx, args) => {
        const currentUserId = await getAuthUserId(ctx);
        if (!currentUserId) throw new Error("Authentication required.");
         if (!(await isHostOrModerator(ctx, args.streamId, currentUserId))) {
            throw new Error("Only host or moderators can unblock users.");
        }
        const stream = await ctx.db.get(args.streamId);
        if(!stream) throw new Error("Stream not found");

        const currentBlocked = stream.blockedUserIds || [];
        await ctx.db.patch(args.streamId, { blockedUserIds: currentBlocked.filter(id => id !== args.userIdToUnblock) });
        return null;
    }
});

export const getStreamModerationData = query({
    args: { streamId: v.id("streams") },
    handler: async (ctx, args) => {
        const stream = await ctx.db.get(args.streamId);
        if (!stream) return null;
        
        const currentUserId = await getAuthUserId(ctx);
        const isCurrentUserHost = stream.hostId === currentUserId;
        const isCurrentUserMod = stream.moderatorIds?.includes(currentUserId!) ?? false;

        const moderators = stream.moderatorIds ? await Promise.all(stream.moderatorIds.map(id => ctx.db.get(id))) : [];
        const blockedUsers = stream.blockedUserIds ? await Promise.all(stream.blockedUserIds.map(id => ctx.db.get(id))) : [];

        return {
            hostId: stream.hostId,
            moderatorIds: stream.moderatorIds || [],
            blockedUserIds: stream.blockedUserIds || [],
            moderators: moderators.filter(Boolean).map(u => ({_id: u!._id, name: u!.name ?? u!.email ?? u!._id.toString()})), 
            blockedUsers: blockedUsers.filter(Boolean).map(u => ({_id: u!._id, name: u!.name ?? u!.email ?? u!._id.toString()})),
            isCurrentUserHost,
            isCurrentUserMod,
        };
    }
});
