import { defineSchema, defineTable } from "convex/server";
import { v } from "convex/values";
import { authTables } from "@convex-dev/auth/server";

const applicationTables = {
  products: defineTable({
    name: v.string(),
    description: v.string(),
    price: v.number(),
    sellerId: v.id("users"),
    imageId: v.optional(v.id("_storage")),
    status: v.string(), // "available", "sold"
  })
    .index("by_sellerId_and_status", ["sellerId", "status"])
    .index("by_status", ["status"]),

  streams: defineTable({
    hostId: v.id("users"),
    title: v.string(),
    roomName: v.string(), 
    category: v.optional(v.string()), 
    status: v.optional(v.string()), 
    isActive: v.boolean(), 
    productId: v.optional(v.id("products")), 
    scheduledTime: v.optional(v.number()), 
    moderatorIds: v.optional(v.array(v.id("users"))), // New
    blockedUserIds: v.optional(v.array(v.id("users"))), // New
  })
    .index("by_hostId", ["hostId"])
    .index("by_status", ["status"]) 
    .index("by_isActive", ["isActive"]) 
    .index("by_roomName", ["roomName"])
    .index("by_category_and_status", ["category", "status"]),

  chatMessages: defineTable({
    streamId: v.id("streams"),
    userId: v.id("users"),
    userName: v.string(),
    text: v.string(),
  }).index("by_streamId", ["streamId"]),
};

export default defineSchema({
  ...authTables,
  ...applicationTables,
});
