{"$schema": "https://turbo.build/schema.json", "globalDependencies": ["**/.env.*local"], "tasks": {"build": {"dependsOn": ["^build"], "outputs": [".next/**", "!.next/cache/**"], "env": ["NODE_ENV", "NEXT_PUBLIC_CONVEX_URL", "AUTH_GOOGLE_ID", "AUTH_GOOGLE_SECRET", "AUTH_GOOGLE_REDIRECT_URI", "AUTH_RESEND_KEY", "JWKS", "JWT_PRIVATE_KEY", "SITE_URL", "STRIPE_SECRET_KEY", "STRIPE_WEBHOOK_ENDPOINT", "STRIPE_WEBHOOK_SECRET", "STRIPE_PUBLISH_KEY", "CONVEX_DEPLOYMENT", "CONVEX_DEPLOY_KEY", "ENABLE_EXPERIMENTAL_COREPACK", "LOOPS_FORM_ID", "POLAR_ORGANIZATION_TOKEN", "POLAR_WEBHOOK_SECRET", "RESEND_API_KEY", "VALIDATE_ENV", "OPENAI_API_KEY", "STREAM_VIDEO_API_KEY", "STREAM_VIDEO_API_SECRET", "TWILIO_ACCOUNT_SID", "TWILIO_AUTH_TOKEN", "TWILIO_PHONE_NUMBER", "TWILIO_VERIFY_SERVICE_SID", "EXPO_ACCESS_TOKEN", "EXPO_PUBLIC_LIVEKIT_API_KEY", "EXPO_PUBLIC_LIVEKIT_URL", "GOOGLE_CLIENT_ID", "GOOGLE_CLIENT_SECRET"]}, "dev": {"cache": false, "persistent": true}, "lint": {}, "clean": {"cache": false}, "mobile#build": {"dependsOn": ["^build"], "outputs": ["apps/mobile/.expo/**", "apps/mobile/android/app/build/**", "apps/mobile/ios/build/**"]}, "mobile#dev": {"cache": false, "persistent": true}, "mobile#start": {"cache": false, "persistent": true}}}