{"name": "liveciety", "version": "0.0.1", "private": true, "type": "module", "scripts": {"build": "turbo build", "dev": "turbo dev", "lint": "turbo lint", "format": "prettier --write \"**/*.{ts,tsx,md}\"", "clean": "git clean -xdf node_modules", "clean:workspaces": "turbo clean", "dev:app": "turbo dev --filter=@workspace/app", "dev:admin": "turbo dev --filter=@workspace/admin", "dev:backend": "turbo dev --filter=@workspace/backend", "dev:mobile": "turbo dev --filter=@workspace/mobile", "start:app": "turbo start --filter=@workspace/app", "start:admin": "turbo start --filter=@workspace/admin", "start:backend": "turbo start --filter=@workspace/backend", "start:mobile": "turbo expo start --filter=@workspace/mobile", "stripe:listen": "stripe listen --forward-to https://affable-partridge-134.convex.site/stripe", "stripe:trigger": "stripe trigger payment_intent.succeeded"}, "packageManager": "pnpm@10.10.0", "workspaces": ["packages/*", "apps/*"], "devDependencies": {"@emotion/is-prop-valid": "^1.3.1", "@react-native-community/cli": "^18.0.0", "@types/lodash": "^4.17.16", "@types/node": "^20.17.25", "@workspace/eslint-config": "workspace:*", "@workspace/typescript-config": "workspace:*", "convex-panel": "^0.2.14", "prettier": "^3.5.1", "turbo": "^2.4.2", "typescript": "5.7.3"}, "engines": {"node": ">=20.0.0", "pnpm": ">=10.10.0"}, "dependencies": {"@auth/core": "0.37.0", "@convex-dev/auth": "^0.0.80", "@tabler/icons-react": "^3.31.0", "chrono-node": "^2.7.9", "convex": "^1.21.0", "livekit-server-sdk": "^2.13.0", "lucide-react": "^0.483.0", "motion": "^12.5.0", "react": "^19.0.0", "react-dom": "^19.0.0", "resend": "^4.5.1", "stripe": "^17.7.0"}}